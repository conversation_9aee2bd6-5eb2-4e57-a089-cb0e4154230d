# Nilgala Micro - Technical Specifications
## Comprehensive System Architecture & Implementation Guide

### System Overview
**Application Type**: Full-Stack Web Application
**Architecture**: Monolithic with modular design (future microservices ready)
**Frontend**: Next.js 14+ with React 18+, TypeScript
**Backend**: Next.js API Routes with Prisma ORM
**Database**: PostgreSQL (Prisma hosted)
**File Storage**: Cloudflare R2
**Authentication**: JWT with Role-Based Access Control (RBAC)

---

## Technology Stack Details

### Frontend Technologies
```typescript
// Core Framework
- Next.js 14+ (App Router)
- React 18+ with TypeScript
- <PERSON><PERSON><PERSON> CSS for styling
- Shadcn/ui component library
- React Hook Form for form management
- Z<PERSON> for validation
- React Query/TanStack Query for state management
- Recharts for data visualization
- React Calendar for scheduling
```

### Backend Technologies
```typescript
// API & Database
- Next.js API Routes
- Prisma ORM with PostgreSQL
- NextAuth.js for authentication
- JWT for token management
- Cloudflare R2 SDK for file storage
- Nodemailer for email notifications
- <PERSON><PERSON><PERSON> for SMS notifications
```

### Development Tools
```typescript
// Development & Quality
- TypeScript for type safety
- ESLint + Prettier for code quality
- Husky for git hooks
- Jest + React Testing Library for testing
- Playwright for E2E testing
- Storybook for component documentation
```

---

## Database Schema Architecture

### Core Tables Structure

#### 1. Users & Authentication
```sql
-- Users table with role-based access
Users {
  id: String @id @default(cuid())
  email: String @unique
  password: String
  firstName: String
  lastName: String
  phone: String?
  avatar: String?
  isActive: Boolean @default(true)
  lastLogin: DateTime?
  createdAt: DateTime @default(now())
  updatedAt: DateTime @updatedAt
  
  // Relationships
  roles: UserRole[]
  createdLoans: Loan[]
  assignedCollections: Collection[]
  auditLogs: AuditLog[]
}

-- Roles and permissions
Role {
  id: String @id @default(cuid())
  name: String @unique // SUPER_ADMIN, HIGHER_MANAGEMENT, MANAGER, CREDIT_OFFICER, CUSTOMER_SERVICE
  description: String
  permissions: Permission[]
  users: UserRole[]
}

Permission {
  id: String @id @default(cuid())
  module: String // CUSTOMERS, LOANS, PAYMENTS, REPORTS, SETTINGS
  action: String // CREATE, READ, UPDATE, DELETE
  resource: String? // Specific resource if needed
  roles: Role[]
}
```

#### 2. Customer Management
```sql
Customer {
  id: String @id @default(cuid())
  // Personal Information
  firstName: String
  lastName: String
  dateOfBirth: DateTime
  gender: Gender
  maritalStatus: MaritalStatus
  nationalId: String @unique
  
  // Contact Information
  phone: String
  email: String?
  address: String
  city: String
  state: String
  postalCode: String
  gpsCoordinates: String?
  
  // Employment Information
  employmentType: EmploymentType
  employer: String?
  monthlyIncome: Decimal
  employmentDuration: Int? // in months
  
  // Financial Information
  bankAccount: String?
  bankName: String?
  creditScore: Int?
  existingLoans: Int @default(0)
  
  // KYC Status
  kycStatus: KYCStatus @default(PENDING)
  kycVerifiedAt: DateTime?
  kycVerifiedBy: String?
  
  // Relationships
  loans: Loan[]
  guarantorFor: LoanGuarantor[]
  documents: Document[]
  dependents: Dependent[]
  
  createdAt: DateTime @default(now())
  updatedAt: DateTime @updatedAt
}
```

#### 3. Loan Management
```sql
Loan {
  id: String @id @default(cuid())
  loanNumber: String @unique
  
  // Loan Details
  customerId: String
  loanType: LoanType
  principalAmount: Decimal
  interestRate: Decimal
  tenure: Int // in months
  repaymentFrequency: RepaymentFrequency
  gracePeriod: Int @default(0) // in days
  
  // Fees and Charges
  processingFee: Decimal @default(0)
  insuranceFee: Decimal @default(0)
  otherCharges: Decimal @default(0)
  
  // Calculated Fields
  totalAmount: Decimal
  emiAmount: Decimal
  totalInterest: Decimal
  
  // Status and Workflow
  status: LoanStatus @default(DRAFT)
  applicationDate: DateTime @default(now())
  approvalDate: DateTime?
  disbursementDate: DateTime?
  maturityDate: DateTime?
  
  // Approval Workflow
  approvalLevel: Int @default(0)
  approvedBy: String?
  rejectionReason: String?
  
  // Relationships
  customer: Customer @relation(fields: [customerId], references: [id])
  guarantors: LoanGuarantor[]
  payments: Payment[]
  documents: Document[]
  schedules: PaymentSchedule[]
  
  createdBy: String
  createdAt: DateTime @default(now())
  updatedAt: DateTime @updatedAt
}
```

#### 4. Payment & Collection System
```sql
Payment {
  id: String @id @default(cuid())
  loanId: String
  
  // Payment Details
  amount: Decimal
  paymentDate: DateTime
  paymentMethod: PaymentMethod
  referenceNumber: String?
  
  // Payment Breakdown
  principalAmount: Decimal
  interestAmount: Decimal
  penaltyAmount: Decimal @default(0)
  feeAmount: Decimal @default(0)
  
  // Status and Processing
  status: PaymentStatus @default(PENDING)
  processedBy: String?
  processedAt: DateTime?
  
  // Collection Information
  collectedBy: String?
  collectionDate: DateTime?
  receiptNumber: String?
  
  // Relationships
  loan: Loan @relation(fields: [loanId], references: [id])
  schedule: PaymentSchedule?
  
  createdAt: DateTime @default(now())
  updatedAt: DateTime @updatedAt
}

PaymentSchedule {
  id: String @id @default(cuid())
  loanId: String
  
  // Schedule Details
  installmentNumber: Int
  dueDate: DateTime
  principalAmount: Decimal
  interestAmount: Decimal
  totalAmount: Decimal
  
  // Status
  status: ScheduleStatus @default(PENDING)
  paidAmount: Decimal @default(0)
  paidDate: DateTime?
  
  // Late Payment
  daysOverdue: Int @default(0)
  penaltyAmount: Decimal @default(0)
  
  // Relationships
  loan: Loan @relation(fields: [loanId], references: [id])
  payments: Payment[]
}
```

---

## API Architecture

### Authentication Endpoints
```typescript
// Authentication routes
POST /api/auth/login
POST /api/auth/logout
POST /api/auth/refresh
POST /api/auth/forgot-password
POST /api/auth/reset-password
GET  /api/auth/me
```

### Customer Management APIs
```typescript
// Customer CRUD operations
GET    /api/customers              // List customers with pagination
POST   /api/customers              // Create new customer
GET    /api/customers/[id]         // Get customer details
PUT    /api/customers/[id]         // Update customer
DELETE /api/customers/[id]         // Soft delete customer
POST   /api/customers/[id]/kyc     // Update KYC status
GET    /api/customers/search       // Search customers
```

### Loan Management APIs
```typescript
// Loan operations
GET    /api/loans                  // List loans with filters
POST   /api/loans                  // Create new loan
GET    /api/loans/[id]             // Get loan details
PUT    /api/loans/[id]             // Update loan
POST   /api/loans/[id]/approve     // Approve loan
POST   /api/loans/[id]/reject      // Reject loan
POST   /api/loans/[id]/disburse    // Disburse loan
GET    /api/loans/[id]/schedule    // Get payment schedule
```

### Payment & Collection APIs
```typescript
// Payment operations
GET    /api/payments               // List payments
POST   /api/payments               // Record payment
GET    /api/payments/[id]          // Get payment details
PUT    /api/payments/[id]          // Update payment
GET    /api/collections/due        // Get due collections
POST   /api/collections/assign    // Assign collections
```

---

## Security Implementation

### Authentication & Authorization
```typescript
// JWT Token Structure
interface JWTPayload {
  userId: string;
  email: string;
  roles: string[];
  permissions: Permission[];
  iat: number;
  exp: number;
}

// Permission Check Middleware
export function requirePermission(module: string, action: string) {
  return async (req: NextRequest, res: NextResponse) => {
    const token = await verifyJWT(req);
    const hasPermission = checkUserPermission(token.permissions, module, action);
    
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }
    
    return NextResponse.next();
  };
}
```

### Data Validation
```typescript
// Zod schemas for validation
export const CustomerSchema = z.object({
  firstName: z.string().min(2).max(50),
  lastName: z.string().min(2).max(50),
  dateOfBirth: z.date().max(new Date()),
  nationalId: z.string().regex(/^[0-9]{9}[vVxX]$/),
  phone: z.string().regex(/^[0-9]{10}$/),
  email: z.string().email().optional(),
  monthlyIncome: z.number().positive(),
  // ... other fields
});

export const LoanSchema = z.object({
  customerId: z.string().cuid(),
  loanType: z.enum(['PERSONAL', 'BUSINESS', 'AGRICULTURAL']),
  principalAmount: z.number().positive().max(1000000),
  interestRate: z.number().positive().max(50),
  tenure: z.number().int().positive().max(120),
  // ... other fields
});
```

---

## File Storage Integration

### Cloudflare R2 Configuration
```typescript
// R2 client setup
import { S3Client } from '@aws-sdk/client-s3';

export const r2Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY!,
  },
});

// File upload utility
export async function uploadDocument(
  file: File,
  customerId: string,
  documentType: string
): Promise<string> {
  const key = `customers/${customerId}/${documentType}/${Date.now()}-${file.name}`;
  
  await r2Client.send(new PutObjectCommand({
    Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
    Key: key,
    Body: await file.arrayBuffer(),
    ContentType: file.type,
  }));
  
  return key;
}
```

---

## Performance Optimization

### Database Optimization
```sql
-- Essential indexes for performance
CREATE INDEX idx_customers_national_id ON Customer(nationalId);
CREATE INDEX idx_customers_phone ON Customer(phone);
CREATE INDEX idx_loans_customer_id ON Loan(customerId);
CREATE INDEX idx_loans_status ON Loan(status);
CREATE INDEX idx_payments_loan_id ON Payment(loanId);
CREATE INDEX idx_payment_schedule_due_date ON PaymentSchedule(dueDate);
CREATE INDEX idx_payment_schedule_status ON PaymentSchedule(status);
```

### Caching Strategy
```typescript
// Redis caching for frequently accessed data
import Redis from 'ioredis';

const redis = new Redis(process.env.REDIS_URL);

export async function getCachedCustomer(customerId: string) {
  const cached = await redis.get(`customer:${customerId}`);
  if (cached) return JSON.parse(cached);
  
  const customer = await prisma.customer.findUnique({
    where: { id: customerId },
    include: { loans: true, documents: true }
  });
  
  await redis.setex(`customer:${customerId}`, 300, JSON.stringify(customer));
  return customer;
}
```

---

## Monitoring & Logging

### Audit Trail Implementation
```typescript
// Audit log for all critical operations
export async function createAuditLog(
  userId: string,
  action: string,
  resource: string,
  resourceId: string,
  changes?: any
) {
  await prisma.auditLog.create({
    data: {
      userId,
      action,
      resource,
      resourceId,
      changes: changes ? JSON.stringify(changes) : null,
      ipAddress: getClientIP(),
      userAgent: getUserAgent(),
      timestamp: new Date(),
    },
  });
}
```

### Error Handling
```typescript
// Global error handler
export class AppError extends Error {
  statusCode: number;
  isOperational: boolean;

  constructor(message: string, statusCode: number) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;
  }
}

export function handleError(error: Error, req: NextRequest) {
  // Log error
  console.error('Error:', error);
  
  // Send to monitoring service
  if (process.env.NODE_ENV === 'production') {
    // Send to Sentry or similar service
  }
  
  // Return appropriate response
  if (error instanceof AppError) {
    return NextResponse.json(
      { error: error.message },
      { status: error.statusCode }
    );
  }
  
  return NextResponse.json(
    { error: 'Internal server error' },
    { status: 500 }
  );
}
```

---

## Deployment Configuration

### Environment Variables
```bash
# Database
DATABASE_URL="postgres://..."
DIRECT_URL="postgres://..."

# Authentication
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="https://your-domain.com"
JWT_SECRET="your-jwt-secret"

# Cloudflare R2
CLOUDFLARE_R2_ENDPOINT="https://..."
CLOUDFLARE_R2_ACCESS_KEY_ID="..."
CLOUDFLARE_R2_SECRET_ACCESS_KEY="..."
CLOUDFLARE_R2_BUCKET_NAME="nilgala-micro-docs"

# Notifications
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="..."
SMTP_PASS="..."
TWILIO_ACCOUNT_SID="..."
TWILIO_AUTH_TOKEN="..."

# Redis (for caching)
REDIS_URL="redis://..."
```

### Docker Configuration
```dockerfile
FROM node:18-alpine AS base

# Install dependencies
FROM base AS deps
WORKDIR /app
COPY package.json package-lock.json ./
RUN npm ci

# Build application
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN npm run build

# Production image
FROM base AS runner
WORKDIR /app
ENV NODE_ENV production
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs
EXPOSE 3000
ENV PORT 3000

CMD ["node", "server.js"]
```

---

## Testing Strategy

### Unit Testing
```typescript
// Example test for loan calculation
import { calculateEMI, generatePaymentSchedule } from '@/lib/loan-calculations';

describe('Loan Calculations', () => {
  test('should calculate EMI correctly', () => {
    const emi = calculateEMI(100000, 12, 24); // 100k, 12% annual, 24 months
    expect(emi).toBeCloseTo(4707.35, 2);
  });

  test('should generate payment schedule', () => {
    const schedule = generatePaymentSchedule({
      principalAmount: 100000,
      interestRate: 12,
      tenure: 12,
      startDate: new Date('2024-01-01'),
    });
    
    expect(schedule).toHaveLength(12);
    expect(schedule[0].dueDate).toEqual(new Date('2024-02-01'));
  });
});
```

### Integration Testing
```typescript
// API endpoint testing
import { createMocks } from 'node-mocks-http';
import handler from '@/pages/api/loans';

describe('/api/loans', () => {
  test('should create loan with valid data', async () => {
    const { req, res } = createMocks({
      method: 'POST',
      body: {
        customerId: 'customer-id',
        loanType: 'PERSONAL',
        principalAmount: 50000,
        interestRate: 15,
        tenure: 12,
      },
    });

    await handler(req, res);
    expect(res._getStatusCode()).toBe(201);
  });
});
```

---

**Next Steps**: 
1. Initialize the Next.js project with the specified technology stack
2. Set up the database schema using Prisma
3. Implement the authentication and authorization system
4. Begin development of core modules following the technical specifications

This technical specification provides a comprehensive foundation for building the Nilgala Micro loan management system with industry best practices and scalable architecture.
