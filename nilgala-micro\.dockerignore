# Dependencies
node_modules
npm-debug.log*

# Next.js build output
.next
out

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# IDE files
.vscode
.idea

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Documentation
README.md
docs/

# Test files
__tests__
*.test.js
*.test.ts
*.spec.js
*.spec.ts
