module.exports = {

"[project]/.next-internal/server/app/api/payments/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "prisma": ()=>prisma
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "authOptions": ()=>authOptions,
    "checkPermissions": ()=>checkPermissions,
    "checkPermissionsSync": ()=>checkPermissionsSync,
    "clearPermissionsCache": ()=>clearPermissionsCache,
    "getRolePermissions": ()=>getRolePermissions,
    "hasPermission": ()=>hasPermission,
    "hasPermissionSync": ()=>hasPermissionSync,
    "initializePermissionsCache": ()=>initializePermissionsCache
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$auth$2f$prisma$2d$adapter$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@auth/prisma-adapter/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
;
;
;
;
// Cache for role permissions to avoid database calls on every request
let rolePermissionsCache = {};
let cacheLastUpdated = 0;
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes
;
const authOptions = {
    adapter: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$auth$2f$prisma$2d$adapter$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PrismaAdapter"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"]),
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            name: 'credentials',
            credentials: {
                email: {
                    label: 'Email',
                    type: 'email'
                },
                password: {
                    label: 'Password',
                    type: 'password'
                }
            },
            async authorize (credentials) {
                if (!credentials?.email || !credentials?.password) {
                    return null;
                }
                const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
                    where: {
                        email: credentials.email
                    }
                });
                if (!user || !user.isActive) {
                    return null;
                }
                const isPasswordValid = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(credentials.password, user.password);
                if (!isPasswordValid) {
                    return null;
                }
                // Update last login
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.update({
                    where: {
                        id: user.id
                    },
                    data: {
                        lastLogin: new Date()
                    }
                });
                return {
                    id: user.id,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    role: user.role,
                    avatar: user.avatar || undefined
                };
            }
        })
    ],
    session: {
        strategy: 'jwt'
    },
    callbacks: {
        async jwt ({ token, user }) {
            if (user) {
                token.role = user.role;
                token.firstName = user.firstName;
                token.lastName = user.lastName;
            }
            return token;
        },
        async session ({ session, token }) {
            if (token) {
                session.user.id = token.sub;
                session.user.role = token.role;
                session.user.firstName = token.firstName;
                session.user.lastName = token.lastName;
            }
            return session;
        }
    },
    pages: {
        signIn: '/auth/signin',
        error: '/auth/error'
    }
};
// Load role permissions from database with caching
async function loadRolePermissions() {
    const now = Date.now();
    // Return cached permissions if still valid
    if (cacheLastUpdated && now - cacheLastUpdated < CACHE_DURATION) {
        return rolePermissionsCache;
    }
    try {
        // Fetch all active role permissions from database
        const permissions = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].rolePermission.findMany({
            where: {
                isActive: true
            },
            select: {
                role: true,
                permission: true
            }
        });
        // Group permissions by role
        const rolePermissions = {};
        for (const perm of permissions){
            if (!rolePermissions[perm.role]) {
                rolePermissions[perm.role] = [];
            }
            rolePermissions[perm.role].push(perm.permission);
        }
        // Update cache
        rolePermissionsCache = rolePermissions;
        cacheLastUpdated = now;
        return rolePermissions;
    } catch (error) {
        console.error('Error loading role permissions from database:', error);
        // Return empty permissions on error to be safe
        return {};
    }
}
async function getRolePermissions() {
    return await loadRolePermissions();
}
function clearPermissionsCache() {
    rolePermissionsCache = {};
    cacheLastUpdated = 0;
}
async function initializePermissionsCache() {
    try {
        await loadRolePermissions();
        console.log('✅ Permissions cache initialized');
    } catch (error) {
        console.error('❌ Failed to initialize permissions cache:', error);
    }
}
async function hasPermission(userRole, permission) {
    const rolePermissions = await getRolePermissions();
    return rolePermissions[userRole]?.includes(permission) || false;
}
async function checkPermissions(userRole, requiredPermissions) {
    const rolePermissions = await getRolePermissions();
    return requiredPermissions.every((permission)=>rolePermissions[userRole]?.includes(permission) || false);
}
function hasPermissionSync(userRole, permission) {
    return rolePermissionsCache[userRole]?.includes(permission) || false;
}
function checkPermissionsSync(userRole, requiredPermissions) {
    return requiredPermissions.every((permission)=>hasPermissionSync(userRole, permission));
}
}),
"[project]/src/lib/audit-logger.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AuditLogger": ()=>AuditLogger,
    "auditLog": ()=>auditLog,
    "withAuditLogging": ()=>withAuditLogging
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
class AuditLogger {
    /**
   * Create an audit log entry
   */ static async log(data) {
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].auditLog.create({
                data: {
                    userId: data.userId,
                    action: data.action,
                    resource: data.resource,
                    resourceId: data.resourceId,
                    oldValues: data.oldValues,
                    newValues: data.newValues,
                    ipAddress: data.ipAddress,
                    userAgent: data.userAgent,
                    timestamp: new Date()
                }
            });
        } catch (error) {
            console.error('Failed to create audit log:', error);
        // Don't throw error to avoid breaking the main operation
        }
    }
    /**
   * Extract audit context from request
   */ static getContextFromRequest(request, userId) {
        return {
            userId,
            ipAddress: this.getClientIP(request),
            userAgent: request.headers.get('user-agent') || undefined
        };
    }
    /**
   * Get client IP address from request
   */ static getClientIP(request) {
        // Check various headers for the real IP
        const forwarded = request.headers.get('x-forwarded-for');
        const realIP = request.headers.get('x-real-ip');
        const remoteAddr = request.headers.get('x-remote-addr');
        if (forwarded) {
            return forwarded.split(',')[0].trim();
        }
        return realIP || remoteAddr || undefined;
    }
    /**
   * Log user authentication events
   */ static async logAuth(context, action, metadata) {
        await this.log({
            userId: context.userId,
            action,
            resource: 'User',
            resourceId: context.userId,
            newValues: {
                timestamp: new Date(),
                ...metadata
            },
            ipAddress: context.ipAddress,
            userAgent: context.userAgent
        });
    }
    /**
   * Log CRUD operations
   */ static async logCRUD(context, action, resource, resourceId, oldValues, newValues) {
        await this.log({
            userId: context.userId,
            action,
            resource,
            resourceId,
            oldValues,
            newValues,
            ipAddress: context.ipAddress,
            userAgent: context.userAgent
        });
    }
    /**
   * Log loan-specific operations
   */ static async logLoanOperation(context, action, loanId, oldValues, newValues) {
        await this.log({
            userId: context.userId,
            action,
            resource: 'Loan',
            resourceId: loanId,
            oldValues,
            newValues,
            ipAddress: context.ipAddress,
            userAgent: context.userAgent
        });
    }
    /**
   * Log payment operations
   */ static async logPaymentOperation(context, action, paymentId, oldValues, newValues) {
        await this.log({
            userId: context.userId,
            action,
            resource: 'Payment',
            resourceId: paymentId,
            oldValues,
            newValues,
            ipAddress: context.ipAddress,
            userAgent: context.userAgent
        });
    }
    /**
   * Log permission changes
   */ static async logPermissionChange(context, role, oldPermissions, newPermissions) {
        await this.log({
            userId: context.userId,
            action: 'PERMISSIONS_UPDATED',
            resource: 'RolePermission',
            resourceId: role,
            oldValues: {
                permissions: oldPermissions
            },
            newValues: {
                permissions: newPermissions
            },
            ipAddress: context.ipAddress,
            userAgent: context.userAgent
        });
    }
    /**
   * Log system configuration changes
   */ static async logSystemConfigChange(context, configKey, oldValue, newValue) {
        await this.log({
            userId: context.userId,
            action: 'CONFIG_UPDATE',
            resource: 'SystemConfig',
            resourceId: configKey,
            oldValues: {
                value: oldValue
            },
            newValues: {
                value: newValue
            },
            ipAddress: context.ipAddress,
            userAgent: context.userAgent
        });
    }
    /**
   * Log bulk operations
   */ static async logBulkOperation(context, action, resource, affectedCount, metadata) {
        await this.log({
            userId: context.userId,
            action: `BULK_${action}`,
            resource,
            newValues: {
                affectedCount,
                ...metadata
            },
            ipAddress: context.ipAddress,
            userAgent: context.userAgent
        });
    }
    /**
   * Log security events
   */ static async logSecurityEvent(context, event, resource, details) {
        await this.log({
            userId: context.userId,
            action: `SECURITY_${event}`,
            resource,
            newValues: {
                event,
                details,
                timestamp: new Date()
            },
            ipAddress: context.ipAddress,
            userAgent: context.userAgent
        });
    }
}
async function auditLog(data) {
    await AuditLogger.log(data);
}
function withAuditLogging(fn, getAuditData) {
    return async (...args)=>{
        const result = await fn(...args);
        try {
            const auditData = getAuditData(...args);
            await AuditLogger.log(auditData);
        } catch (error) {
            console.error('Audit logging failed:', error);
        }
        return result;
    };
}
}),
"[project]/src/app/api/payments/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>GET,
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$audit$2d$logger$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/audit-logger.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-route] (ecmascript) <export * as z>");
;
;
;
;
;
;
;
// Validation schema for payment creation
const createPaymentSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    loanId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, 'Loan ID is required'),
    amount: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive('Amount must be positive'),
    paymentMethod: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
        'CASH',
        'BANK_TRANSFER',
        'CHEQUE',
        'ONLINE',
        'MOBILE_PAYMENT'
    ]),
    referenceNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    notes: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    paymentDate: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().transform((str)=>new Date(str)).optional()
});
async function GET(request) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["hasPermissionSync"])(session.user.role, 'payments:read')) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized'
            }, {
                status: 401
            });
        }
        const { searchParams } = new URL(request.url);
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '10');
        const search = searchParams.get('search') || '';
        const loanId = searchParams.get('loanId') || '';
        const paymentMethod = searchParams.get('paymentMethod') || '';
        const startDate = searchParams.get('startDate');
        const endDate = searchParams.get('endDate');
        const skip = (page - 1) * limit;
        // Build where clause
        const where = {};
        if (search) {
            where.OR = [
                {
                    referenceNumber: {
                        contains: search,
                        mode: 'insensitive'
                    }
                },
                {
                    loan: {
                        loanId: {
                            contains: search,
                            mode: 'insensitive'
                        }
                    }
                },
                {
                    loan: {
                        customer: {
                            OR: [
                                {
                                    firstName: {
                                        contains: search,
                                        mode: 'insensitive'
                                    }
                                },
                                {
                                    lastName: {
                                        contains: search,
                                        mode: 'insensitive'
                                    }
                                },
                                {
                                    nationalId: {
                                        contains: search,
                                        mode: 'insensitive'
                                    }
                                }
                            ]
                        }
                    }
                }
            ];
        }
        if (loanId) {
            where.loanId = loanId;
        }
        if (paymentMethod) {
            where.paymentMethod = paymentMethod;
        }
        if (startDate && endDate) {
            where.paymentDate = {
                gte: new Date(startDate),
                lte: new Date(endDate)
            };
        }
        const [payments, total] = await Promise.all([
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].payment.findMany({
                where,
                include: {
                    loan: {
                        select: {
                            id: true,
                            loanNumber: true,
                            customer: {
                                select: {
                                    id: true,
                                    nationalId: true,
                                    firstName: true,
                                    lastName: true,
                                    phone: true
                                }
                            },
                            loanType: {
                                select: {
                                    name: true
                                }
                            }
                        }
                    },
                    createdByUser: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            role: true
                        }
                    }
                },
                orderBy: {
                    paymentDate: 'desc'
                },
                skip,
                take: limit
            }),
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].payment.count({
                where
            })
        ]);
        const pages = Math.ceil(total / limit);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            payments,
            pagination: {
                page,
                limit,
                total,
                pages
            }
        });
    } catch (error) {
        console.error('Error fetching payments:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["hasPermissionSync"])(session.user.role, 'payments:create')) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized'
            }, {
                status: 401
            });
        }
        const body = await request.json();
        const validatedData = createPaymentSchema.parse(body);
        // Check if loan exists and is active
        const loan = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].loan.findUnique({
            where: {
                id: validatedData.loanId
            },
            include: {
                customer: {
                    select: {
                        id: true,
                        nationalId: true,
                        firstName: true,
                        lastName: true
                    }
                }
            }
        });
        if (!loan) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Loan not found'
            }, {
                status: 404
            });
        }
        if (![
            'ACTIVE',
            'DISBURSED',
            'OVERDUE'
        ].includes(loan.status)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Payments can only be made for active, disbursed, or overdue loans'
            }, {
                status: 400
            });
        }
        // Calculate outstanding amount
        const totalPaid = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].payment.aggregate({
            where: {
                loanId: loan.id
            },
            _sum: {
                amount: true
            }
        });
        const outstandingAmount = loan.disbursedAmount - (totalPaid._sum.amount || 0);
        if (validatedData.amount > outstandingAmount) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: `Payment amount (${validatedData.amount}) exceeds outstanding amount (${outstandingAmount})`
            }, {
                status: 400
            });
        }
        const payment = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
            // Create the payment
            const newPayment = await tx.payment.create({
                data: {
                    loanId: validatedData.loanId,
                    amount: validatedData.amount,
                    paymentMethod: validatedData.paymentMethod,
                    referenceNumber: validatedData.referenceNumber,
                    notes: validatedData.notes,
                    paymentDate: validatedData.paymentDate || new Date(),
                    principalAmount: validatedData.amount,
                    interestAmount: 0,
                    penaltyAmount: 0,
                    feeAmount: 0,
                    status: 'COMPLETED',
                    processedBy: session.user.id,
                    processedAt: new Date(),
                    collectedBy: session.user.id,
                    collectionDate: new Date(),
                    createdBy: session.user.id
                }
            });
            // Update payment schedules
            await updatePaymentSchedules(tx, validatedData.loanId, validatedData.amount);
            // Check if loan is fully paid by verifying all payment schedules are completed
            const pendingSchedules = await tx.paymentSchedule.count({
                where: {
                    loanId: validatedData.loanId,
                    status: {
                        in: [
                            'PENDING',
                            'PARTIAL'
                        ]
                    }
                }
            });
            // Only mark loan as completed if all schedules are paid
            if (pendingSchedules === 0) {
                await tx.loan.update({
                    where: {
                        id: loan.id
                    },
                    data: {
                        status: 'COMPLETED'
                    }
                });
            } else {
                // If loan was previously completed but now has pending schedules, mark as active
                if (loan.status === 'COMPLETED') {
                    await tx.loan.update({
                        where: {
                            id: loan.id
                        },
                        data: {
                            status: 'ACTIVE'
                        }
                    });
                }
            }
            return newPayment;
        });
        // Create enhanced audit log
        const auditContext = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$audit$2d$logger$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AuditLogger"].getContextFromRequest(request, session.user.id);
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$audit$2d$logger$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AuditLogger"].logPaymentOperation(auditContext, 'RECORD_PAYMENT', payment.id, undefined, {
            amount: validatedData.amount,
            paymentMethod: validatedData.paymentMethod,
            loanId: validatedData.loanId,
            loanNumber: loan.loanNumber,
            customerName: `${loan.customer.firstName} ${loan.customer.lastName}`,
            referenceNumber: validatedData.referenceNumber,
            paymentDate: validatedData.paymentDate || new Date()
        });
        // Fetch complete payment with relations
        const completePayment = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].payment.findUnique({
            where: {
                id: payment.id
            },
            include: {
                loan: {
                    include: {
                        customer: {
                            select: {
                                id: true,
                                nationalId: true,
                                firstName: true,
                                lastName: true
                            }
                        }
                    }
                }
            }
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(completePayment, {
            status: 201
        });
    } catch (error) {
        if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].ZodError) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Validation error',
                details: error.errors
            }, {
                status: 400
            });
        }
        console.error('Error creating payment:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
// Helper function to update payment schedules
async function updatePaymentSchedules(tx, loanId, paymentAmount) {
    // Get all payment schedules (including partial ones) ordered by due date
    const schedules = await tx.paymentSchedule.findMany({
        where: {
            loanId,
            status: {
                in: [
                    'PENDING',
                    'PARTIAL'
                ]
            }
        },
        orderBy: {
            dueDate: 'asc'
        }
    });
    let remainingAmount = paymentAmount;
    for (const schedule of schedules){
        if (remainingAmount <= 0) break;
        const currentPaid = schedule.paidAmount || 0;
        const amountNeeded = schedule.totalAmount - currentPaid;
        if (remainingAmount >= amountNeeded) {
            // Full payment for this schedule (complete it)
            await tx.paymentSchedule.update({
                where: {
                    id: schedule.id
                },
                data: {
                    status: 'PAID',
                    paidAmount: schedule.totalAmount,
                    paidDate: new Date()
                }
            });
            remainingAmount -= amountNeeded;
        } else {
            // Partial payment - add to existing paid amount
            await tx.paymentSchedule.update({
                where: {
                    id: schedule.id
                },
                data: {
                    paidAmount: currentPaid + remainingAmount,
                    status: 'PARTIAL'
                }
            });
            remainingAmount = 0;
        }
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__d5ee8772._.js.map