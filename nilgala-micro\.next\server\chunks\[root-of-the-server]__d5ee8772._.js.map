{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Cred<PERSON><PERSON><PERSON>rovider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\nimport { UserRole } from '@prisma/client'\n\n// Cache for role permissions to avoid database calls on every request\nlet rolePermissionsCache: Record<string, string[]> = {}\nlet cacheLastUpdated = 0\nconst CACHE_DURATION = 5 * 60 * 1000 // 5 minutes\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma) as any,\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user || !user.isActive) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        // Update last login\n        await prisma.user.update({\n          where: { id: user.id },\n          data: { lastLogin: new Date() }\n        })\n\n        return {\n          id: user.id,\n          email: user.email,\n          firstName: user.firstName,\n          lastName: user.lastName,\n          role: user.role,\n          avatar: user.avatar || undefined,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.firstName = user.firstName\n        token.lastName = user.lastName\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as UserRole\n        session.user.firstName = token.firstName as string\n        session.user.lastName = token.lastName as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n    error: '/auth/error',\n  }\n}\n\n// Load role permissions from database with caching\nasync function loadRolePermissions(): Promise<Record<string, string[]>> {\n  const now = Date.now()\n\n  // Return cached permissions if still valid\n  if (cacheLastUpdated && (now - cacheLastUpdated) < CACHE_DURATION) {\n    return rolePermissionsCache\n  }\n\n  try {\n    // Fetch all active role permissions from database\n    const permissions = await prisma.rolePermission.findMany({\n      where: { isActive: true },\n      select: { role: true, permission: true }\n    })\n\n    // Group permissions by role\n    const rolePermissions: Record<string, string[]> = {}\n    for (const perm of permissions) {\n      if (!rolePermissions[perm.role]) {\n        rolePermissions[perm.role] = []\n      }\n      rolePermissions[perm.role].push(perm.permission)\n    }\n\n    // Update cache\n    rolePermissionsCache = rolePermissions\n    cacheLastUpdated = now\n\n    return rolePermissions\n  } catch (error) {\n    console.error('Error loading role permissions from database:', error)\n    // Return empty permissions on error to be safe\n    return {}\n  }\n}\n\n// Get role permissions (cached)\nexport async function getRolePermissions(): Promise<Record<string, string[]>> {\n  return await loadRolePermissions()\n}\n\n// Clear permissions cache (call this when permissions are updated)\nexport function clearPermissionsCache() {\n  rolePermissionsCache = {}\n  cacheLastUpdated = 0\n}\n\n// Initialize permissions cache on server startup\nexport async function initializePermissionsCache() {\n  try {\n    await loadRolePermissions()\n    console.log('✅ Permissions cache initialized')\n  } catch (error) {\n    console.error('❌ Failed to initialize permissions cache:', error)\n  }\n}\n\nexport async function hasPermission(userRole: UserRole, permission: string): Promise<boolean> {\n  const rolePermissions = await getRolePermissions()\n  return rolePermissions[userRole]?.includes(permission) || false\n}\n\nexport async function checkPermissions(userRole: UserRole, requiredPermissions: string[]): Promise<boolean> {\n  const rolePermissions = await getRolePermissions()\n  return requiredPermissions.every(permission => rolePermissions[userRole]?.includes(permission) || false)\n}\n\n// Synchronous version for backward compatibility (uses cache)\nexport function hasPermissionSync(userRole: UserRole, permission: string): boolean {\n  return rolePermissionsCache[userRole]?.includes(permission) || false\n}\n\nexport function checkPermissionsSync(userRole: UserRole, requiredPermissions: string[]): boolean {\n  return requiredPermissions.every(permission => hasPermissionSync(userRole, permission))\n}\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AACA;AACA;;;;;AAGA,sEAAsE;AACtE,IAAI,uBAAiD,CAAC;AACtD,IAAI,mBAAmB;AACvB,MAAM,iBAAiB,IAAI,KAAK,KAAK,YAAY;;AAE1C,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;oBAC3B,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,oBAAoB;gBACpB,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;oBACvB,OAAO;wBAAE,IAAI,KAAK,EAAE;oBAAC;oBACrB,MAAM;wBAAE,WAAW,IAAI;oBAAO;gBAChC;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,WAAW,KAAK,SAAS;oBACzB,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM,IAAI;gBACzB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;AACF;AAEA,mDAAmD;AACnD,eAAe;IACb,MAAM,MAAM,KAAK,GAAG;IAEpB,2CAA2C;IAC3C,IAAI,oBAAoB,AAAC,MAAM,mBAAoB,gBAAgB;QACjE,OAAO;IACT;IAEA,IAAI;QACF,kDAAkD;QAClD,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;YACvD,OAAO;gBAAE,UAAU;YAAK;YACxB,QAAQ;gBAAE,MAAM;gBAAM,YAAY;YAAK;QACzC;QAEA,4BAA4B;QAC5B,MAAM,kBAA4C,CAAC;QACnD,KAAK,MAAM,QAAQ,YAAa;YAC9B,IAAI,CAAC,eAAe,CAAC,KAAK,IAAI,CAAC,EAAE;gBAC/B,eAAe,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE;YACjC;YACA,eAAe,CAAC,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,UAAU;QACjD;QAEA,eAAe;QACf,uBAAuB;QACvB,mBAAmB;QAEnB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iDAAiD;QAC/D,+CAA+C;QAC/C,OAAO,CAAC;IACV;AACF;AAGO,eAAe;IACpB,OAAO,MAAM;AACf;AAGO,SAAS;IACd,uBAAuB,CAAC;IACxB,mBAAmB;AACrB;AAGO,eAAe;IACpB,IAAI;QACF,MAAM;QACN,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;IAC7D;AACF;AAEO,eAAe,cAAc,QAAkB,EAAE,UAAkB;IACxE,MAAM,kBAAkB,MAAM;IAC9B,OAAO,eAAe,CAAC,SAAS,EAAE,SAAS,eAAe;AAC5D;AAEO,eAAe,iBAAiB,QAAkB,EAAE,mBAA6B;IACtF,MAAM,kBAAkB,MAAM;IAC9B,OAAO,oBAAoB,KAAK,CAAC,CAAA,aAAc,eAAe,CAAC,SAAS,EAAE,SAAS,eAAe;AACpG;AAGO,SAAS,kBAAkB,QAAkB,EAAE,UAAkB;IACtE,OAAO,oBAAoB,CAAC,SAAS,EAAE,SAAS,eAAe;AACjE;AAEO,SAAS,qBAAqB,QAAkB,EAAE,mBAA6B;IACpF,OAAO,oBAAoB,KAAK,CAAC,CAAA,aAAc,kBAAkB,UAAU;AAC7E", "debugId": null}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/lib/audit-logger.ts"], "sourcesContent": ["import { prisma } from '@/lib/prisma'\nimport { NextRequest } from 'next/server'\n\nexport interface AuditLogData {\n  userId: string\n  action: string\n  resource: string\n  resourceId?: string\n  oldValues?: any\n  newValues?: any\n  ipAddress?: string\n  userAgent?: string\n  metadata?: any\n}\n\nexport interface AuditContext {\n  userId: string\n  ipAddress?: string\n  userAgent?: string\n}\n\n/**\n * Enhanced audit logging utility for comprehensive system tracking\n */\nexport class AuditLogger {\n  /**\n   * Create an audit log entry\n   */\n  static async log(data: AuditLogData): Promise<void> {\n    try {\n      await prisma.auditLog.create({\n        data: {\n          userId: data.userId,\n          action: data.action,\n          resource: data.resource,\n          resourceId: data.resourceId,\n          oldValues: data.oldValues,\n          newValues: data.newValues,\n          ipAddress: data.ipAddress,\n          userAgent: data.userAgent,\n          timestamp: new Date()\n        }\n      })\n    } catch (error) {\n      console.error('Failed to create audit log:', error)\n      // Don't throw error to avoid breaking the main operation\n    }\n  }\n\n  /**\n   * Extract audit context from request\n   */\n  static getContextFromRequest(request: NextRequest, userId: string): AuditContext {\n    return {\n      userId,\n      ipAddress: this.getClientIP(request),\n      userAgent: request.headers.get('user-agent') || undefined\n    }\n  }\n\n  /**\n   * Get client IP address from request\n   */\n  private static getClientIP(request: NextRequest): string | undefined {\n    // Check various headers for the real IP\n    const forwarded = request.headers.get('x-forwarded-for')\n    const realIP = request.headers.get('x-real-ip')\n    const remoteAddr = request.headers.get('x-remote-addr')\n    \n    if (forwarded) {\n      return forwarded.split(',')[0].trim()\n    }\n    \n    return realIP || remoteAddr || undefined\n  }\n\n  /**\n   * Log user authentication events\n   */\n  static async logAuth(context: AuditContext, action: 'LOGIN' | 'LOGOUT' | 'LOGIN_FAILED', metadata?: any): Promise<void> {\n    await this.log({\n      userId: context.userId,\n      action,\n      resource: 'User',\n      resourceId: context.userId,\n      newValues: {\n        timestamp: new Date(),\n        ...metadata\n      },\n      ipAddress: context.ipAddress,\n      userAgent: context.userAgent\n    })\n  }\n\n  /**\n   * Log CRUD operations\n   */\n  static async logCRUD(\n    context: AuditContext,\n    action: 'CREATE' | 'READ' | 'UPDATE' | 'DELETE',\n    resource: string,\n    resourceId: string,\n    oldValues?: any,\n    newValues?: any\n  ): Promise<void> {\n    await this.log({\n      userId: context.userId,\n      action,\n      resource,\n      resourceId,\n      oldValues,\n      newValues,\n      ipAddress: context.ipAddress,\n      userAgent: context.userAgent\n    })\n  }\n\n  /**\n   * Log loan-specific operations\n   */\n  static async logLoanOperation(\n    context: AuditContext,\n    action: 'APPROVE' | 'REJECT' | 'DISBURSE' | 'REQUEST_MORE_INFO',\n    loanId: string,\n    oldValues?: any,\n    newValues?: any\n  ): Promise<void> {\n    await this.log({\n      userId: context.userId,\n      action,\n      resource: 'Loan',\n      resourceId: loanId,\n      oldValues,\n      newValues,\n      ipAddress: context.ipAddress,\n      userAgent: context.userAgent\n    })\n  }\n\n  /**\n   * Log payment operations\n   */\n  static async logPaymentOperation(\n    context: AuditContext,\n    action: 'RECORD_PAYMENT' | 'REVERSE_PAYMENT' | 'UPDATE_PAYMENT',\n    paymentId: string,\n    oldValues?: any,\n    newValues?: any\n  ): Promise<void> {\n    await this.log({\n      userId: context.userId,\n      action,\n      resource: 'Payment',\n      resourceId: paymentId,\n      oldValues,\n      newValues,\n      ipAddress: context.ipAddress,\n      userAgent: context.userAgent\n    })\n  }\n\n  /**\n   * Log permission changes\n   */\n  static async logPermissionChange(\n    context: AuditContext,\n    role: string,\n    oldPermissions: string[],\n    newPermissions: string[]\n  ): Promise<void> {\n    await this.log({\n      userId: context.userId,\n      action: 'PERMISSIONS_UPDATED',\n      resource: 'RolePermission',\n      resourceId: role,\n      oldValues: { permissions: oldPermissions },\n      newValues: { permissions: newPermissions },\n      ipAddress: context.ipAddress,\n      userAgent: context.userAgent\n    })\n  }\n\n  /**\n   * Log system configuration changes\n   */\n  static async logSystemConfigChange(\n    context: AuditContext,\n    configKey: string,\n    oldValue: any,\n    newValue: any\n  ): Promise<void> {\n    await this.log({\n      userId: context.userId,\n      action: 'CONFIG_UPDATE',\n      resource: 'SystemConfig',\n      resourceId: configKey,\n      oldValues: { value: oldValue },\n      newValues: { value: newValue },\n      ipAddress: context.ipAddress,\n      userAgent: context.userAgent\n    })\n  }\n\n  /**\n   * Log bulk operations\n   */\n  static async logBulkOperation(\n    context: AuditContext,\n    action: string,\n    resource: string,\n    affectedCount: number,\n    metadata?: any\n  ): Promise<void> {\n    await this.log({\n      userId: context.userId,\n      action: `BULK_${action}`,\n      resource,\n      newValues: {\n        affectedCount,\n        ...metadata\n      },\n      ipAddress: context.ipAddress,\n      userAgent: context.userAgent\n    })\n  }\n\n  /**\n   * Log security events\n   */\n  static async logSecurityEvent(\n    context: AuditContext,\n    event: 'UNAUTHORIZED_ACCESS' | 'PERMISSION_DENIED' | 'SUSPICIOUS_ACTIVITY',\n    resource: string,\n    details?: any\n  ): Promise<void> {\n    await this.log({\n      userId: context.userId,\n      action: `SECURITY_${event}`,\n      resource,\n      newValues: {\n        event,\n        details,\n        timestamp: new Date()\n      },\n      ipAddress: context.ipAddress,\n      userAgent: context.userAgent\n    })\n  }\n}\n\n/**\n * Convenience function for quick audit logging\n */\nexport async function auditLog(data: AuditLogData): Promise<void> {\n  await AuditLogger.log(data)\n}\n\n/**\n * Middleware helper for automatic audit logging\n */\nexport function withAuditLogging<T extends any[], R>(\n  fn: (...args: T) => Promise<R>,\n  getAuditData: (...args: T) => AuditLogData\n) {\n  return async (...args: T): Promise<R> => {\n    const result = await fn(...args)\n    \n    try {\n      const auditData = getAuditData(...args)\n      await AuditLogger.log(auditData)\n    } catch (error) {\n      console.error('Audit logging failed:', error)\n    }\n    \n    return result\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAwBO,MAAM;IACX;;GAEC,GACD,aAAa,IAAI,IAAkB,EAAiB;QAClD,IAAI;YACF,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC3B,MAAM;oBACJ,QAAQ,KAAK,MAAM;oBACnB,QAAQ,KAAK,MAAM;oBACnB,UAAU,KAAK,QAAQ;oBACvB,YAAY,KAAK,UAAU;oBAC3B,WAAW,KAAK,SAAS;oBACzB,WAAW,KAAK,SAAS;oBACzB,WAAW,KAAK,SAAS;oBACzB,WAAW,KAAK,SAAS;oBACzB,WAAW,IAAI;gBACjB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,yDAAyD;QAC3D;IACF;IAEA;;GAEC,GACD,OAAO,sBAAsB,OAAoB,EAAE,MAAc,EAAgB;QAC/E,OAAO;YACL;YACA,WAAW,IAAI,CAAC,WAAW,CAAC;YAC5B,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;QAClD;IACF;IAEA;;GAEC,GACD,OAAe,YAAY,OAAoB,EAAsB;QACnE,wCAAwC;QACxC,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;QACtC,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QACnC,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEvC,IAAI,WAAW;YACb,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;QACrC;QAEA,OAAO,UAAU,cAAc;IACjC;IAEA;;GAEC,GACD,aAAa,QAAQ,OAAqB,EAAE,MAA2C,EAAE,QAAc,EAAiB;QACtH,MAAM,IAAI,CAAC,GAAG,CAAC;YACb,QAAQ,QAAQ,MAAM;YACtB;YACA,UAAU;YACV,YAAY,QAAQ,MAAM;YAC1B,WAAW;gBACT,WAAW,IAAI;gBACf,GAAG,QAAQ;YACb;YACA,WAAW,QAAQ,SAAS;YAC5B,WAAW,QAAQ,SAAS;QAC9B;IACF;IAEA;;GAEC,GACD,aAAa,QACX,OAAqB,EACrB,MAA+C,EAC/C,QAAgB,EAChB,UAAkB,EAClB,SAAe,EACf,SAAe,EACA;QACf,MAAM,IAAI,CAAC,GAAG,CAAC;YACb,QAAQ,QAAQ,MAAM;YACtB;YACA;YACA;YACA;YACA;YACA,WAAW,QAAQ,SAAS;YAC5B,WAAW,QAAQ,SAAS;QAC9B;IACF;IAEA;;GAEC,GACD,aAAa,iBACX,OAAqB,EACrB,MAA+D,EAC/D,MAAc,EACd,SAAe,EACf,SAAe,EACA;QACf,MAAM,IAAI,CAAC,GAAG,CAAC;YACb,QAAQ,QAAQ,MAAM;YACtB;YACA,UAAU;YACV,YAAY;YACZ;YACA;YACA,WAAW,QAAQ,SAAS;YAC5B,WAAW,QAAQ,SAAS;QAC9B;IACF;IAEA;;GAEC,GACD,aAAa,oBACX,OAAqB,EACrB,MAA+D,EAC/D,SAAiB,EACjB,SAAe,EACf,SAAe,EACA;QACf,MAAM,IAAI,CAAC,GAAG,CAAC;YACb,QAAQ,QAAQ,MAAM;YACtB;YACA,UAAU;YACV,YAAY;YACZ;YACA;YACA,WAAW,QAAQ,SAAS;YAC5B,WAAW,QAAQ,SAAS;QAC9B;IACF;IAEA;;GAEC,GACD,aAAa,oBACX,OAAqB,EACrB,IAAY,EACZ,cAAwB,EACxB,cAAwB,EACT;QACf,MAAM,IAAI,CAAC,GAAG,CAAC;YACb,QAAQ,QAAQ,MAAM;YACtB,QAAQ;YACR,UAAU;YACV,YAAY;YACZ,WAAW;gBAAE,aAAa;YAAe;YACzC,WAAW;gBAAE,aAAa;YAAe;YACzC,WAAW,QAAQ,SAAS;YAC5B,WAAW,QAAQ,SAAS;QAC9B;IACF;IAEA;;GAEC,GACD,aAAa,sBACX,OAAqB,EACrB,SAAiB,EACjB,QAAa,EACb,QAAa,EACE;QACf,MAAM,IAAI,CAAC,GAAG,CAAC;YACb,QAAQ,QAAQ,MAAM;YACtB,QAAQ;YACR,UAAU;YACV,YAAY;YACZ,WAAW;gBAAE,OAAO;YAAS;YAC7B,WAAW;gBAAE,OAAO;YAAS;YAC7B,WAAW,QAAQ,SAAS;YAC5B,WAAW,QAAQ,SAAS;QAC9B;IACF;IAEA;;GAEC,GACD,aAAa,iBACX,OAAqB,EACrB,MAAc,EACd,QAAgB,EAChB,aAAqB,EACrB,QAAc,EACC;QACf,MAAM,IAAI,CAAC,GAAG,CAAC;YACb,QAAQ,QAAQ,MAAM;YACtB,QAAQ,CAAC,KAAK,EAAE,QAAQ;YACxB;YACA,WAAW;gBACT;gBACA,GAAG,QAAQ;YACb;YACA,WAAW,QAAQ,SAAS;YAC5B,WAAW,QAAQ,SAAS;QAC9B;IACF;IAEA;;GAEC,GACD,aAAa,iBACX,OAAqB,EACrB,KAA0E,EAC1E,QAAgB,EAChB,OAAa,EACE;QACf,MAAM,IAAI,CAAC,GAAG,CAAC;YACb,QAAQ,QAAQ,MAAM;YACtB,QAAQ,CAAC,SAAS,EAAE,OAAO;YAC3B;YACA,WAAW;gBACT;gBACA;gBACA,WAAW,IAAI;YACjB;YACA,WAAW,QAAQ,SAAS;YAC5B,WAAW,QAAQ,SAAS;QAC9B;IACF;AACF;AAKO,eAAe,SAAS,IAAkB;IAC/C,MAAM,YAAY,GAAG,CAAC;AACxB;AAKO,SAAS,iBACd,EAA8B,EAC9B,YAA0C;IAE1C,OAAO,OAAO,GAAG;QACf,MAAM,SAAS,MAAM,MAAM;QAE3B,IAAI;YACF,MAAM,YAAY,gBAAgB;YAClC,MAAM,YAAY,GAAG,CAAC;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/app/api/payments/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\nimport { hasPermissionSync } from '@/lib/auth'\nimport { AuditLogger } from '@/lib/audit-logger'\nimport { z } from 'zod'\n\n// Validation schema for payment creation\nconst createPaymentSchema = z.object({\n  loanId: z.string().min(1, 'Loan ID is required'),\n  amount: z.number().positive('Amount must be positive'),\n  paymentMethod: z.enum(['CASH', 'BANK_TRANSFER', 'CHEQUE', 'ONLINE', 'MOBILE_PAYMENT']),\n  referenceNumber: z.string().optional(),\n  notes: z.string().optional(),\n  paymentDate: z.string().transform((str) => new Date(str)).optional(),\n})\n\n// GET /api/payments - List payments with filtering\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session || !hasPermissionSync(session.user.role, 'payments:read')) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const { searchParams } = new URL(request.url)\n    const page = parseInt(searchParams.get('page') || '1')\n    const limit = parseInt(searchParams.get('limit') || '10')\n    const search = searchParams.get('search') || ''\n    const loanId = searchParams.get('loanId') || ''\n    const paymentMethod = searchParams.get('paymentMethod') || ''\n    const startDate = searchParams.get('startDate')\n    const endDate = searchParams.get('endDate')\n\n    const skip = (page - 1) * limit\n\n    // Build where clause\n    const where: any = {}\n\n    if (search) {\n      where.OR = [\n        { referenceNumber: { contains: search, mode: 'insensitive' } },\n        { loan: { loanId: { contains: search, mode: 'insensitive' } } },\n        { loan: { customer: { \n          OR: [\n            { firstName: { contains: search, mode: 'insensitive' } },\n            { lastName: { contains: search, mode: 'insensitive' } },\n            { nationalId: { contains: search, mode: 'insensitive' } }\n          ]\n        }}}\n      ]\n    }\n\n    if (loanId) {\n      where.loanId = loanId\n    }\n\n    if (paymentMethod) {\n      where.paymentMethod = paymentMethod\n    }\n\n    if (startDate && endDate) {\n      where.paymentDate = {\n        gte: new Date(startDate),\n        lte: new Date(endDate)\n      }\n    }\n\n    const [payments, total] = await Promise.all([\n      prisma.payment.findMany({\n        where,\n        include: {\n          loan: {\n            select: {\n              id: true,\n              loanNumber: true,\n              customer: {\n                select: {\n                  id: true,\n                  nationalId: true,\n                  firstName: true,\n                  lastName: true,\n                  phone: true,\n                }\n              },\n              loanType: {\n                select: {\n                  name: true\n                }\n              }\n            }\n          },\n          createdByUser: {\n            select: {\n              id: true,\n              firstName: true,\n              lastName: true,\n              role: true,\n            }\n          }\n        },\n        orderBy: { paymentDate: 'desc' },\n        skip,\n        take: limit,\n      }),\n      prisma.payment.count({ where })\n    ])\n\n    const pages = Math.ceil(total / limit)\n\n    return NextResponse.json({\n      payments,\n      pagination: {\n        page,\n        limit,\n        total,\n        pages\n      }\n    })\n  } catch (error) {\n    console.error('Error fetching payments:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n\n// POST /api/payments - Create new payment\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session || !hasPermissionSync(session.user.role, 'payments:create')) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const body = await request.json()\n    const validatedData = createPaymentSchema.parse(body)\n\n    // Check if loan exists and is active\n    const loan = await prisma.loan.findUnique({\n      where: { id: validatedData.loanId },\n      include: {\n        customer: {\n          select: {\n            id: true,\n            nationalId: true,\n            firstName: true,\n            lastName: true,\n          }\n        }\n      }\n    })\n\n    if (!loan) {\n      return NextResponse.json({ error: 'Loan not found' }, { status: 404 })\n    }\n\n    if (!['ACTIVE', 'DISBURSED', 'OVERDUE'].includes(loan.status)) {\n      return NextResponse.json(\n        { error: 'Payments can only be made for active, disbursed, or overdue loans' },\n        { status: 400 }\n      )\n    }\n\n    // Calculate outstanding amount\n    const totalPaid = await prisma.payment.aggregate({\n      where: { loanId: loan.id },\n      _sum: { amount: true }\n    })\n\n    const outstandingAmount = loan.disbursedAmount - (totalPaid._sum.amount || 0)\n\n    if (validatedData.amount > outstandingAmount) {\n      return NextResponse.json(\n        { error: `Payment amount (${validatedData.amount}) exceeds outstanding amount (${outstandingAmount})` },\n        { status: 400 }\n      )\n    }\n\n    const payment = await prisma.$transaction(async (tx) => {\n      // Create the payment\n      const newPayment = await tx.payment.create({\n        data: {\n          loanId: validatedData.loanId,\n          amount: validatedData.amount,\n          paymentMethod: validatedData.paymentMethod,\n          referenceNumber: validatedData.referenceNumber,\n          notes: validatedData.notes,\n          paymentDate: validatedData.paymentDate || new Date(),\n          principalAmount: validatedData.amount, // For now, treat full amount as principal\n          interestAmount: 0, // TODO: Calculate based on payment schedule\n          penaltyAmount: 0,\n          feeAmount: 0,\n          status: 'COMPLETED',\n          processedBy: session.user.id,\n          processedAt: new Date(),\n          collectedBy: session.user.id,\n          collectionDate: new Date(),\n          createdBy: session.user.id,\n        }\n      })\n\n      // Update payment schedules\n      await updatePaymentSchedules(tx, validatedData.loanId, validatedData.amount)\n\n      // Check if loan is fully paid by verifying all payment schedules are completed\n      const pendingSchedules = await tx.paymentSchedule.count({\n        where: {\n          loanId: validatedData.loanId,\n          status: { in: ['PENDING', 'PARTIAL'] }\n        }\n      })\n\n      // Only mark loan as completed if all schedules are paid\n      if (pendingSchedules === 0) {\n        await tx.loan.update({\n          where: { id: loan.id },\n          data: { status: 'COMPLETED' }\n        })\n      } else {\n        // If loan was previously completed but now has pending schedules, mark as active\n        if (loan.status === 'COMPLETED') {\n          await tx.loan.update({\n            where: { id: loan.id },\n            data: { status: 'ACTIVE' }\n          })\n        }\n      }\n\n      return newPayment\n    })\n\n    // Create enhanced audit log\n    const auditContext = AuditLogger.getContextFromRequest(request, session.user.id)\n    await AuditLogger.logPaymentOperation(\n      auditContext,\n      'RECORD_PAYMENT',\n      payment.id,\n      undefined,\n      {\n        amount: validatedData.amount,\n        paymentMethod: validatedData.paymentMethod,\n        loanId: validatedData.loanId,\n        loanNumber: loan.loanNumber,\n        customerName: `${loan.customer.firstName} ${loan.customer.lastName}`,\n        referenceNumber: validatedData.referenceNumber,\n        paymentDate: validatedData.paymentDate || new Date()\n      }\n    )\n\n    // Fetch complete payment with relations\n    const completePayment = await prisma.payment.findUnique({\n      where: { id: payment.id },\n      include: {\n        loan: {\n          include: {\n            customer: {\n              select: {\n                id: true,\n                nationalId: true,\n                firstName: true,\n                lastName: true,\n              }\n            }\n          }\n        }\n      }\n    })\n\n    return NextResponse.json(completePayment, { status: 201 })\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { error: 'Validation error', details: error.errors },\n        { status: 400 }\n      )\n    }\n\n    console.error('Error creating payment:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n\n// Helper function to update payment schedules\nasync function updatePaymentSchedules(tx: any, loanId: string, paymentAmount: number) {\n  // Get all payment schedules (including partial ones) ordered by due date\n  const schedules = await tx.paymentSchedule.findMany({\n    where: {\n      loanId,\n      status: { in: ['PENDING', 'PARTIAL'] }\n    },\n    orderBy: { dueDate: 'asc' }\n  })\n\n  let remainingAmount = paymentAmount\n\n  for (const schedule of schedules) {\n    if (remainingAmount <= 0) break\n\n    const currentPaid = schedule.paidAmount || 0\n    const amountNeeded = schedule.totalAmount - currentPaid\n\n    if (remainingAmount >= amountNeeded) {\n      // Full payment for this schedule (complete it)\n      await tx.paymentSchedule.update({\n        where: { id: schedule.id },\n        data: {\n          status: 'PAID',\n          paidAmount: schedule.totalAmount,\n          paidDate: new Date()\n        }\n      })\n      remainingAmount -= amountNeeded\n    } else {\n      // Partial payment - add to existing paid amount\n      await tx.paymentSchedule.update({\n        where: { id: schedule.id },\n        data: {\n          paidAmount: currentPaid + remainingAmount,\n          status: 'PARTIAL'\n        }\n      })\n      remainingAmount = 0\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAEA;AACA;;;;;;;;AAEA,yCAAyC;AACzC,MAAM,sBAAsB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,QAAQ,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,QAAQ,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC5B,eAAe,+KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;QAAiB;QAAU;QAAU;KAAiB;IACrF,iBAAiB,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACpC,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,MAAQ,IAAI,KAAK,MAAM,QAAQ;AACpE;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,WAAW,CAAC,CAAA,GAAA,oHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,IAAI,CAAC,IAAI,EAAE,kBAAkB;YACtE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,gBAAgB,aAAa,GAAG,CAAC,oBAAoB;QAC3D,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,UAAU,aAAa,GAAG,CAAC;QAEjC,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,qBAAqB;QACrB,MAAM,QAAa,CAAC;QAEpB,IAAI,QAAQ;YACV,MAAM,EAAE,GAAG;gBACT;oBAAE,iBAAiB;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBAC7D;oBAAE,MAAM;wBAAE,QAAQ;4BAAE,UAAU;4BAAQ,MAAM;wBAAc;oBAAE;gBAAE;gBAC9D;oBAAE,MAAM;wBAAE,UAAU;4BAClB,IAAI;gCACF;oCAAE,WAAW;wCAAE,UAAU;wCAAQ,MAAM;oCAAc;gCAAE;gCACvD;oCAAE,UAAU;wCAAE,UAAU;wCAAQ,MAAM;oCAAc;gCAAE;gCACtD;oCAAE,YAAY;wCAAE,UAAU;wCAAQ,MAAM;oCAAc;gCAAE;6BACzD;wBACH;oBAAC;gBAAC;aACH;QACH;QAEA,IAAI,QAAQ;YACV,MAAM,MAAM,GAAG;QACjB;QAEA,IAAI,eAAe;YACjB,MAAM,aAAa,GAAG;QACxB;QAEA,IAAI,aAAa,SAAS;YACxB,MAAM,WAAW,GAAG;gBAClB,KAAK,IAAI,KAAK;gBACd,KAAK,IAAI,KAAK;YAChB;QACF;QAEA,MAAM,CAAC,UAAU,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC1C,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;gBACtB;gBACA,SAAS;oBACP,MAAM;wBACJ,QAAQ;4BACN,IAAI;4BACJ,YAAY;4BACZ,UAAU;gCACR,QAAQ;oCACN,IAAI;oCACJ,YAAY;oCACZ,WAAW;oCACX,UAAU;oCACV,OAAO;gCACT;4BACF;4BACA,UAAU;gCACR,QAAQ;oCACN,MAAM;gCACR;4BACF;wBACF;oBACF;oBACA,eAAe;wBACb,QAAQ;4BACN,IAAI;4BACJ,WAAW;4BACX,UAAU;4BACV,MAAM;wBACR;oBACF;gBACF;gBACA,SAAS;oBAAE,aAAa;gBAAO;gBAC/B;gBACA,MAAM;YACR;YACA,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBAAE;YAAM;SAC9B;QAED,MAAM,QAAQ,KAAK,IAAI,CAAC,QAAQ;QAEhC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA,YAAY;gBACV;gBACA;gBACA;gBACA;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,WAAW,CAAC,CAAA,GAAA,oHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,IAAI,CAAC,IAAI,EAAE,oBAAoB;YACxE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,gBAAgB,oBAAoB,KAAK,CAAC;QAEhD,qCAAqC;QACrC,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI,cAAc,MAAM;YAAC;YAClC,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,IAAI;wBACJ,YAAY;wBACZ,WAAW;wBACX,UAAU;oBACZ;gBACF;YACF;QACF;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,IAAI,CAAC;YAAC;YAAU;YAAa;SAAU,CAAC,QAAQ,CAAC,KAAK,MAAM,GAAG;YAC7D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoE,GAC7E;gBAAE,QAAQ;YAAI;QAElB;QAEA,+BAA+B;QAC/B,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,SAAS,CAAC;YAC/C,OAAO;gBAAE,QAAQ,KAAK,EAAE;YAAC;YACzB,MAAM;gBAAE,QAAQ;YAAK;QACvB;QAEA,MAAM,oBAAoB,KAAK,eAAe,GAAG,CAAC,UAAU,IAAI,CAAC,MAAM,IAAI,CAAC;QAE5E,IAAI,cAAc,MAAM,GAAG,mBAAmB;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,CAAC,gBAAgB,EAAE,cAAc,MAAM,CAAC,8BAA8B,EAAE,kBAAkB,CAAC,CAAC;YAAC,GACtG;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;YAC/C,qBAAqB;YACrB,MAAM,aAAa,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;gBACzC,MAAM;oBACJ,QAAQ,cAAc,MAAM;oBAC5B,QAAQ,cAAc,MAAM;oBAC5B,eAAe,cAAc,aAAa;oBAC1C,iBAAiB,cAAc,eAAe;oBAC9C,OAAO,cAAc,KAAK;oBAC1B,aAAa,cAAc,WAAW,IAAI,IAAI;oBAC9C,iBAAiB,cAAc,MAAM;oBACrC,gBAAgB;oBAChB,eAAe;oBACf,WAAW;oBACX,QAAQ;oBACR,aAAa,QAAQ,IAAI,CAAC,EAAE;oBAC5B,aAAa,IAAI;oBACjB,aAAa,QAAQ,IAAI,CAAC,EAAE;oBAC5B,gBAAgB,IAAI;oBACpB,WAAW,QAAQ,IAAI,CAAC,EAAE;gBAC5B;YACF;YAEA,2BAA2B;YAC3B,MAAM,uBAAuB,IAAI,cAAc,MAAM,EAAE,cAAc,MAAM;YAE3E,+EAA+E;YAC/E,MAAM,mBAAmB,MAAM,GAAG,eAAe,CAAC,KAAK,CAAC;gBACtD,OAAO;oBACL,QAAQ,cAAc,MAAM;oBAC5B,QAAQ;wBAAE,IAAI;4BAAC;4BAAW;yBAAU;oBAAC;gBACvC;YACF;YAEA,wDAAwD;YACxD,IAAI,qBAAqB,GAAG;gBAC1B,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;oBACnB,OAAO;wBAAE,IAAI,KAAK,EAAE;oBAAC;oBACrB,MAAM;wBAAE,QAAQ;oBAAY;gBAC9B;YACF,OAAO;gBACL,iFAAiF;gBACjF,IAAI,KAAK,MAAM,KAAK,aAAa;oBAC/B,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;wBACnB,OAAO;4BAAE,IAAI,KAAK,EAAE;wBAAC;wBACrB,MAAM;4BAAE,QAAQ;wBAAS;oBAC3B;gBACF;YACF;YAEA,OAAO;QACT;QAEA,4BAA4B;QAC5B,MAAM,eAAe,+HAAA,CAAA,cAAW,CAAC,qBAAqB,CAAC,SAAS,QAAQ,IAAI,CAAC,EAAE;QAC/E,MAAM,+HAAA,CAAA,cAAW,CAAC,mBAAmB,CACnC,cACA,kBACA,QAAQ,EAAE,EACV,WACA;YACE,QAAQ,cAAc,MAAM;YAC5B,eAAe,cAAc,aAAa;YAC1C,QAAQ,cAAc,MAAM;YAC5B,YAAY,KAAK,UAAU;YAC3B,cAAc,GAAG,KAAK,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,QAAQ,EAAE;YACpE,iBAAiB,cAAc,eAAe;YAC9C,aAAa,cAAc,WAAW,IAAI,IAAI;QAChD;QAGF,wCAAwC;QACxC,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACtD,OAAO;gBAAE,IAAI,QAAQ,EAAE;YAAC;YACxB,SAAS;gBACP,MAAM;oBACJ,SAAS;wBACP,UAAU;4BACR,QAAQ;gCACN,IAAI;gCACJ,YAAY;gCACZ,WAAW;gCACX,UAAU;4BACZ;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,iBAAiB;YAAE,QAAQ;QAAI;IAC1D,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,+KAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAoB,SAAS,MAAM,MAAM;YAAC,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,8CAA8C;AAC9C,eAAe,uBAAuB,EAAO,EAAE,MAAc,EAAE,aAAqB;IAClF,yEAAyE;IACzE,MAAM,YAAY,MAAM,GAAG,eAAe,CAAC,QAAQ,CAAC;QAClD,OAAO;YACL;YACA,QAAQ;gBAAE,IAAI;oBAAC;oBAAW;iBAAU;YAAC;QACvC;QACA,SAAS;YAAE,SAAS;QAAM;IAC5B;IAEA,IAAI,kBAAkB;IAEtB,KAAK,MAAM,YAAY,UAAW;QAChC,IAAI,mBAAmB,GAAG;QAE1B,MAAM,cAAc,SAAS,UAAU,IAAI;QAC3C,MAAM,eAAe,SAAS,WAAW,GAAG;QAE5C,IAAI,mBAAmB,cAAc;YACnC,+CAA+C;YAC/C,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;gBAC9B,OAAO;oBAAE,IAAI,SAAS,EAAE;gBAAC;gBACzB,MAAM;oBACJ,QAAQ;oBACR,YAAY,SAAS,WAAW;oBAChC,UAAU,IAAI;gBAChB;YACF;YACA,mBAAmB;QACrB,OAAO;YACL,gDAAgD;YAChD,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;gBAC9B,OAAO;oBAAE,IAAI,SAAS,EAAE;gBAAC;gBACzB,MAAM;oBACJ,YAAY,cAAc;oBAC1B,QAAQ;gBACV;YACF;YACA,kBAAkB;QACpB;IACF;AACF", "debugId": null}}]}