{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACb,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,KAGY;QAHZ,EACpB,SAAS,EACT,GAAG,OAC6B,GAHZ;IAIpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 620, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/layout/PageHeader.tsx"], "sourcesContent": ["'use client'\n\nimport { useSession, signOut } from 'next-auth/react'\nimport { Button } from '@/components/ui/button'\nimport { ArrowLeft, LogOut, User, Home } from 'lucide-react'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { useState, useEffect } from 'react'\n\ninterface PageHeaderProps {\n  title: string\n  description?: string\n  showBackButton?: boolean\n  backUrl?: string\n  actions?: React.ReactNode\n  children?: React.ReactNode\n}\n\nexport default function PageHeader({\n  title,\n  description,\n  showBackButton = true,\n  backUrl = '/dashboard',\n  actions,\n  children\n}: PageHeaderProps) {\n  const { data: session } = useSession()\n  const router = useRouter()\n  const [companySettings, setCompanySettings] = useState({\n    systemTitle: 'Nilgala Micro',\n    companyLogo: ''\n  })\n\n  useEffect(() => {\n    fetchCompanySettings()\n  }, [])\n\n  const fetchCompanySettings = async () => {\n    try {\n      const response = await fetch('/api/company-settings')\n      if (response.ok) {\n        const data = await response.json()\n        setCompanySettings({\n          systemTitle: data.systemTitle || 'Nilgala Micro',\n          companyLogo: data.companyLogo || ''\n        })\n      }\n    } catch (error) {\n      console.error('Error fetching company settings:', error)\n    }\n  }\n\n  const handleBack = () => {\n    if (backUrl) {\n      router.push(backUrl)\n    } else {\n      router.back()\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Top Navigation Bar */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-3 sm:py-4\">\n            <div className=\"flex items-center space-x-2 sm:space-x-4 min-w-0 flex-1\">\n              <Link href=\"/dashboard\" className=\"flex items-center space-x-2 min-w-0\">\n                {companySettings.companyLogo ? (\n                  <img\n                    src={companySettings.companyLogo}\n                    alt=\"Company Logo\"\n                    className=\"h-6 sm:h-8 object-contain flex-shrink-0\"\n                    onError={(e) => {\n                      e.currentTarget.style.display = 'none'\n                    }}\n                  />\n                ) : null}\n                {!companySettings.companyLogo && (\n                  <h1 className=\"text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 truncate\">\n                    {companySettings.systemTitle}\n                  </h1>\n                )}\n              </Link>\n              {showBackButton && (\n                <div className=\"flex items-center space-x-1 sm:space-x-2\">\n                  <span className=\"text-gray-400 hidden sm:inline\">|</span>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={handleBack}\n                    className=\"text-gray-600 hover:text-gray-900 px-2 sm:px-3\"\n                  >\n                    <ArrowLeft className=\"h-4 w-4 mr-1 sm:mr-2\" />\n                    <span className=\"hidden sm:inline\">Back</span>\n                  </Button>\n                </div>\n              )}\n            </div>\n\n            <div className=\"flex items-center space-x-2 sm:space-x-4 flex-shrink-0\">\n              <Link href=\"/dashboard\" className=\"hidden sm:block\">\n                <Button variant=\"ghost\" size=\"sm\">\n                  <Home className=\"h-4 w-4 mr-2\" />\n                  Dashboard\n                </Button>\n              </Link>\n\n              {/* Mobile Dashboard Link */}\n              <Link href=\"/dashboard\" className=\"sm:hidden\">\n                <Button variant=\"ghost\" size=\"sm\" className=\"px-2\">\n                  <Home className=\"h-4 w-4\" />\n                </Button>\n              </Link>\n\n              {session?.user && (\n                <>\n                  {/* Desktop User Info */}\n                  <div className=\"hidden lg:flex items-center space-x-2\">\n                    <User className=\"h-5 w-5 text-gray-500\" />\n                    <span className=\"text-sm text-gray-700\">\n                      {session.user.firstName} {session.user.lastName}\n                    </span>\n                    <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\">\n                      {session.user.role.replace('_', ' ')}\n                    </span>\n                  </div>\n\n                  {/* Mobile User Info */}\n                  <div className=\"lg:hidden flex items-center space-x-1\">\n                    <User className=\"h-4 w-4 text-gray-500\" />\n                    <span className=\"text-xs bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded\">\n                      {session.user.role.replace('_', ' ').split(' ')[0]}\n                    </span>\n                  </div>\n\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => signOut({ callbackUrl: '/auth/signin' })}\n                    className=\"px-2 sm:px-3\"\n                  >\n                    <LogOut className=\"h-4 w-4 mr-0 sm:mr-2\" />\n                    <span className=\"hidden sm:inline\">Sign Out</span>\n                  </Button>\n                </>\n              )}\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Page Content */}\n      <main className=\"max-w-7xl mx-auto py-4 sm:py-6 px-4 sm:px-6 lg:px-8\">\n        <div className=\"py-4 sm:py-6\">\n          {/* Page Title Section */}\n          <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-4 mb-4 sm:mb-6\">\n            <div className=\"min-w-0 flex-1\">\n              <h1 className=\"text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 truncate\">{title}</h1>\n              {description && (\n                <p className=\"text-sm sm:text-base text-gray-600 mt-1\">{description}</p>\n              )}\n            </div>\n            {actions && (\n              <div className=\"flex items-center space-x-2 flex-shrink-0\">\n                {actions}\n              </div>\n            )}\n          </div>\n\n          {/* Page Content */}\n          {children}\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;;AAkBe,SAAS,WAAW,KAOjB;QAPiB,EACjC,KAAK,EACL,WAAW,EACX,iBAAiB,IAAI,EACrB,UAAU,YAAY,EACtB,OAAO,EACP,QAAQ,EACQ,GAPiB;;IAQjC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrD,aAAa;QACb,aAAa;IACf;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACF;+BAAG,EAAE;IAEL,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,mBAAmB;oBACjB,aAAa,KAAK,WAAW,IAAI;oBACjC,aAAa,KAAK,WAAW,IAAI;gBACnC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,SAAS;YACX,OAAO,IAAI,CAAC;QACd,OAAO;YACL,OAAO,IAAI;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;;4CAC/B,gBAAgB,WAAW,iBAC1B,6LAAC;gDACC,KAAK,gBAAgB,WAAW;gDAChC,KAAI;gDACJ,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;gDAClC;;;;;uDAEA;4CACH,CAAC,gBAAgB,WAAW,kBAC3B,6LAAC;gDAAG,WAAU;0DACX,gBAAgB,WAAW;;;;;;;;;;;;oCAIjC,gCACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAiC;;;;;;0DACjD,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,6LAAC;wDAAK,WAAU;kEAAmB;;;;;;;;;;;;;;;;;;;;;;;;0CAM3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAChC,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;;8DAC3B,6LAAC,sMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAMrC,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAChC,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;sDAC1C,cAAA,6LAAC,sMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;oCAInB,CAAA,oBAAA,8BAAA,QAAS,IAAI,mBACZ;;0DAEE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;;4DACb,QAAQ,IAAI,CAAC,SAAS;4DAAC;4DAAE,QAAQ,IAAI,CAAC,QAAQ;;;;;;;kEAEjD,6LAAC;wDAAK,WAAU;kEACb,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;0DAKpC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEACb,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;0DAItD,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;wDAAE,aAAa;oDAAe;gDACrD,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;wDAAK,WAAU;kEAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUjD,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAoE;;;;;;wCACjF,6BACC,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;gCAG3D,yBACC,6LAAC;oCAAI,WAAU;8CACZ;;;;;;;;;;;;wBAMN;;;;;;;;;;;;;;;;;;AAKX;GA9JwB;;QAQI,iJAAA,CAAA,aAAU;QACrB,qIAAA,CAAA,YAAS;;;KATF", "debugId": null}}, {"offset": {"line": 1022, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/app/payments/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  Table, \n  TableBody, \n  TableCell, \n  TableHead, \n  TableHeader, \n  TableRow \n} from '@/components/ui/table'\nimport { \n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { \n  Plus, \n  Search, \n  Eye, \n  DollarSign,\n  TrendingUp,\n  Clock,\n  AlertTriangle,\n  Calendar\n} from 'lucide-react'\nimport Link from 'next/link'\nimport PageHeader from '@/components/layout/PageHeader'\nimport { formatCurrency } from '@/lib/utils'\n\ninterface Payment {\n  id: string\n  amount: number | any // Handle Prisma Decimal objects\n  paymentMethod: string\n  referenceNumber?: string\n  paymentDate: string\n  notes?: string\n  loan: {\n    id: string\n    loanNumber: string\n    customer: {\n      id: string\n      nationalId: string\n      firstName: string\n      lastName: string\n      phone: string\n    }\n    loanType: {\n      name: string\n    }\n  }\n  createdByUser?: {\n    firstName: string\n    lastName: string\n  }\n}\n\ninterface PaymentResponse {\n  payments: Payment[]\n  pagination: {\n    page: number\n    limit: number\n    total: number\n    pages: number\n  }\n}\n\nexport default function PaymentsPage() {\n  const { data: session } = useSession()\n  const [payments, setPayments] = useState<Payment[]>([])\n  const [loading, setLoading] = useState(true)\n  const [search, setSearch] = useState('')\n  const [methodFilter, setMethodFilter] = useState('ALL')\n  const [currentPage, setCurrentPage] = useState(1)\n  const [pagination, setPagination] = useState({\n    page: 1,\n    limit: 10,\n    total: 0,\n    pages: 0\n  })\n\n  const fetchPayments = async () => {\n    try {\n      setLoading(true)\n      const params = new URLSearchParams({\n        page: currentPage.toString(),\n        limit: '10',\n        ...(search && { search }),\n        ...(methodFilter && methodFilter !== 'ALL' && { paymentMethod: methodFilter })\n      })\n\n      const response = await fetch(`/api/payments?${params}`)\n      if (response.ok) {\n        const data: PaymentResponse = await response.json()\n        setPayments(data.payments)\n        setPagination(data.pagination)\n      }\n    } catch (error) {\n      console.error('Error fetching payments:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  useEffect(() => {\n    fetchPayments()\n  }, [currentPage, search, methodFilter])\n\n  const getMethodBadge = (method: string) => {\n    const colors = {\n      CASH: 'bg-green-100 text-green-800',\n      BANK_TRANSFER: 'bg-blue-100 text-blue-800',\n      CHEQUE: 'bg-purple-100 text-purple-800',\n      ONLINE: 'bg-orange-100 text-orange-800',\n      MOBILE_PAYMENT: 'bg-pink-100 text-pink-800'\n    }\n\n    return (\n      <Badge className={colors[method as keyof typeof colors] || 'bg-gray-100 text-gray-800'}>\n        {method.replace('_', ' ')}\n      </Badge>\n    )\n  }\n\n  // Calculate summary statistics\n  const stats = {\n    total: pagination.total,\n    totalAmount: payments.reduce((sum, payment) => {\n      // Convert Decimal to number properly\n      const amount = typeof payment.amount === 'object' && payment.amount.toNumber\n        ? payment.amount.toNumber()\n        : Number(payment.amount)\n      return sum + amount\n    }, 0),\n    todayPayments: payments.filter(p =>\n      new Date(p.paymentDate).toDateString() === new Date().toDateString()\n    ).length,\n    avgPayment: payments.length > 0 ?\n      payments.reduce((sum, payment) => {\n        // Convert Decimal to number properly\n        const amount = typeof payment.amount === 'object' && payment.amount.toNumber\n          ? payment.amount.toNumber()\n          : Number(payment.amount)\n        return sum + amount\n      }, 0) / payments.length : 0\n  }\n\n  return (\n    <PageHeader\n      title=\"Payment Management\"\n      description=\"Track and manage loan payments\"\n      actions={\n        <div className=\"flex gap-2\">\n          <Link href=\"/payment-schedules\">\n            <Button variant=\"outline\">\n              <Calendar className=\"h-4 w-4 mr-2\" />\n              Payment Schedules\n            </Button>\n          </Link>\n          <Link href=\"/payments/new\">\n            <Button>\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Record Payment\n            </Button>\n          </Link>\n        </div>\n      }\n    >\n\n      {/* Summary Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\">\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Total Payments</CardTitle>\n            <DollarSign className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{stats.total}</div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Total Amount</CardTitle>\n            <TrendingUp className=\"h-4 w-4 text-green-600\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-green-600\">\n              {formatCurrency(stats.totalAmount)}\n            </div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Today's Payments</CardTitle>\n            <Clock className=\"h-4 w-4 text-blue-600\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-blue-600\">{stats.todayPayments}</div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Average Payment</CardTitle>\n            <AlertTriangle className=\"h-4 w-4 text-orange-600\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-orange-600\">\n              {formatCurrency(Math.round(stats.avgPayment))}\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Filters */}\n      <Card className=\"mb-6\">\n        <CardHeader>\n          <CardTitle>Search & Filter</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex gap-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                <Input\n                  placeholder=\"Search by reference number, customer name, or loan ID...\"\n                  value={search}\n                  onChange={(e) => setSearch(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n            </div>\n            <Select value={methodFilter} onValueChange={setMethodFilter}>\n              <SelectTrigger className=\"w-48\">\n                <SelectValue placeholder=\"Filter by method\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"ALL\">All Methods</SelectItem>\n                <SelectItem value=\"CASH\">Cash</SelectItem>\n                <SelectItem value=\"BANK_TRANSFER\">Bank Transfer</SelectItem>\n                <SelectItem value=\"CHEQUE\">Cheque</SelectItem>\n                <SelectItem value=\"ONLINE\">Online</SelectItem>\n                <SelectItem value=\"MOBILE_PAYMENT\">Mobile Payment</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Payments Table */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Recent Payments</CardTitle>\n          <CardDescription>\n            {pagination.total} total payments\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {loading ? (\n            <div className=\"text-center py-8\">Loading payments...</div>\n          ) : (\n            <>\n              <Table>\n                <TableHeader>\n                  <TableRow>\n                    <TableHead>Date</TableHead>\n                    <TableHead>Customer</TableHead>\n                    <TableHead>Loan ID</TableHead>\n                    <TableHead>Amount</TableHead>\n                    <TableHead>Method</TableHead>\n                    <TableHead>Reference</TableHead>\n                    <TableHead>Recorded By</TableHead>\n                    <TableHead>Actions</TableHead>\n                  </TableRow>\n                </TableHeader>\n                <TableBody>\n                  {payments.map((payment) => (\n                    <TableRow key={payment.id}>\n                      <TableCell>\n                        <div>\n                          <div>{new Date(payment.paymentDate).toLocaleDateString()}</div>\n                          <div className=\"text-sm text-gray-500\">\n                            {new Date(payment.paymentDate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n                          </div>\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <div>\n                          <div className=\"font-medium\">\n                            {payment.loan.customer.firstName} {payment.loan.customer.lastName}\n                          </div>\n                          <div className=\"text-sm text-gray-500\">\n                            {payment.loan.customer.customerId}\n                          </div>\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <div>\n                          <div className=\"font-medium\">{payment.loan.loanNumber}</div>\n                          <div className=\"text-sm text-gray-500\">\n                            {payment.loan.loanType.name}\n                          </div>\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"font-medium text-green-600\">\n                          {formatCurrency(payment.amount)}\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        {getMethodBadge(payment.paymentMethod)}\n                      </TableCell>\n                      <TableCell>\n                        {payment.referenceNumber || '-'}\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"text-sm\">\n                          {payment.createdByUser ?\n                            `${payment.createdByUser.firstName} ${payment.createdByUser.lastName}` :\n                            'System'\n                          }\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <Link href={`/payments/${payment.id}`}>\n                          <Button variant=\"outline\" size=\"sm\">\n                            <Eye className=\"h-4 w-4\" />\n                          </Button>\n                        </Link>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n\n              {/* Pagination */}\n              {pagination.pages > 1 && (\n                <div className=\"flex justify-center gap-2 mt-4\">\n                  <Button\n                    variant=\"outline\"\n                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}\n                    disabled={currentPage === 1}\n                  >\n                    Previous\n                  </Button>\n                  <span className=\"flex items-center px-4\">\n                    Page {currentPage} of {pagination.pages}\n                  </span>\n                  <Button\n                    variant=\"outline\"\n                    onClick={() => setCurrentPage(Math.min(pagination.pages, currentPage + 1))}\n                    disabled={currentPage === pagination.pages}\n                  >\n                    Next\n                  </Button>\n                </div>\n              )}\n            </>\n          )}\n        </CardContent>\n      </Card>\n    </PageHeader>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;;;AAnCA;;;;;;;;;;;;;AA0Ee,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;IACT;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI,gBAAgB;gBACjC,MAAM,YAAY,QAAQ;gBAC1B,OAAO;gBACP,GAAI,UAAU;oBAAE;gBAAO,CAAC;gBACxB,GAAI,gBAAgB,iBAAiB,SAAS;oBAAE,eAAe;gBAAa,CAAC;YAC/E;YAEA,MAAM,WAAW,MAAM,MAAM,AAAC,iBAAuB,OAAP;YAC9C,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAwB,MAAM,SAAS,IAAI;gBACjD,YAAY,KAAK,QAAQ;gBACzB,cAAc,KAAK,UAAU;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG;QAAC;QAAa;QAAQ;KAAa;IAEtC,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS;YACb,MAAM;YACN,eAAe;YACf,QAAQ;YACR,QAAQ;YACR,gBAAgB;QAClB;QAEA,qBACE,6LAAC,oIAAA,CAAA,QAAK;YAAC,WAAW,MAAM,CAAC,OAA8B,IAAI;sBACxD,OAAO,OAAO,CAAC,KAAK;;;;;;IAG3B;IAEA,+BAA+B;IAC/B,MAAM,QAAQ;QACZ,OAAO,WAAW,KAAK;QACvB,aAAa,SAAS,MAAM,CAAC,CAAC,KAAK;YACjC,qCAAqC;YACrC,MAAM,SAAS,OAAO,QAAQ,MAAM,KAAK,YAAY,QAAQ,MAAM,CAAC,QAAQ,GACxE,QAAQ,MAAM,CAAC,QAAQ,KACvB,OAAO,QAAQ,MAAM;YACzB,OAAO,MAAM;QACf,GAAG;QACH,eAAe,SAAS,MAAM,CAAC,CAAA,IAC7B,IAAI,KAAK,EAAE,WAAW,EAAE,YAAY,OAAO,IAAI,OAAO,YAAY,IAClE,MAAM;QACR,YAAY,SAAS,MAAM,GAAG,IAC5B,SAAS,MAAM,CAAC,CAAC,KAAK;YACpB,qCAAqC;YACrC,MAAM,SAAS,OAAO,QAAQ,MAAM,KAAK,YAAY,QAAQ,MAAM,CAAC,QAAQ,GACxE,QAAQ,MAAM,CAAC,QAAQ,KACvB,OAAO,QAAQ,MAAM;YACzB,OAAO,MAAM;QACf,GAAG,KAAK,SAAS,MAAM,GAAG;IAC9B;IAEA,qBACE,6LAAC,6IAAA,CAAA,UAAU;QACT,OAAM;QACN,aAAY;QACZ,uBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;;0CACd,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;8BAIzC,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;;0CACL,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;0BASzC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;8CAAsB,MAAM,KAAK;;;;;;;;;;;;;;;;;kCAGpD,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;8CACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,WAAW;;;;;;;;;;;;;;;;;kCAIvC,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;8CAAoC,MAAM,aAAa;;;;;;;;;;;;;;;;;kCAG1E,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;;0CAE3B,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;8CACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,KAAK,CAAC,MAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;0BAOnD,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gDACzC,WAAU;;;;;;;;;;;;;;;;;8CAIhB,6LAAC,qIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAc,eAAe;;sDAC1C,6LAAC,qIAAA,CAAA,gBAAa;4CAAC,WAAU;sDACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,6LAAC,qIAAA,CAAA,gBAAa;;8DACZ,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAM;;;;;;8DACxB,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAO;;;;;;8DACzB,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAgB;;;;;;8DAClC,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAS;;;;;;8DAC3B,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAS;;;;;;8DAC3B,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7C,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;;oCACb,WAAW,KAAK;oCAAC;;;;;;;;;;;;;kCAGtB,6LAAC,mIAAA,CAAA,cAAW;kCACT,wBACC,6LAAC;4BAAI,WAAU;sCAAmB;;;;;iDAElC;;8CACE,6LAAC,oIAAA,CAAA,QAAK;;sDACJ,6LAAC,oIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;kEACP,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,oIAAA,CAAA,YAAS;kEAAC;;;;;;;;;;;;;;;;;sDAGf,6LAAC,oIAAA,CAAA,YAAS;sDACP,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,oIAAA,CAAA,WAAQ;;sEACP,6LAAC,oIAAA,CAAA,YAAS;sEACR,cAAA,6LAAC;;kFACC,6LAAC;kFAAK,IAAI,KAAK,QAAQ,WAAW,EAAE,kBAAkB;;;;;;kFACtD,6LAAC;wEAAI,WAAU;kFACZ,IAAI,KAAK,QAAQ,WAAW,EAAE,kBAAkB,CAAC,EAAE,EAAE;4EAAE,MAAM;4EAAW,QAAQ;wEAAU;;;;;;;;;;;;;;;;;sEAIjG,6LAAC,oIAAA,CAAA,YAAS;sEACR,cAAA,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;;4EACZ,QAAQ,IAAI,CAAC,QAAQ,CAAC,SAAS;4EAAC;4EAAE,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ;;;;;;;kFAEnE,6LAAC;wEAAI,WAAU;kFACZ,QAAQ,IAAI,CAAC,QAAQ,CAAC,UAAU;;;;;;;;;;;;;;;;;sEAIvC,6LAAC,oIAAA,CAAA,YAAS;sEACR,cAAA,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;kFAAe,QAAQ,IAAI,CAAC,UAAU;;;;;;kFACrD,6LAAC;wEAAI,WAAU;kFACZ,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI;;;;;;;;;;;;;;;;;sEAIjC,6LAAC,oIAAA,CAAA,YAAS;sEACR,cAAA,6LAAC;gEAAI,WAAU;0EACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM;;;;;;;;;;;sEAGlC,6LAAC,oIAAA,CAAA,YAAS;sEACP,eAAe,QAAQ,aAAa;;;;;;sEAEvC,6LAAC,oIAAA,CAAA,YAAS;sEACP,QAAQ,eAAe,IAAI;;;;;;sEAE9B,6LAAC,oIAAA,CAAA,YAAS;sEACR,cAAA,6LAAC;gEAAI,WAAU;0EACZ,QAAQ,aAAa,GACpB,AAAC,GAAqC,OAAnC,QAAQ,aAAa,CAAC,SAAS,EAAC,KAAkC,OAA/B,QAAQ,aAAa,CAAC,QAAQ,IACpE;;;;;;;;;;;sEAIN,6LAAC,oIAAA,CAAA,YAAS;sEACR,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAM,AAAC,aAAuB,OAAX,QAAQ,EAAE;0EACjC,cAAA,6LAAC,qIAAA,CAAA,SAAM;oEAAC,SAAQ;oEAAU,MAAK;8EAC7B,cAAA,6LAAC,mMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;mDAjDR,QAAQ,EAAE;;;;;;;;;;;;;;;;gCA2D9B,WAAW,KAAK,GAAG,mBAClB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;4CACxD,UAAU,gBAAgB;sDAC3B;;;;;;sDAGD,6LAAC;4CAAK,WAAU;;gDAAyB;gDACjC;gDAAY;gDAAK,WAAW,KAAK;;;;;;;sDAEzC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,WAAW,KAAK,EAAE,cAAc;4CACvE,UAAU,gBAAgB,WAAW,KAAK;sDAC3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GAvSwB;;QACI,iJAAA,CAAA,aAAU;;;KADd", "debugId": null}}]}