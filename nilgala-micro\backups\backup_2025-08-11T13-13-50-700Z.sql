--
-- PostgreSQL database dump
--

-- Dumped from database version 17.2
-- Dumped by pg_dump version 17.5

-- Started on 2025-08-11 18:43:51

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

DROP DATABASE IF EXISTS postgres;
--
-- TOC entry 3191 (class 1262 OID 5)
-- Name: postgres; Type: DATABASE; Schema: -; Owner: postgres
--

CREATE DATABASE postgres WITH TEMPLATE = template0 ENCODING = 'UTF8' LOCALE_PROVIDER = libc LOCALE = 'en_US.utf8';


ALTER DATABASE postgres OWNER TO postgres;

\connect postgres

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 3192 (class 0 OID 0)
-- Dependencies: 3191
-- Name: DATABASE postgres; Type: COMMENT; Schema: -; Owner: postgres
--

COMMENT ON DATABASE postgres IS 'default administrative connection database';


--
-- TOC entry 5 (class 2615 OID 21034)
-- Name: public; Type: SCHEMA; Schema: -; Owner: prisma_migration
--

-- *not* creating schema, since initdb creates it


ALTER SCHEMA public OWNER TO prisma_migration;

--
-- TOC entry 3193 (class 0 OID 0)
-- Dependencies: 5
-- Name: SCHEMA public; Type: COMMENT; Schema: -; Owner: prisma_migration
--

COMMENT ON SCHEMA public IS '';


--
-- TOC entry 910 (class 1247 OID 21192)
-- Name: CollectionType; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."CollectionType" AS ENUM (
    'DAILY',
    'WEEKLY',
    'MONTHLY',
    'QUARTERLY',
    'YEARLY'
);


ALTER TYPE public."CollectionType" OWNER TO prisma_migration;

--
-- TOC entry 919 (class 1247 OID 21220)
-- Name: CustomerStatus; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."CustomerStatus" AS ENUM (
    'INACTIVE',
    'ACTIVE',
    'SUSPENDED',
    'BLACKLISTED'
);


ALTER TYPE public."CustomerStatus" OWNER TO prisma_migration;

--
-- TOC entry 898 (class 1247 OID 21138)
-- Name: DocumentType; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."DocumentType" AS ENUM (
    'NATIONAL_ID',
    'PASSPORT',
    'DRIVING_LICENSE',
    'UTILITY_BILL',
    'BANK_STATEMENT',
    'INCOME_CERTIFICATE',
    'PROPERTY_DEED',
    'BUSINESS_REGISTRATION',
    'LOAN_APPLICATION',
    'GUARANTOR_ID',
    'OTHER'
);


ALTER TYPE public."DocumentType" OWNER TO prisma_migration;

--
-- TOC entry 880 (class 1247 OID 21062)
-- Name: EmploymentType; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."EmploymentType" AS ENUM (
    'EMPLOYED',
    'SELF_EMPLOYED',
    'UNEMPLOYED',
    'RETIRED',
    'STUDENT'
);


ALTER TYPE public."EmploymentType" OWNER TO prisma_migration;

--
-- TOC entry 874 (class 1247 OID 21045)
-- Name: Gender; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."Gender" AS ENUM (
    'MALE',
    'FEMALE',
    'OTHER'
);


ALTER TYPE public."Gender" OWNER TO prisma_migration;

--
-- TOC entry 916 (class 1247 OID 21214)
-- Name: InterestCalculationMethod; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."InterestCalculationMethod" AS ENUM (
    'MONTHLY_INTEREST',
    'COMPOUND_INTEREST'
);


ALTER TYPE public."InterestCalculationMethod" OWNER TO prisma_migration;

--
-- TOC entry 904 (class 1247 OID 21174)
-- Name: LoanCategory; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."LoanCategory" AS ENUM (
    'PERSONAL',
    'BUSINESS',
    'AGRICULTURAL',
    'EDUCATION',
    'HOUSING',
    'VEHICLE',
    'MICROFINANCE',
    'EMERGENCY'
);


ALTER TYPE public."LoanCategory" OWNER TO prisma_migration;

--
-- TOC entry 883 (class 1247 OID 21074)
-- Name: LoanStatus; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."LoanStatus" AS ENUM (
    'DRAFT',
    'PENDING_APPROVAL',
    'PENDING_MORE_INFO',
    'APPROVED',
    'REJECTED',
    'DISBURSED',
    'ACTIVE',
    'COMPLETED',
    'DEFAULTED',
    'WRITTEN_OFF'
);


ALTER TYPE public."LoanStatus" OWNER TO prisma_migration;

--
-- TOC entry 877 (class 1247 OID 21052)
-- Name: MaritalStatus; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."MaritalStatus" AS ENUM (
    'SINGLE',
    'MARRIED',
    'DIVORCED',
    'WIDOWED'
);


ALTER TYPE public."MaritalStatus" OWNER TO prisma_migration;

--
-- TOC entry 889 (class 1247 OID 21106)
-- Name: PaymentMethod; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."PaymentMethod" AS ENUM (
    'CASH',
    'BANK_TRANSFER',
    'HAND_OVER_TO_OFFICER',
    'MOBILE_PAYMENT',
    'CHEQUE'
);


ALTER TYPE public."PaymentMethod" OWNER TO prisma_migration;

--
-- TOC entry 892 (class 1247 OID 21118)
-- Name: PaymentStatus; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."PaymentStatus" AS ENUM (
    'PENDING',
    'COMPLETED',
    'FAILED',
    'CANCELLED'
);


ALTER TYPE public."PaymentStatus" OWNER TO prisma_migration;

--
-- TOC entry 886 (class 1247 OID 21096)
-- Name: RepaymentFrequency; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."RepaymentFrequency" AS ENUM (
    'DAILY',
    'WEEKLY',
    'MONTHLY',
    'QUARTERLY'
);


ALTER TYPE public."RepaymentFrequency" OWNER TO prisma_migration;

--
-- TOC entry 895 (class 1247 OID 21128)
-- Name: ScheduleStatus; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."ScheduleStatus" AS ENUM (
    'PENDING',
    'PAID',
    'OVERDUE',
    'PARTIAL'
);


ALTER TYPE public."ScheduleStatus" OWNER TO prisma_migration;

--
-- TOC entry 913 (class 1247 OID 21204)
-- Name: TenureUnit; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."TenureUnit" AS ENUM (
    'DAYS',
    'WEEKS',
    'MONTHS',
    'YEARS'
);


ALTER TYPE public."TenureUnit" OWNER TO prisma_migration;

--
-- TOC entry 901 (class 1247 OID 21162)
-- Name: UserRole; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."UserRole" AS ENUM (
    'SUPER_ADMIN',
    'HIGHER_MANAGEMENT',
    'MANAGER',
    'CREDIT_OFFICER',
    'CUSTOMER_SERVICE_OFFICER'
);


ALTER TYPE public."UserRole" OWNER TO prisma_migration;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- TOC entry 217 (class 1259 OID 21035)
-- Name: _prisma_migrations; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public._prisma_migrations (
    id character varying(36) NOT NULL,
    checksum character varying(64) NOT NULL,
    finished_at timestamp with time zone,
    migration_name character varying(255) NOT NULL,
    logs text,
    rolled_back_at timestamp with time zone,
    started_at timestamp with time zone DEFAULT now() NOT NULL,
    applied_steps_count integer DEFAULT 0 NOT NULL
);


ALTER TABLE public._prisma_migrations OWNER TO prisma_migration;

--
-- TOC entry 231 (class 1259 OID 21355)
-- Name: audit_logs; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.audit_logs (
    id text NOT NULL,
    "userId" text NOT NULL,
    action text NOT NULL,
    resource text NOT NULL,
    "resourceId" text,
    "oldValues" jsonb,
    "newValues" jsonb,
    "ipAddress" text,
    "userAgent" text,
    "timestamp" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.audit_logs OWNER TO prisma_migration;

--
-- TOC entry 230 (class 1259 OID 21345)
-- Name: collection_items; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.collection_items (
    id text NOT NULL,
    "collectionId" text NOT NULL,
    "loanId" text NOT NULL,
    "expectedAmount" numeric(65,30) NOT NULL,
    "collectedAmount" numeric(65,30) DEFAULT 0 NOT NULL,
    status text DEFAULT 'PENDING'::text NOT NULL,
    notes text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.collection_items OWNER TO prisma_migration;

--
-- TOC entry 229 (class 1259 OID 21335)
-- Name: collections; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.collections (
    id text NOT NULL,
    "assignedTo" text NOT NULL,
    "collectionDate" timestamp(3) without time zone NOT NULL,
    route text,
    area text,
    status text DEFAULT 'PENDING'::text NOT NULL,
    "startTime" timestamp(3) without time zone,
    "endTime" timestamp(3) without time zone,
    "totalCollected" numeric(65,30) DEFAULT 0 NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.collections OWNER TO prisma_migration;

--
-- TOC entry 237 (class 1259 OID 21417)
-- Name: credit_officer_targets; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.credit_officer_targets (
    id text NOT NULL,
    "creditOfficerId" text NOT NULL,
    month integer NOT NULL,
    year integer NOT NULL,
    "loanTarget" numeric(65,30) NOT NULL,
    "collectionTarget" numeric(65,30) NOT NULL,
    "setBy" text NOT NULL,
    notes text,
    "isActive" boolean DEFAULT true NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.credit_officer_targets OWNER TO prisma_migration;

--
-- TOC entry 219 (class 1259 OID 21238)
-- Name: customers; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.customers (
    id text NOT NULL,
    "firstName" text NOT NULL,
    "lastName" text NOT NULL,
    "dateOfBirth" timestamp(3) without time zone NOT NULL,
    gender public."Gender" NOT NULL,
    "maritalStatus" public."MaritalStatus" NOT NULL,
    "nationalId" text NOT NULL,
    phone text NOT NULL,
    email text,
    address text NOT NULL,
    city text NOT NULL,
    state text NOT NULL,
    "postalCode" text NOT NULL,
    "gpsCoordinates" text,
    "employmentType" public."EmploymentType" NOT NULL,
    employer text,
    "monthlyIncome" numeric(65,30) NOT NULL,
    "employmentDuration" integer,
    "bankAccount" text,
    "bankName" text,
    "creditScore" integer,
    "existingLoans" integer DEFAULT 0 NOT NULL,
    status public."CustomerStatus" DEFAULT 'INACTIVE'::public."CustomerStatus" NOT NULL,
    "statusUpdatedBy" text,
    "statusUpdatedAt" timestamp(3) without time zone,
    "statusNotes" text,
    "assignedTo" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "additionalPhones" text[] DEFAULT ARRAY[]::text[]
);


ALTER TABLE public.customers OWNER TO prisma_migration;

--
-- TOC entry 239 (class 1259 OID 21579)
-- Name: database_backups; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.database_backups (
    id text NOT NULL,
    filename text NOT NULL,
    "originalName" text NOT NULL,
    "fileSize" bigint NOT NULL,
    "backupType" text NOT NULL,
    status text NOT NULL,
    "filePath" text NOT NULL,
    "createdBy" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "completedAt" timestamp(3) without time zone,
    "errorMessage" text,
    description text
);


ALTER TABLE public.database_backups OWNER TO prisma_migration;

--
-- TOC entry 220 (class 1259 OID 21248)
-- Name: dependents; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.dependents (
    id text NOT NULL,
    "customerId" text NOT NULL,
    "firstName" text NOT NULL,
    "lastName" text NOT NULL,
    relationship text NOT NULL,
    "dateOfBirth" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.dependents OWNER TO prisma_migration;

--
-- TOC entry 228 (class 1259 OID 21325)
-- Name: documents; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.documents (
    id text NOT NULL,
    "fileName" text NOT NULL,
    "originalName" text NOT NULL,
    "fileSize" integer NOT NULL,
    "mimeType" text NOT NULL,
    "documentType" public."DocumentType" NOT NULL,
    "storageKey" text NOT NULL,
    "storageUrl" text,
    "customerId" text,
    "loanId" text,
    "uploadedBy" text NOT NULL,
    "uploadedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "verifiedBy" text,
    "verifiedAt" timestamp(3) without time zone,
    "isVerified" boolean DEFAULT false NOT NULL,
    notes text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.documents OWNER TO prisma_migration;

--
-- TOC entry 224 (class 1259 OID 21286)
-- Name: guarantor_documents; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.guarantor_documents (
    id text NOT NULL,
    "guarantorId" text NOT NULL,
    "documentName" text NOT NULL,
    "documentType" text NOT NULL,
    "fileName" text NOT NULL,
    "fileSize" integer NOT NULL,
    "fileKey" text NOT NULL,
    "uploadedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.guarantor_documents OWNER TO prisma_migration;

--
-- TOC entry 223 (class 1259 OID 21278)
-- Name: guarantors; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.guarantors (
    id text NOT NULL,
    "firstName" text NOT NULL,
    "lastName" text NOT NULL,
    "nationalId" text NOT NULL,
    phone text NOT NULL,
    email text,
    address text NOT NULL,
    city text NOT NULL,
    state text NOT NULL,
    "dateOfBirth" timestamp(3) without time zone,
    occupation text,
    "monthlyIncome" numeric(65,30),
    relationship text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.guarantors OWNER TO prisma_migration;

--
-- TOC entry 222 (class 1259 OID 21270)
-- Name: loan_approvals; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.loan_approvals (
    id text NOT NULL,
    "loanId" text NOT NULL,
    action text NOT NULL,
    notes text,
    "requestedInfo" text,
    "approvedBy" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.loan_approvals OWNER TO prisma_migration;

--
-- TOC entry 238 (class 1259 OID 21560)
-- Name: loan_comments; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.loan_comments (
    id text NOT NULL,
    "loanId" text NOT NULL,
    "userId" text NOT NULL,
    comment text NOT NULL,
    "isInternal" boolean DEFAULT false NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.loan_comments OWNER TO prisma_migration;

--
-- TOC entry 225 (class 1259 OID 21294)
-- Name: loan_guarantors; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.loan_guarantors (
    id text NOT NULL,
    "loanId" text NOT NULL,
    "customerId" text,
    "guarantorId" text,
    "guarantorType" text NOT NULL,
    "liabilityAmount" numeric(65,30) NOT NULL,
    "guarantorStatus" text DEFAULT 'ACTIVE'::text NOT NULL,
    "verificationDate" timestamp(3) without time zone,
    "verificationNotes" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.loan_guarantors OWNER TO prisma_migration;

--
-- TOC entry 236 (class 1259 OID 21398)
-- Name: loan_types; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.loan_types (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    category public."LoanCategory" DEFAULT 'PERSONAL'::public."LoanCategory" NOT NULL,
    "minAmount" numeric(65,30) NOT NULL,
    "maxAmount" numeric(65,30) NOT NULL,
    "defaultInterestRate" numeric(65,30) NOT NULL,
    "minInterestRate" numeric(65,30) NOT NULL,
    "maxInterestRate" numeric(65,30) NOT NULL,
    "defaultTenure" integer NOT NULL,
    "minTenure" integer NOT NULL,
    "maxTenure" integer NOT NULL,
    "tenureUnit" public."TenureUnit" DEFAULT 'MONTHS'::public."TenureUnit" NOT NULL,
    "collectionType" public."CollectionType" DEFAULT 'MONTHLY'::public."CollectionType" NOT NULL,
    "interestCalculationMethod" public."InterestCalculationMethod" DEFAULT 'MONTHLY_INTEREST'::public."InterestCalculationMethod" NOT NULL,
    "processingFeeRate" numeric(65,30) DEFAULT 0 NOT NULL,
    "insuranceFeeRate" numeric(65,30) DEFAULT 0 NOT NULL,
    "gracePeriod" integer DEFAULT 0 NOT NULL,
    "isActive" boolean DEFAULT true NOT NULL,
    "requiresGuarantor" boolean DEFAULT false NOT NULL,
    "maxGuarantors" integer DEFAULT 0 NOT NULL,
    "eligibilityCriteria" jsonb,
    "requiredDocuments" text[] DEFAULT ARRAY[]::text[],
    "createdBy" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.loan_types OWNER TO prisma_migration;

--
-- TOC entry 221 (class 1259 OID 21255)
-- Name: loans; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.loans (
    id text NOT NULL,
    "loanNumber" text NOT NULL,
    "customerId" text NOT NULL,
    "loanTypeId" text NOT NULL,
    "principalAmount" numeric(65,30) NOT NULL,
    "interestRate" numeric(65,30) NOT NULL,
    tenure integer NOT NULL,
    "repaymentFrequency" public."RepaymentFrequency" NOT NULL,
    "gracePeriod" integer DEFAULT 0 NOT NULL,
    "processingFee" numeric(65,30) DEFAULT 0 NOT NULL,
    "insuranceFee" numeric(65,30) DEFAULT 0 NOT NULL,
    "otherCharges" numeric(65,30) DEFAULT 0 NOT NULL,
    "totalAmount" numeric(65,30) NOT NULL,
    "emiAmount" numeric(65,30) NOT NULL,
    "totalInterest" numeric(65,30) NOT NULL,
    status public."LoanStatus" DEFAULT 'DRAFT'::public."LoanStatus" NOT NULL,
    "applicationDate" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "approvalDate" timestamp(3) without time zone,
    "disbursementDate" timestamp(3) without time zone,
    "disbursedAmount" numeric(65,30),
    "disbursementMethod" public."PaymentMethod",
    "disbursementReference" text,
    "disbursementNotes" text,
    "disbursedBy" text,
    "disbursedAt" timestamp(3) without time zone,
    "maturityDate" timestamp(3) without time zone,
    "approvalLevel" integer DEFAULT 0 NOT NULL,
    "approvedBy" text,
    "approvedAt" timestamp(3) without time zone,
    "rejectedBy" text,
    "rejectedAt" timestamp(3) without time zone,
    "rejectionReason" text,
    "createdBy" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.loans OWNER TO prisma_migration;

--
-- TOC entry 235 (class 1259 OID 21389)
-- Name: notifications; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.notifications (
    id text NOT NULL,
    "userId" text,
    title text NOT NULL,
    message text NOT NULL,
    type text NOT NULL,
    channel text NOT NULL,
    "isRead" boolean DEFAULT false NOT NULL,
    "sentAt" timestamp(3) without time zone,
    "readAt" timestamp(3) without time zone,
    metadata jsonb,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.notifications OWNER TO prisma_migration;

--
-- TOC entry 227 (class 1259 OID 21314)
-- Name: payment_schedules; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.payment_schedules (
    id text NOT NULL,
    "loanId" text NOT NULL,
    "installmentNumber" integer NOT NULL,
    "dueDate" timestamp(3) without time zone NOT NULL,
    "principalAmount" numeric(65,30) NOT NULL,
    "interestAmount" numeric(65,30) NOT NULL,
    "totalAmount" numeric(65,30) NOT NULL,
    status public."ScheduleStatus" DEFAULT 'PENDING'::public."ScheduleStatus" NOT NULL,
    "paidAmount" numeric(65,30) DEFAULT 0 NOT NULL,
    "paidDate" timestamp(3) without time zone,
    "daysOverdue" integer DEFAULT 0 NOT NULL,
    "penaltyAmount" numeric(65,30) DEFAULT 0 NOT NULL
);


ALTER TABLE public.payment_schedules OWNER TO prisma_migration;

--
-- TOC entry 226 (class 1259 OID 21303)
-- Name: payments; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.payments (
    id text NOT NULL,
    "loanId" text NOT NULL,
    "scheduleId" text,
    amount numeric(65,30) NOT NULL,
    "paymentDate" timestamp(3) without time zone NOT NULL,
    "paymentMethod" public."PaymentMethod" NOT NULL,
    "referenceNumber" text,
    "principalAmount" numeric(65,30) NOT NULL,
    "interestAmount" numeric(65,30) NOT NULL,
    "penaltyAmount" numeric(65,30) DEFAULT 0 NOT NULL,
    "feeAmount" numeric(65,30) DEFAULT 0 NOT NULL,
    status public."PaymentStatus" DEFAULT 'PENDING'::public."PaymentStatus" NOT NULL,
    "processedBy" text,
    "processedAt" timestamp(3) without time zone,
    "collectedBy" text,
    "collectionDate" timestamp(3) without time zone,
    "receiptNumber" text,
    notes text,
    "createdBy" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.payments OWNER TO prisma_migration;

--
-- TOC entry 233 (class 1259 OID 21371)
-- Name: required_documents; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.required_documents (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    "isActive" boolean DEFAULT true NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.required_documents OWNER TO prisma_migration;

--
-- TOC entry 234 (class 1259 OID 21380)
-- Name: role_permissions; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.role_permissions (
    id text NOT NULL,
    role public."UserRole" NOT NULL,
    permission text NOT NULL,
    "isActive" boolean DEFAULT true NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.role_permissions OWNER TO prisma_migration;

--
-- TOC entry 232 (class 1259 OID 21363)
-- Name: system_config; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.system_config (
    id text NOT NULL,
    key text NOT NULL,
    value text NOT NULL,
    description text,
    category text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.system_config OWNER TO prisma_migration;

--
-- TOC entry 218 (class 1259 OID 21229)
-- Name: users; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.users (
    id text NOT NULL,
    email text NOT NULL,
    password text NOT NULL,
    "firstName" text NOT NULL,
    "lastName" text NOT NULL,
    phone text,
    avatar text,
    role public."UserRole" NOT NULL,
    "isActive" boolean DEFAULT true NOT NULL,
    "lastLogin" timestamp(3) without time zone,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.users OWNER TO prisma_migration;

--
-- TOC entry 3163 (class 0 OID 21035)
-- Dependencies: 217
-- Data for Name: _prisma_migrations; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public._prisma_migrations (id, checksum, finished_at, migration_name, logs, rolled_back_at, started_at, applied_steps_count) FROM stdin;
a07ce4f4-f294-41f6-9edf-b5870155d9b9	766f0b0a00155ed49f80d88a012ee81a9cb1ff672dfa0a391e790fc927448557	2025-08-11 12:12:58.665479+00	0_init	\N	\N	2025-08-11 12:12:58.288789+00	1
86b7c07a-0559-4716-bac7-fabfc01c9d24	c1917fff0ef1fa366dd749159bbb9b0b8efde4ae175d2d0e80ec895e112a90a3	2025-08-11 12:12:58.948815+00	20241209_add_additional_phones	\N	\N	2025-08-11 12:12:58.746396+00	1
\.


--
-- TOC entry 3177 (class 0 OID 21355)
-- Dependencies: 231
-- Data for Name: audit_logs; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.audit_logs (id, "userId", action, resource, "resourceId", "oldValues", "newValues", "ipAddress", "userAgent", "timestamp") FROM stdin;
cme734jua0002pgd0829i0bdq	cme72wf400000pgv40dqaltm0	UPDATE	CompanyLogo	company-logo	\N	{"companyLogo": "/mainlogo.png"}	\N	\N	2025-08-11 12:24:37.234
cme73t18b0001pghchcmm9zpy	cme72wf400000pgv40dqaltm0	USER_UPDATED	User	cme72wf400000pgv40dqaltm0	\N	{"role": "SUPER_ADMIN", "email": "<EMAIL>", "isActive": true}	\N	\N	2025-08-11 12:43:39.515
cme74k9zw0003pgyg3g88h0k2	cme72wf400000pgv40dqaltm0	RESTORE_DATABASE	DatabaseBackup	cme74jnke0001pgygmhsm0oql	\N	{"restoredAt": "2025-08-11T13:04:50.588Z", "backupFilename": "backup_2025-08-11T13-04-21-407Z.sql", "backupCreatedAt": "2025-08-11T13:04:21.410Z"}	\N	\N	2025-08-11 13:04:50.589
cme74ktxh0005pgyg0j4nzykl	cme72wf400000pgv40dqaltm0	DELETE	DatabaseBackup	cme74azye0001pgecc4kvihvl	{"filename": "backup_2025-08-11T12-57-37-667Z.sql", "createdAt": "2025-08-11T12:57:37.670Z"}	\N	\N	\N	2025-08-11 13:05:16.421
cme74sfot000apgygo74vv6dp	cme72wfe90001pgv4nho1evg4	USER_CREATED	User	cme74sfm70008pgygc12mkhw0	\N	{"role": "CREDIT_OFFICER", "email": "<EMAIL>", "lastName": "Sandaruwan", "firstName": "Shanaka"}	\N	\N	2025-08-11 13:11:11.213
\.


--
-- TOC entry 3176 (class 0 OID 21345)
-- Dependencies: 230
-- Data for Name: collection_items; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.collection_items (id, "collectionId", "loanId", "expectedAmount", "collectedAmount", status, notes, "createdAt", "updatedAt") FROM stdin;
\.


--
-- TOC entry 3175 (class 0 OID 21335)
-- Dependencies: 229
-- Data for Name: collections; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.collections (id, "assignedTo", "collectionDate", route, area, status, "startTime", "endTime", "totalCollected", "createdAt", "updatedAt") FROM stdin;
\.


--
-- TOC entry 3183 (class 0 OID 21417)
-- Dependencies: 237
-- Data for Name: credit_officer_targets; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.credit_officer_targets (id, "creditOfficerId", month, year, "loanTarget", "collectionTarget", "setBy", notes, "isActive", "createdAt", "updatedAt") FROM stdin;
cme74oxxy0007pgygs6ef0f3y	cme72wfw00003pgv4impxe3ur	8	2025	500000.000000000000000000000000000000	500000.000000000000000000000000000000	cme72wf400000pgv40dqaltm0		t	2025-08-11 13:08:28.246	2025-08-11 13:08:28.246
\.


--
-- TOC entry 3165 (class 0 OID 21238)
-- Dependencies: 219
-- Data for Name: customers; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.customers (id, "firstName", "lastName", "dateOfBirth", gender, "maritalStatus", "nationalId", phone, email, address, city, state, "postalCode", "gpsCoordinates", "employmentType", employer, "monthlyIncome", "employmentDuration", "bankAccount", "bankName", "creditScore", "existingLoans", status, "statusUpdatedBy", "statusUpdatedAt", "statusNotes", "assignedTo", "createdAt", "updatedAt", "additionalPhones") FROM stdin;
\.


--
-- TOC entry 3185 (class 0 OID 21579)
-- Dependencies: 239
-- Data for Name: database_backups; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.database_backups (id, filename, "originalName", "fileSize", "backupType", status, "filePath", "createdBy", "createdAt", "completedAt", "errorMessage", description) FROM stdin;
cme74bblc0003pgecwb8379lx	backup_2025-08-11T12-57-52-645Z.sql	backup_2025-08-11T12-57-52-645Z.sql	65642	MANUAL	COMPLETED	C:\\Users\\<USER>\\Desktop\\micro_finance\\nilgala-micro\\backups\\backup_2025-08-11T12-57-52-645Z.sql	cme72wf400000pgv40dqaltm0	2025-08-11 12:57:52.647	2025-08-11 12:57:58.111	\N	Test
cme74g3xr0005pgeckjumj826	backup_2025-08-11T13-01-36-109Z.sql	backup_2025-08-11T13-01-36-109Z.sql	65950	MANUAL	COMPLETED	C:\\Users\\<USER>\\Desktop\\micro_finance\\nilgala-micro\\backups\\backup_2025-08-11T13-01-36-109Z.sql	cme72wf400000pgv40dqaltm0	2025-08-11 13:01:36.112	2025-08-11 13:01:44.553	\N	111111
cme74jnke0001pgygmhsm0oql	backup_2025-08-11T13-04-21-407Z.sql	backup_2025-08-11T13-04-21-407Z.sql	66256	MANUAL	COMPLETED	C:\\Users\\<USER>\\Desktop\\micro_finance\\nilgala-micro\\backups\\backup_2025-08-11T13-04-21-407Z.sql	cme72wf400000pgv40dqaltm0	2025-08-11 13:04:21.41	2025-08-11 13:04:27.036	\N	Today
cme74vur1000cpgygk71wzdkj	backup_2025-08-11T13-13-50-700Z.sql	backup_2025-08-11T13-13-50-700Z.sql	0	MANUAL	CREATING	C:\\Users\\<USER>\\Desktop\\micro_finance\\nilgala-micro\\backups\\backup_2025-08-11T13-13-50-700Z.sql	cme72wf400000pgv40dqaltm0	2025-08-11 13:13:50.702	\N	\N	\N
\.


--
-- TOC entry 3166 (class 0 OID 21248)
-- Dependencies: 220
-- Data for Name: dependents; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.dependents (id, "customerId", "firstName", "lastName", relationship, "dateOfBirth") FROM stdin;
\.


--
-- TOC entry 3174 (class 0 OID 21325)
-- Dependencies: 228
-- Data for Name: documents; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.documents (id, "fileName", "originalName", "fileSize", "mimeType", "documentType", "storageKey", "storageUrl", "customerId", "loanId", "uploadedBy", "uploadedAt", "verifiedBy", "verifiedAt", "isVerified", notes, "createdAt", "updatedAt") FROM stdin;
\.


--
-- TOC entry 3170 (class 0 OID 21286)
-- Dependencies: 224
-- Data for Name: guarantor_documents; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.guarantor_documents (id, "guarantorId", "documentName", "documentType", "fileName", "fileSize", "fileKey", "uploadedAt") FROM stdin;
\.


--
-- TOC entry 3169 (class 0 OID 21278)
-- Dependencies: 223
-- Data for Name: guarantors; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.guarantors (id, "firstName", "lastName", "nationalId", phone, email, address, city, state, "dateOfBirth", occupation, "monthlyIncome", relationship, "createdAt", "updatedAt") FROM stdin;
\.


--
-- TOC entry 3168 (class 0 OID 21270)
-- Dependencies: 222
-- Data for Name: loan_approvals; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.loan_approvals (id, "loanId", action, notes, "requestedInfo", "approvedBy", "createdAt") FROM stdin;
\.


--
-- TOC entry 3184 (class 0 OID 21560)
-- Dependencies: 238
-- Data for Name: loan_comments; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.loan_comments (id, "loanId", "userId", comment, "isInternal", "createdAt", "updatedAt") FROM stdin;
\.


--
-- TOC entry 3171 (class 0 OID 21294)
-- Dependencies: 225
-- Data for Name: loan_guarantors; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.loan_guarantors (id, "loanId", "customerId", "guarantorId", "guarantorType", "liabilityAmount", "guarantorStatus", "verificationDate", "verificationNotes", "createdAt", "updatedAt") FROM stdin;
\.


--
-- TOC entry 3182 (class 0 OID 21398)
-- Dependencies: 236
-- Data for Name: loan_types; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.loan_types (id, name, description, category, "minAmount", "maxAmount", "defaultInterestRate", "minInterestRate", "maxInterestRate", "defaultTenure", "minTenure", "maxTenure", "tenureUnit", "collectionType", "interestCalculationMethod", "processingFeeRate", "insuranceFeeRate", "gracePeriod", "isActive", "requiresGuarantor", "maxGuarantors", "eligibilityCriteria", "requiredDocuments", "createdBy", "createdAt", "updatedAt") FROM stdin;
\.


--
-- TOC entry 3167 (class 0 OID 21255)
-- Dependencies: 221
-- Data for Name: loans; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.loans (id, "loanNumber", "customerId", "loanTypeId", "principalAmount", "interestRate", tenure, "repaymentFrequency", "gracePeriod", "processingFee", "insuranceFee", "otherCharges", "totalAmount", "emiAmount", "totalInterest", status, "applicationDate", "approvalDate", "disbursementDate", "disbursedAmount", "disbursementMethod", "disbursementReference", "disbursementNotes", "disbursedBy", "disbursedAt", "maturityDate", "approvalLevel", "approvedBy", "approvedAt", "rejectedBy", "rejectedAt", "rejectionReason", "createdBy", "createdAt", "updatedAt") FROM stdin;
\.


--
-- TOC entry 3181 (class 0 OID 21389)
-- Dependencies: 235
-- Data for Name: notifications; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.notifications (id, "userId", title, message, type, channel, "isRead", "sentAt", "readAt", metadata, "createdAt", "updatedAt") FROM stdin;
\.


--
-- TOC entry 3173 (class 0 OID 21314)
-- Dependencies: 227
-- Data for Name: payment_schedules; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.payment_schedules (id, "loanId", "installmentNumber", "dueDate", "principalAmount", "interestAmount", "totalAmount", status, "paidAmount", "paidDate", "daysOverdue", "penaltyAmount") FROM stdin;
\.


--
-- TOC entry 3172 (class 0 OID 21303)
-- Dependencies: 226
-- Data for Name: payments; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.payments (id, "loanId", "scheduleId", amount, "paymentDate", "paymentMethod", "referenceNumber", "principalAmount", "interestAmount", "penaltyAmount", "feeAmount", status, "processedBy", "processedAt", "collectedBy", "collectionDate", "receiptNumber", notes, "createdBy", "createdAt", "updatedAt") FROM stdin;
\.


--
-- TOC entry 3179 (class 0 OID 21371)
-- Dependencies: 233
-- Data for Name: required_documents; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.required_documents (id, name, description, "isActive", "createdAt", "updatedAt") FROM stdin;
cme732d200000pgikl0is0lda	NIC (National Identity Card)	Copy of National Identity Card - front and back	t	2025-08-11 12:22:55.128	2025-08-11 12:22:55.128
cme732d4j0001pgik94ob7ger	Address Proof	Utility bill, bank statement, or other address verification document	t	2025-08-11 12:22:55.22	2025-08-11 12:22:55.22
cme732d5r0002pgik04u9egnz	Bank Statements	Last 3 months bank statements	t	2025-08-11 12:22:55.263	2025-08-11 12:22:55.263
cme732d6y0003pgik99hu1gqx	Business Registration	Business registration certificate (for business loans)	t	2025-08-11 12:22:55.306	2025-08-11 12:22:55.306
cme732d850004pgiku93tfow6	Income Certificate	Salary certificate or income verification letter	t	2025-08-11 12:22:55.349	2025-08-11 12:22:55.349
cme732d9b0005pgik7hn6sh0v	Guarantor NIC	Copy of guarantor's National Identity Card	t	2025-08-11 12:22:55.392	2025-08-11 12:22:55.392
cme732dai0006pgikfacj4drt	Property Documents	Land deed or property ownership documents (for collateral)	t	2025-08-11 12:22:55.434	2025-08-11 12:22:55.434
cme732dbo0007pgikw3jneykp	Employment Letter	Letter from employer confirming employment	t	2025-08-11 12:22:55.477	2025-08-11 12:22:55.477
cme732dcv0008pgik4byja6ic	Tax Returns	Last year's tax return documents	t	2025-08-11 12:22:55.52	2025-08-11 12:22:55.52
cme732de30009pgikwtn6bszb	Passport Photos	2 recent passport-size photographs	t	2025-08-11 12:22:55.563	2025-08-11 12:22:55.563
\.


--
-- TOC entry 3180 (class 0 OID 21380)
-- Dependencies: 234
-- Data for Name: role_permissions; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.role_permissions (id, role, permission, "isActive", "createdAt", "updatedAt") FROM stdin;
cme730y7p0000pgoc6lhlu27k	SUPER_ADMIN	loans:create	t	2025-08-11 12:21:49.237	2025-08-11 12:21:49.237
cme730ya40001pgoc8ucemq1i	MANAGER	loans:create	t	2025-08-11 12:21:49.324	2025-08-11 12:21:49.324
cme730yba0002pgocqoszo950	CREDIT_OFFICER	loans:create	t	2025-08-11 12:21:49.366	2025-08-11 12:21:49.366
cme730ycg0003pgocqjilixao	SUPER_ADMIN	loans:read	t	2025-08-11 12:21:49.408	2025-08-11 12:21:49.408
cme730ydm0004pgocjmgjvuk1	HIGHER_MANAGEMENT	loans:read	t	2025-08-11 12:21:49.45	2025-08-11 12:21:49.45
cme730yet0005pgocatyps5b9	MANAGER	loans:read	t	2025-08-11 12:21:49.493	2025-08-11 12:21:49.493
cme730yg00006pgoc6yy55hu5	CREDIT_OFFICER	loans:read	t	2025-08-11 12:21:49.536	2025-08-11 12:21:49.536
cme730yh50007pgocf115xt2n	CUSTOMER_SERVICE_OFFICER	loans:read	t	2025-08-11 12:21:49.578	2025-08-11 12:21:49.578
cme730yib0008pgocd6jff9h1	SUPER_ADMIN	loans:update	t	2025-08-11 12:21:49.62	2025-08-11 12:21:49.62
cme730yjh0009pgoclek5il9h	MANAGER	loans:update	t	2025-08-11 12:21:49.662	2025-08-11 12:21:49.662
cme730ykn000apgoc4r2clxyw	CREDIT_OFFICER	loans:update	t	2025-08-11 12:21:49.704	2025-08-11 12:21:49.704
cme730ylu000bpgoc6ifk6hzj	SUPER_ADMIN	loans:delete	t	2025-08-11 12:21:49.747	2025-08-11 12:21:49.747
cme730yn2000cpgocxkhvhcpv	SUPER_ADMIN	loans:approve	t	2025-08-11 12:21:49.791	2025-08-11 12:21:49.791
cme730yo9000dpgocdwto10f2	HIGHER_MANAGEMENT	loans:approve	t	2025-08-11 12:21:49.833	2025-08-11 12:21:49.833
cme730ypg000epgocg58lun5s	SUPER_ADMIN	loans:disburse	t	2025-08-11 12:21:49.877	2025-08-11 12:21:49.877
cme730yqm000fpgocri0q7205	MANAGER	loans:disburse	t	2025-08-11 12:21:49.919	2025-08-11 12:21:49.919
cme730yrt000gpgochhf6fmex	CREDIT_OFFICER	loans:disburse	t	2025-08-11 12:21:49.961	2025-08-11 12:21:49.961
cme730yt0000hpgocc9kkwhod	SUPER_ADMIN	customers:create	t	2025-08-11 12:21:50.005	2025-08-11 12:21:50.005
cme730yu7000ipgocprih95kd	MANAGER	customers:create	t	2025-08-11 12:21:50.048	2025-08-11 12:21:50.048
cme730yve000jpgock400vfba	CREDIT_OFFICER	customers:create	t	2025-08-11 12:21:50.09	2025-08-11 12:21:50.09
cme730ywn000kpgocy12z4aex	SUPER_ADMIN	customers:read	t	2025-08-11 12:21:50.135	2025-08-11 12:21:50.135
cme730yxs000lpgocylutgapo	HIGHER_MANAGEMENT	customers:read	t	2025-08-11 12:21:50.176	2025-08-11 12:21:50.176
cme730yyx000mpgocusq9yvcj	MANAGER	customers:read	t	2025-08-11 12:21:50.218	2025-08-11 12:21:50.218
cme730z03000npgocetzxo8as	CREDIT_OFFICER	customers:read	t	2025-08-11 12:21:50.26	2025-08-11 12:21:50.26
cme730z19000opgoc23du7zzh	CUSTOMER_SERVICE_OFFICER	customers:read	t	2025-08-11 12:21:50.301	2025-08-11 12:21:50.301
cme730z2d000ppgoc39o2itd1	SUPER_ADMIN	customers:update	t	2025-08-11 12:21:50.342	2025-08-11 12:21:50.342
cme730z3j000qpgocum2s9grp	MANAGER	customers:update	t	2025-08-11 12:21:50.383	2025-08-11 12:21:50.383
cme730z4p000rpgoczzl5lfvc	CREDIT_OFFICER	customers:update	t	2025-08-11 12:21:50.426	2025-08-11 12:21:50.426
cme730z5v000spgocx0odpt1p	SUPER_ADMIN	customers:delete	t	2025-08-11 12:21:50.468	2025-08-11 12:21:50.468
cme730z72000tpgocacp8tard	SUPER_ADMIN	payments:create	t	2025-08-11 12:21:50.51	2025-08-11 12:21:50.51
cme730z88000upgocsx4s08rj	MANAGER	payments:create	t	2025-08-11 12:21:50.553	2025-08-11 12:21:50.553
cme730z9f000vpgoc3gs5s81w	CREDIT_OFFICER	payments:create	t	2025-08-11 12:21:50.596	2025-08-11 12:21:50.596
cme730zal000wpgoco6o6sf9y	SUPER_ADMIN	payments:read	t	2025-08-11 12:21:50.637	2025-08-11 12:21:50.637
cme730zbr000xpgoc8y8f684d	HIGHER_MANAGEMENT	payments:read	t	2025-08-11 12:21:50.679	2025-08-11 12:21:50.679
cme730zcw000ypgocw5jmojks	MANAGER	payments:read	t	2025-08-11 12:21:50.721	2025-08-11 12:21:50.721
cme730ze3000zpgocw2qobd8j	CREDIT_OFFICER	payments:read	t	2025-08-11 12:21:50.763	2025-08-11 12:21:50.763
cme730zf90010pgoc159weimb	CUSTOMER_SERVICE_OFFICER	payments:read	t	2025-08-11 12:21:50.806	2025-08-11 12:21:50.806
cme730zgg0011pgochbmj74fv	SUPER_ADMIN	payments:update	t	2025-08-11 12:21:50.848	2025-08-11 12:21:50.848
cme730zhl0012pgocnqg0tv6c	MANAGER	payments:update	t	2025-08-11 12:21:50.89	2025-08-11 12:21:50.89
cme730zir0013pgocpyxk3fmv	SUPER_ADMIN	payments:delete	t	2025-08-11 12:21:50.931	2025-08-11 12:21:50.931
cme730zjy0014pgocfrtzq438	SUPER_ADMIN	users:create	t	2025-08-11 12:21:50.975	2025-08-11 12:21:50.975
cme730zl40015pgochabnoccg	HIGHER_MANAGEMENT	users:create	t	2025-08-11 12:21:51.016	2025-08-11 12:21:51.016
cme730zm80016pgocsuxacaif	SUPER_ADMIN	users:read	t	2025-08-11 12:21:51.057	2025-08-11 12:21:51.057
cme730zne0017pgocpfbp2o3h	HIGHER_MANAGEMENT	users:read	t	2025-08-11 12:21:51.099	2025-08-11 12:21:51.099
cme730zoj0018pgocdcc2tutt	MANAGER	users:read	t	2025-08-11 12:21:51.14	2025-08-11 12:21:51.14
cme730zpo0019pgocba4ffvvo	SUPER_ADMIN	users:update	t	2025-08-11 12:21:51.181	2025-08-11 12:21:51.181
cme730zqu001apgoc9kwxjih4	HIGHER_MANAGEMENT	users:update	t	2025-08-11 12:21:51.222	2025-08-11 12:21:51.222
cme730zs0001bpgocuc3ufqt0	SUPER_ADMIN	users:delete	t	2025-08-11 12:21:51.264	2025-08-11 12:21:51.264
cme730zt6001cpgocjkyg0ne3	SUPER_ADMIN	reports:read	t	2025-08-11 12:21:51.306	2025-08-11 12:21:51.306
cme730zud001dpgoceytjiwaw	HIGHER_MANAGEMENT	reports:read	t	2025-08-11 12:21:51.349	2025-08-11 12:21:51.349
cme730zvk001epgocll5ypllj	MANAGER	reports:read	t	2025-08-11 12:21:51.392	2025-08-11 12:21:51.392
cme730zwr001fpgocso19kqmz	CREDIT_OFFICER	reports:read	t	2025-08-11 12:21:51.435	2025-08-11 12:21:51.435
cme730zxx001gpgoc2omc1tbl	SUPER_ADMIN	reports:export	t	2025-08-11 12:21:51.478	2025-08-11 12:21:51.478
cme730zz3001hpgoc0mzs2vla	HIGHER_MANAGEMENT	reports:export	t	2025-08-11 12:21:51.52	2025-08-11 12:21:51.52
cme73100a001ipgoclio6z9hk	MANAGER	reports:export	t	2025-08-11 12:21:51.562	2025-08-11 12:21:51.562
cme73101g001jpgoc0xiy6bnz	SUPER_ADMIN	reports:create	t	2025-08-11 12:21:51.604	2025-08-11 12:21:51.604
cme73102m001kpgoccu3avu2g	HIGHER_MANAGEMENT	reports:create	t	2025-08-11 12:21:51.646	2025-08-11 12:21:51.646
cme73103s001lpgocpswq34t6	MANAGER	reports:create	t	2025-08-11 12:21:51.689	2025-08-11 12:21:51.689
cme731050001mpgocqg13p2xt	SUPER_ADMIN	audit:read	t	2025-08-11 12:21:51.732	2025-08-11 12:21:51.732
cme731068001npgocmc7rekb3	HIGHER_MANAGEMENT	audit:read	t	2025-08-11 12:21:51.776	2025-08-11 12:21:51.776
cme73107f001opgocjxoiqm2u	MANAGER	audit:read	t	2025-08-11 12:21:51.819	2025-08-11 12:21:51.819
cme73108m001ppgocsj6iys3l	SUPER_ADMIN	settings:read	t	2025-08-11 12:21:51.863	2025-08-11 12:21:51.863
cme73109t001qpgoc1wxhtt1o	HIGHER_MANAGEMENT	settings:read	t	2025-08-11 12:21:51.905	2025-08-11 12:21:51.905
cme7310b0001rpgocw43auj8c	MANAGER	settings:read	t	2025-08-11 12:21:51.948	2025-08-11 12:21:51.948
cme7310c6001spgoc198n4gqo	SUPER_ADMIN	settings:update	t	2025-08-11 12:21:51.991	2025-08-11 12:21:51.991
cme7310de001tpgoctunrg4vv	SUPER_ADMIN	loan-types:create	t	2025-08-11 12:21:52.034	2025-08-11 12:21:52.034
cme7310el001upgocbg3l9buh	MANAGER	loan-types:create	t	2025-08-11 12:21:52.077	2025-08-11 12:21:52.077
cme7310fr001vpgocyiq7sdnc	SUPER_ADMIN	loan-types:read	t	2025-08-11 12:21:52.12	2025-08-11 12:21:52.12
cme7310gw001wpgoctz9jxm3w	HIGHER_MANAGEMENT	loan-types:read	t	2025-08-11 12:21:52.161	2025-08-11 12:21:52.161
cme7310i1001xpgocu0xqbt7a	MANAGER	loan-types:read	t	2025-08-11 12:21:52.202	2025-08-11 12:21:52.202
cme7310j8001ypgoclsu0w3kb	CREDIT_OFFICER	loan-types:read	t	2025-08-11 12:21:52.244	2025-08-11 12:21:52.244
cme7310ke001zpgoc04if1fqc	SUPER_ADMIN	loan-types:update	t	2025-08-11 12:21:52.287	2025-08-11 12:21:52.287
cme7310lm0020pgoc40yfu5wq	MANAGER	loan-types:update	t	2025-08-11 12:21:52.33	2025-08-11 12:21:52.33
cme7310mt0021pgocnl7oqs6d	SUPER_ADMIN	loan-types:delete	t	2025-08-11 12:21:52.373	2025-08-11 12:21:52.373
cme7310ny0022pgocquw6erkv	SUPER_ADMIN	documents:create	t	2025-08-11 12:21:52.415	2025-08-11 12:21:52.415
cme7310p40023pgoc0gbolzv7	MANAGER	documents:create	t	2025-08-11 12:21:52.456	2025-08-11 12:21:52.456
cme7310q90024pgocmchiupdp	CREDIT_OFFICER	documents:create	t	2025-08-11 12:21:52.498	2025-08-11 12:21:52.498
cme7310re0025pgocbddjzj51	SUPER_ADMIN	documents:read	t	2025-08-11 12:21:52.539	2025-08-11 12:21:52.539
cme7310sj0026pgocpgkiqnrn	HIGHER_MANAGEMENT	documents:read	t	2025-08-11 12:21:52.58	2025-08-11 12:21:52.58
cme7310to0027pgoc5lqkbgwf	MANAGER	documents:read	t	2025-08-11 12:21:52.621	2025-08-11 12:21:52.621
cme7310ut0028pgocklfnojtp	CREDIT_OFFICER	documents:read	t	2025-08-11 12:21:52.662	2025-08-11 12:21:52.662
cme7310vy0029pgochms8rtu8	CUSTOMER_SERVICE_OFFICER	documents:read	t	2025-08-11 12:21:52.703	2025-08-11 12:21:52.703
cme7310x4002apgocr4ietxgx	SUPER_ADMIN	documents:update	t	2025-08-11 12:21:52.744	2025-08-11 12:21:52.744
cme7310y9002bpgoctu6wu8an	MANAGER	documents:update	t	2025-08-11 12:21:52.786	2025-08-11 12:21:52.786
cme7310zf002cpgocaz2jq6ny	CREDIT_OFFICER	documents:update	t	2025-08-11 12:21:52.827	2025-08-11 12:21:52.827
cme73110l002dpgocj7swv8pj	SUPER_ADMIN	documents:delete	t	2025-08-11 12:21:52.87	2025-08-11 12:21:52.87
\.


--
-- TOC entry 3178 (class 0 OID 21363)
-- Dependencies: 232
-- Data for Name: system_config; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.system_config (id, key, value, description, category, "createdAt", "updatedAt") FROM stdin;
cme734jpi0000pgd0xzbm15rs	COMPANY_LOGO	/mainlogo.png	Company logo URL	COMPANY_SETTINGS	2025-08-11 12:24:37.021	2025-08-11 12:24:37.021
\.


--
-- TOC entry 3164 (class 0 OID 21229)
-- Dependencies: 218
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.users (id, email, password, "firstName", "lastName", phone, avatar, role, "isActive", "lastLogin", "createdAt", "updatedAt") FROM stdin;
cme72wfn50002pgv4i68hs59s	<EMAIL>	$2b$12$grjviJYpbJSS1gezkuvBJumjx1VM1SfkBcqo4xA8Xq/tIDl3puKzu	Priya	Silva	+94771234569	\N	MANAGER	t	\N	2025-08-11 12:18:18.546	2025-08-11 12:18:18.546
cme72wg4s0004pgv44dns0t7e	<EMAIL>	$2b$12$9gURRw9llTkpXqIgDFI/meUdVB/BnbvNhu7EAeRtwCXudfUAEz1mG	Nisha	Jayawardena	+94771234571	\N	CUSTOMER_SERVICE_OFFICER	t	\N	2025-08-11 12:18:19.181	2025-08-11 12:18:19.181
cme74sfm70008pgygc12mkhw0	<EMAIL>	$2b$12$eH8UMUXoJyt.mm8ICCJEzua5qbxe9id81K0Nn78hXU68eA83VI2xG	Shanaka	Sandaruwan	0745412360	\N	CREDIT_OFFICER	t	\N	2025-08-11 13:11:11.119	2025-08-11 13:11:11.119
cme72wfe90001pgv4nho1evg4	<EMAIL>	$2b$12$A6v3yQAihB6TqZSO34ZNVufygJm9BpcjdmQZDyGDXDV1GQ.a3q/jO	Rajesh	Fernando	+94771234568	\N	HIGHER_MANAGEMENT	t	2025-08-11 13:11:26.596	2025-08-11 12:18:18.225	2025-08-11 13:11:26.597
cme72wfw00003pgv4impxe3ur	<EMAIL>	$2b$12$ghzrHFxXUG4oZWZ8tttJgekvUyBwreHMC5bSzM0bPFQ5Rz75E2L12	Kamal	Perera	+94771234570	\N	CREDIT_OFFICER	t	2025-08-11 13:13:29.43	2025-08-11 12:18:18.864	2025-08-11 13:13:29.431
cme72wf400000pgv40dqaltm0	<EMAIL>	$2b$12$2Ys9vw6Tv3o9mmo9ZLqDJ.Lm4chIH81XXZiPV2c.M15IC5aGGBryq	Sangeeth	Thilakarathna	+94771234567	\N	SUPER_ADMIN	t	2025-08-11 13:13:44.712	2025-08-11 12:18:17.856	2025-08-11 13:13:44.713
\.


--
-- TOC entry 2933 (class 2606 OID 21043)
-- Name: _prisma_migrations _prisma_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public._prisma_migrations
    ADD CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id);


--
-- TOC entry 2967 (class 2606 OID 21362)
-- Name: audit_logs audit_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.audit_logs
    ADD CONSTRAINT audit_logs_pkey PRIMARY KEY (id);


--
-- TOC entry 2965 (class 2606 OID 21354)
-- Name: collection_items collection_items_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.collection_items
    ADD CONSTRAINT collection_items_pkey PRIMARY KEY (id);


--
-- TOC entry 2963 (class 2606 OID 21344)
-- Name: collections collections_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.collections
    ADD CONSTRAINT collections_pkey PRIMARY KEY (id);


--
-- TOC entry 2984 (class 2606 OID 21425)
-- Name: credit_officer_targets credit_officer_targets_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.credit_officer_targets
    ADD CONSTRAINT credit_officer_targets_pkey PRIMARY KEY (id);


--
-- TOC entry 2939 (class 2606 OID 21247)
-- Name: customers customers_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.customers
    ADD CONSTRAINT customers_pkey PRIMARY KEY (id);


--
-- TOC entry 2989 (class 2606 OID 21586)
-- Name: database_backups database_backups_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.database_backups
    ADD CONSTRAINT database_backups_pkey PRIMARY KEY (id);


--
-- TOC entry 2941 (class 2606 OID 21254)
-- Name: dependents dependents_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.dependents
    ADD CONSTRAINT dependents_pkey PRIMARY KEY (id);


--
-- TOC entry 2961 (class 2606 OID 21334)
-- Name: documents documents_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_pkey PRIMARY KEY (id);


--
-- TOC entry 2951 (class 2606 OID 21293)
-- Name: guarantor_documents guarantor_documents_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.guarantor_documents
    ADD CONSTRAINT guarantor_documents_pkey PRIMARY KEY (id);


--
-- TOC entry 2949 (class 2606 OID 21285)
-- Name: guarantors guarantors_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.guarantors
    ADD CONSTRAINT guarantors_pkey PRIMARY KEY (id);


--
-- TOC entry 2946 (class 2606 OID 21277)
-- Name: loan_approvals loan_approvals_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.loan_approvals
    ADD CONSTRAINT loan_approvals_pkey PRIMARY KEY (id);


--
-- TOC entry 2986 (class 2606 OID 21568)
-- Name: loan_comments loan_comments_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.loan_comments
    ADD CONSTRAINT loan_comments_pkey PRIMARY KEY (id);


--
-- TOC entry 2954 (class 2606 OID 21302)
-- Name: loan_guarantors loan_guarantors_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.loan_guarantors
    ADD CONSTRAINT loan_guarantors_pkey PRIMARY KEY (id);


--
-- TOC entry 2981 (class 2606 OID 21416)
-- Name: loan_types loan_types_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.loan_types
    ADD CONSTRAINT loan_types_pkey PRIMARY KEY (id);


--
-- TOC entry 2944 (class 2606 OID 21269)
-- Name: loans loans_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.loans
    ADD CONSTRAINT loans_pkey PRIMARY KEY (id);


--
-- TOC entry 2978 (class 2606 OID 21397)
-- Name: notifications notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_pkey PRIMARY KEY (id);


--
-- TOC entry 2959 (class 2606 OID 21324)
-- Name: payment_schedules payment_schedules_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.payment_schedules
    ADD CONSTRAINT payment_schedules_pkey PRIMARY KEY (id);


--
-- TOC entry 2956 (class 2606 OID 21313)
-- Name: payments payments_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT payments_pkey PRIMARY KEY (id);


--
-- TOC entry 2973 (class 2606 OID 21379)
-- Name: required_documents required_documents_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.required_documents
    ADD CONSTRAINT required_documents_pkey PRIMARY KEY (id);


--
-- TOC entry 2975 (class 2606 OID 21388)
-- Name: role_permissions role_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.role_permissions
    ADD CONSTRAINT role_permissions_pkey PRIMARY KEY (id);


--
-- TOC entry 2970 (class 2606 OID 21370)
-- Name: system_config system_config_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.system_config
    ADD CONSTRAINT system_config_pkey PRIMARY KEY (id);


--
-- TOC entry 2936 (class 2606 OID 21237)
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- TOC entry 2982 (class 1259 OID 21436)
-- Name: credit_officer_targets_creditOfficerId_month_year_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX "credit_officer_targets_creditOfficerId_month_year_key" ON public.credit_officer_targets USING btree ("creditOfficerId", month, year);


--
-- TOC entry 2937 (class 1259 OID 21427)
-- Name: customers_nationalId_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX "customers_nationalId_key" ON public.customers USING btree ("nationalId");


--
-- TOC entry 2987 (class 1259 OID 21587)
-- Name: database_backups_filename_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX database_backups_filename_key ON public.database_backups USING btree (filename);


--
-- TOC entry 2947 (class 1259 OID 21429)
-- Name: guarantors_nationalId_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX "guarantors_nationalId_key" ON public.guarantors USING btree ("nationalId");


--
-- TOC entry 2952 (class 1259 OID 21430)
-- Name: loan_guarantors_loanId_customerId_guarantorId_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX "loan_guarantors_loanId_customerId_guarantorId_key" ON public.loan_guarantors USING btree ("loanId", "customerId", "guarantorId");


--
-- TOC entry 2979 (class 1259 OID 21435)
-- Name: loan_types_name_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX loan_types_name_key ON public.loan_types USING btree (name);


--
-- TOC entry 2942 (class 1259 OID 21428)
-- Name: loans_loanNumber_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX "loans_loanNumber_key" ON public.loans USING btree ("loanNumber");


--
-- TOC entry 2957 (class 1259 OID 21431)
-- Name: payment_schedules_loanId_installmentNumber_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX "payment_schedules_loanId_installmentNumber_key" ON public.payment_schedules USING btree ("loanId", "installmentNumber");


--
-- TOC entry 2971 (class 1259 OID 21433)
-- Name: required_documents_name_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX required_documents_name_key ON public.required_documents USING btree (name);


--
-- TOC entry 2976 (class 1259 OID 21434)
-- Name: role_permissions_role_permission_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX role_permissions_role_permission_key ON public.role_permissions USING btree (role, permission);


--
-- TOC entry 2968 (class 1259 OID 21432)
-- Name: system_config_key_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX system_config_key_key ON public.system_config USING btree (key);


--
-- TOC entry 2934 (class 1259 OID 21426)
-- Name: users_email_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX users_email_key ON public.users USING btree (email);


--
-- TOC entry 3010 (class 2606 OID 21537)
-- Name: audit_logs audit_logs_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.audit_logs
    ADD CONSTRAINT "audit_logs_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- TOC entry 3009 (class 2606 OID 21532)
-- Name: collection_items collection_items_collectionId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.collection_items
    ADD CONSTRAINT "collection_items_collectionId_fkey" FOREIGN KEY ("collectionId") REFERENCES public.collections(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 3008 (class 2606 OID 21527)
-- Name: collections collections_assignedTo_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.collections
    ADD CONSTRAINT "collections_assignedTo_fkey" FOREIGN KEY ("assignedTo") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- TOC entry 3012 (class 2606 OID 21547)
-- Name: credit_officer_targets credit_officer_targets_creditOfficerId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.credit_officer_targets
    ADD CONSTRAINT "credit_officer_targets_creditOfficerId_fkey" FOREIGN KEY ("creditOfficerId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- TOC entry 3013 (class 2606 OID 21552)
-- Name: credit_officer_targets credit_officer_targets_setBy_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.credit_officer_targets
    ADD CONSTRAINT "credit_officer_targets_setBy_fkey" FOREIGN KEY ("setBy") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- TOC entry 2990 (class 2606 OID 21437)
-- Name: customers customers_assignedTo_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.customers
    ADD CONSTRAINT "customers_assignedTo_fkey" FOREIGN KEY ("assignedTo") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 3016 (class 2606 OID 21588)
-- Name: database_backups database_backups_createdBy_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.database_backups
    ADD CONSTRAINT "database_backups_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- TOC entry 2991 (class 2606 OID 21442)
-- Name: dependents dependents_customerId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.dependents
    ADD CONSTRAINT "dependents_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES public.customers(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 3006 (class 2606 OID 21517)
-- Name: documents documents_customerId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT "documents_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES public.customers(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 3007 (class 2606 OID 21522)
-- Name: documents documents_loanId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT "documents_loanId_fkey" FOREIGN KEY ("loanId") REFERENCES public.loans(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 2998 (class 2606 OID 21477)
-- Name: guarantor_documents guarantor_documents_guarantorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.guarantor_documents
    ADD CONSTRAINT "guarantor_documents_guarantorId_fkey" FOREIGN KEY ("guarantorId") REFERENCES public.guarantors(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 2996 (class 2606 OID 21472)
-- Name: loan_approvals loan_approvals_approvedBy_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.loan_approvals
    ADD CONSTRAINT "loan_approvals_approvedBy_fkey" FOREIGN KEY ("approvedBy") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- TOC entry 2997 (class 2606 OID 21467)
-- Name: loan_approvals loan_approvals_loanId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.loan_approvals
    ADD CONSTRAINT "loan_approvals_loanId_fkey" FOREIGN KEY ("loanId") REFERENCES public.loans(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- TOC entry 3014 (class 2606 OID 21569)
-- Name: loan_comments loan_comments_loanId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.loan_comments
    ADD CONSTRAINT "loan_comments_loanId_fkey" FOREIGN KEY ("loanId") REFERENCES public.loans(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 3015 (class 2606 OID 21574)
-- Name: loan_comments loan_comments_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.loan_comments
    ADD CONSTRAINT "loan_comments_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- TOC entry 2999 (class 2606 OID 21482)
-- Name: loan_guarantors loan_guarantors_customerId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.loan_guarantors
    ADD CONSTRAINT "loan_guarantors_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES public.customers(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 3000 (class 2606 OID 21487)
-- Name: loan_guarantors loan_guarantors_guarantorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.loan_guarantors
    ADD CONSTRAINT "loan_guarantors_guarantorId_fkey" FOREIGN KEY ("guarantorId") REFERENCES public.guarantors(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 3001 (class 2606 OID 21492)
-- Name: loan_guarantors loan_guarantors_loanId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.loan_guarantors
    ADD CONSTRAINT "loan_guarantors_loanId_fkey" FOREIGN KEY ("loanId") REFERENCES public.loans(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 3011 (class 2606 OID 21542)
-- Name: loan_types loan_types_createdBy_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.loan_types
    ADD CONSTRAINT "loan_types_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- TOC entry 2992 (class 2606 OID 21447)
-- Name: loans loans_createdBy_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.loans
    ADD CONSTRAINT "loans_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- TOC entry 2993 (class 2606 OID 21452)
-- Name: loans loans_customerId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.loans
    ADD CONSTRAINT "loans_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES public.customers(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- TOC entry 2994 (class 2606 OID 21462)
-- Name: loans loans_disbursedBy_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.loans
    ADD CONSTRAINT "loans_disbursedBy_fkey" FOREIGN KEY ("disbursedBy") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 2995 (class 2606 OID 21457)
-- Name: loans loans_loanTypeId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.loans
    ADD CONSTRAINT "loans_loanTypeId_fkey" FOREIGN KEY ("loanTypeId") REFERENCES public.loan_types(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- TOC entry 3005 (class 2606 OID 21512)
-- Name: payment_schedules payment_schedules_loanId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.payment_schedules
    ADD CONSTRAINT "payment_schedules_loanId_fkey" FOREIGN KEY ("loanId") REFERENCES public.loans(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 3002 (class 2606 OID 21507)
-- Name: payments payments_createdBy_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT "payments_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- TOC entry 3003 (class 2606 OID 21497)
-- Name: payments payments_loanId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT "payments_loanId_fkey" FOREIGN KEY ("loanId") REFERENCES public.loans(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- TOC entry 3004 (class 2606 OID 21502)
-- Name: payments payments_scheduleId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT "payments_scheduleId_fkey" FOREIGN KEY ("scheduleId") REFERENCES public.payment_schedules(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 3162 (class 6104 OID 16388)
-- Name: all_models; Type: PUBLICATION; Schema: -; Owner: postgres
--

CREATE PUBLICATION all_models FOR ALL TABLES WITH (publish = 'insert, update, delete, truncate');


ALTER PUBLICATION all_models OWNER TO postgres;

--
-- TOC entry 3194 (class 0 OID 0)
-- Dependencies: 5
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: prisma_migration
--

REVOKE USAGE ON SCHEMA public FROM PUBLIC;


-- Completed on 2025-08-11 18:43:56

--
-- PostgreSQL database dump complete
--

