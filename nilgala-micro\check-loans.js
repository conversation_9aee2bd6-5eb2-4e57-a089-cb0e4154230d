// Script to check existing loan statuses
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  try {
    console.log('=== Checking Loan Statuses ===')
    
    // Get all loans with their statuses
    const loans = await prisma.loan.findMany({
      select: {
        id: true,
        loanNumber: true,
        status: true,
        principalAmount: true,
        customer: {
          select: {
            firstName: true,
            lastName: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    })
    
    console.log('Recent loans:')
    loans.forEach(loan => {
      console.log(`- ${loan.loanNumber}: ${loan.status} - ${loan.customer.firstName} ${loan.customer.lastName} - LKR ${loan.principalAmount}`)
    })
    
    // Get status counts
    const statusCounts = await prisma.loan.groupBy({
      by: ['status'],
      _count: {
        id: true
      }
    })
    
    console.log('\nStatus counts:')
    statusCounts.forEach(item => {
      console.log(`- ${item.status}: ${item._count.id}`)
    })
    
    // Pick the first loan and set it to PENDING_MORE_INFO
    if (loans.length > 0) {
      const firstLoan = loans[0]
      console.log(`\nSetting ${firstLoan.loanNumber} to PENDING_MORE_INFO...`)
      
      const updatedLoan = await prisma.loan.update({
        where: { id: firstLoan.id },
        data: {
          status: 'PENDING_MORE_INFO'
        }
      })
      
      console.log('✅ Updated loan status to PENDING_MORE_INFO')
      console.log('Edit URL:', `http://localhost:3000/loans/${updatedLoan.id}/edit`)
    }
    
  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()
