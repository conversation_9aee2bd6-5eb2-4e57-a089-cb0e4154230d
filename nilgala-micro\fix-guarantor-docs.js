// Script to fix guarantor document file keys and provide migration info
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  try {
    console.log('=== Guarantor Document Migration Analysis ===')
    
    // Get all guarantor documents
    const documents = await prisma.guarantorDocument.findMany({
      include: {
        guarantor: {
          select: {
            firstName: true,
            lastName: true,
            nationalId: true
          }
        }
      },
      orderBy: {
        uploadedAt: 'desc'
      }
    })
    
    console.log(`Found ${documents.length} guarantor documents to analyze:`)
    console.log('')
    
    let oldFormatCount = 0
    let newFormatCount = 0
    
    documents.forEach((doc, index) => {
      const isOldFormat = doc.fileKey.includes('/') && !doc.fileKey.includes('_')
      const isNewFormat = doc.fileKey.includes('_') && doc.fileKey.startsWith('guarantor-documents/')
      
      if (isOldFormat) oldFormatCount++
      if (isNewFormat) newFormatCount++
      
      console.log(`${index + 1}. ${doc.documentName}`)
      console.log(`   Guarantor: ${doc.guarantor.firstName} ${doc.guarantor.lastName}`)
      console.log(`   File: ${doc.fileName}`)
      console.log(`   Key: ${doc.fileKey}`)
      console.log(`   Format: ${isOldFormat ? 'OLD (needs fixing)' : isNewFormat ? 'NEW (correct)' : 'UNKNOWN'}`)
      console.log(`   Size: ${Math.round(doc.fileSize / 1024)} KB`)
      console.log(`   Date: ${doc.uploadedAt.toLocaleDateString()}`)
      console.log('')
    })
    
    console.log('=== Summary ===')
    console.log(`Total documents: ${documents.length}`)
    console.log(`Old format (broken): ${oldFormatCount}`)
    console.log(`New format (working): ${newFormatCount}`)
    console.log(`Unknown format: ${documents.length - oldFormatCount - newFormatCount}`)
    console.log('')
    
    if (oldFormatCount > 0) {
      console.log('=== Recommended Actions ===')
      console.log('1. The old format documents have incorrect file keys and files were never uploaded to R2 storage')
      console.log('2. These documents need to be re-uploaded through the system')
      console.log('3. Users should be notified to re-upload missing guarantor documents')
      console.log('4. Consider implementing a bulk re-upload feature for administrators')
      console.log('')
      console.log('=== Technical Details ===')
      console.log('- Old format: guarantor-documents/{guarantorId}/{fileName}')
      console.log('- New format: guarantor-documents/{timestamp}_{randomId}.{extension}')
      console.log('- The parameter order fix will prevent this issue for new uploads')
    }
    
    if (newFormatCount > 0) {
      console.log('')
      console.log('=== Testing New Format Documents ===')
      const newFormatDocs = documents.filter(doc => 
        doc.fileKey.includes('_') && doc.fileKey.startsWith('guarantor-documents/')
      )
      
      if (newFormatDocs.length > 0) {
        const testDoc = newFormatDocs[0]
        console.log(`Test this working document: http://localhost:3000/api/guarantor-documents/${testDoc.id}/view`)
      }
    }
    
  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()
