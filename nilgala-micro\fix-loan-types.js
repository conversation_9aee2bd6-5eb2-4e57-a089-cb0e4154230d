// Script to check and fix loan type configurations
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  try {
    console.log('=== Checking Loan Types ===')
    
    // Get all loan types
    const loanTypes = await prisma.loanType.findMany()
    
    console.log('Current loan types:')
    loanTypes.forEach(type => {
      console.log(`- ${type.name}: tenureUnit=${type.tenureUnit}, defaultTenure=${type.defaultTenure}`)
    })
    
    console.log('\n=== Checking Problematic Loan ===')
    
    // Find the problematic loan
    const loan = await prisma.loan.findFirst({
      where: {
        loanNumber: 'LN1755100684237'
      },
      include: {
        loanType: true
      }
    })
    
    if (loan) {
      console.log('Found loan:', {
        loanNumber: loan.loanNumber,
        principalAmount: loan.principalAmount,
        interestRate: loan.interestRate,
        tenure: loan.tenure,
        repaymentFrequency: loan.repaymentFrequency,
        loanTypeName: loan.loanType.name,
        loanTypeTenureUnit: loan.loanType.tenureUnit
      })
      
      // Check payment schedules count
      const scheduleCount = await prisma.paymentSchedule.count({
        where: { loanId: loan.id }
      })
      
      console.log(`Payment schedules count: ${scheduleCount}`)
      
      if (scheduleCount > 100) {
        console.log('⚠️  This loan has too many payment schedules!')
        
        // Check if loan type should be DAYS instead of MONTHS
        if (loan.loanType.tenureUnit === 'MONTHS' && loan.repaymentFrequency === 'DAILY') {
          console.log('🔧 This loan type should probably use DAYS instead of MONTHS for daily loans')
          
          // Update the loan type to use DAYS
          const updatedLoanType = await prisma.loanType.update({
            where: { id: loan.loanType.id },
            data: {
              tenureUnit: 'DAYS',
              // Convert existing tenure values from months to days
              defaultTenure: loan.loanType.defaultTenure * 30,
              minTenure: loan.loanType.minTenure * 30,
              maxTenure: loan.loanType.maxTenure * 30
            }
          })
          
          console.log('✅ Updated loan type:', updatedLoanType.name)
          console.log('   - tenureUnit: MONTHS → DAYS')
          console.log(`   - defaultTenure: ${loan.loanType.defaultTenure} → ${updatedLoanType.defaultTenure}`)
          console.log(`   - minTenure: ${loan.loanType.minTenure} → ${updatedLoanType.minTenure}`)
          console.log(`   - maxTenure: ${loan.loanType.maxTenure} → ${updatedLoanType.maxTenure}`)
        }
      }
    } else {
      console.log('Loan LN1755100684237 not found')
    }
    
  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()
