import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Disable type checking during build
  typescript: {
    ignoreBuildErrors: true,
  },
  // Disable ESLint during build
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Optimize for production
  experimental: {
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
  },
  // Enable standalone output for better deployment
  output: 'standalone',
  // Handle dynamic routes better
  trailingSlash: false,
  // Disable static optimization for pages with dynamic content
  generateBuildId: async () => {
    return 'build-' + Date.now()
  },
};

export default nextConfig;
