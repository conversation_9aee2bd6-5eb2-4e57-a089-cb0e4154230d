{"name": "nilgala-micro", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:production": "cross-env NODE_ENV=production next build", "build:analyze": "cross-env ANALYZE=true next build", "start": "next start", "start:production": "cross-env NODE_ENV=production next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "seed": "tsx prisma/seed-system.ts", "seed:system": "tsx prisma/seed-system.ts", "seed:permissions": "tsx prisma/seed-permissions.ts", "seed:documents": "tsx prisma/seed-required-documents.ts", "seed:company": "tsx prisma/seed-company-settings.ts", "test:backup": "node scripts/test-backup-restore.cjs", "clean": "rimraf .next out", "export": "next export", "build:prod": "node scripts/build-production.js"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@aws-sdk/client-s3": "^3.859.0", "@aws-sdk/s3-request-presigner": "^3.859.0", "@hookform/resolvers": "^5.2.1", "@prisma/client": "^6.13.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-query": "^5.84.1", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.536.0", "next": "15.4.5", "next-auth": "^4.24.11", "prisma": "^6.13.0", "react": "19.1.0", "react-day-picker": "^9.8.1", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "recharts": "^3.1.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.4.5", "rimraf": "^6.0.1", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.6", "typescript": "^5"}}