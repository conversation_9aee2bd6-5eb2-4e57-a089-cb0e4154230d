generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                  String       @id @default(cuid())
  email               String       @unique
  password            String
  firstName           String
  lastName            String
  phone               String?
  avatar              String?
  role                UserRole
  isActive            Boolean      @default(true)
  lastLogin           DateTime?
  createdAt           DateTime     @default(now())
  updatedAt           DateTime     @updatedAt
  auditLogs           AuditLog[]
  assignedCollections Collection[]
  assignedCustomers   Customer[]     @relation("CustomerAssignedTo")
  createdLoans        Loan[]         @relation("LoanCreatedBy")
  disbursedLoans      Loan[]         @relation("LoanDisbursedBy")
  createdLoanTypes    LoanType[]     @relation("LoanTypeCreatedBy")
  loanApprovals       LoanApproval[] @relation("LoanApprovals")
  createdPayments     Payment[]      @relation("PaymentCreatedBy")
  creditOfficerTargets CreditOfficerTarget[] @relation("CreditOfficerTargets")
  setTargets          CreditOfficerTarget[] @relation("TargetSetBy")
  loanComments        LoanComment[]  @relation("LoanComments")
  databaseBackups     DatabaseBackup[] @relation("DatabaseBackups")

  @@map("users")
}

model Customer {
  id                 String          @id @default(cuid())
  firstName          String
  lastName           String
  dateOfBirth        DateTime
  gender             Gender
  maritalStatus      MaritalStatus
  nationalId         String          @unique
  phone              String
  additionalPhones   String[]        @default([])
  email              String?
  address            String
  city               String
  state              String
  postalCode         String
  gpsCoordinates     String?
  employmentType     EmploymentType
  employer           String?
  monthlyIncome      Decimal
  employmentDuration Int?
  bankAccount        String?
  bankName           String?
  creditScore        Int?
  existingLoans      Int             @default(0)
  status             CustomerStatus  @default(INACTIVE)
  statusUpdatedBy    String?
  statusUpdatedAt    DateTime?
  statusNotes        String?
  assignedTo         String?         // Credit Officer assigned to this customer
  createdAt          DateTime        @default(now())
  updatedAt          DateTime        @updatedAt
  dependents         Dependent[]
  documents          Document[]
  guarantorFor       LoanGuarantor[]
  loans              Loan[]
  assignedOfficer    User?           @relation("CustomerAssignedTo", fields: [assignedTo], references: [id])

  @@map("customers")
}

model Dependent {
  id           String   @id @default(cuid())
  customerId   String
  firstName    String
  lastName     String
  relationship String
  dateOfBirth  DateTime
  customer     Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@map("dependents")
}

model Loan {
  id                 String             @id @default(cuid())
  loanNumber         String             @unique
  customerId         String
  loanTypeId         String
  principalAmount    Decimal
  interestRate       Decimal
  tenure             Int
  repaymentFrequency RepaymentFrequency
  gracePeriod        Int                @default(0)
  processingFee      Decimal            @default(0)
  insuranceFee       Decimal            @default(0)
  otherCharges       Decimal            @default(0)
  totalAmount        Decimal
  emiAmount          Decimal
  totalInterest      Decimal
  status             LoanStatus         @default(DRAFT)
  applicationDate    DateTime           @default(now())
  approvalDate       DateTime?
  disbursementDate   DateTime?
  disbursedAmount    Decimal?
  disbursementMethod PaymentMethod?
  disbursementReference String?
  disbursementNotes  String?
  disbursedBy        String?
  disbursedAt        DateTime?
  maturityDate       DateTime?
  approvalLevel      Int                @default(0)
  approvedBy         String?
  approvedAt         DateTime?
  rejectedBy         String?
  rejectedAt         DateTime?
  rejectionReason    String?
  createdBy          String
  createdAt          DateTime           @default(now())
  updatedAt          DateTime           @updatedAt
  documents          Document[]
  guarantors         LoanGuarantor[]
  approvals          LoanApproval[]
  creator            User               @relation("LoanCreatedBy", fields: [createdBy], references: [id])
  customer           Customer           @relation(fields: [customerId], references: [id])
  loanType           LoanType           @relation("LoanTypeLoan", fields: [loanTypeId], references: [id])
  disburser          User?              @relation("LoanDisbursedBy", fields: [disbursedBy], references: [id])
  schedules          PaymentSchedule[]
  payments           Payment[]
  comments           LoanComment[]

  @@map("loans")
}

model LoanApproval {
  id            String   @id @default(cuid())
  loanId        String
  action        String   // APPROVE, REJECT, REQUEST_MORE_INFO
  notes         String?
  requestedInfo String?
  approvedBy    String
  createdAt     DateTime @default(now())
  loan          Loan     @relation(fields: [loanId], references: [id])
  approver      User     @relation("LoanApprovals", fields: [approvedBy], references: [id])

  @@map("loan_approvals")
}

model LoanComment {
  id        String   @id @default(cuid())
  loanId    String
  userId    String
  comment   String
  isInternal Boolean @default(false) // Internal comments only visible to staff
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  loan      Loan     @relation(fields: [loanId], references: [id], onDelete: Cascade)
  user      User     @relation("LoanComments", fields: [userId], references: [id])

  @@map("loan_comments")
}

model Guarantor {
  id                String               @id @default(cuid())
  firstName         String
  lastName          String
  nationalId        String               @unique
  phone             String
  email             String?
  address           String
  city              String
  state             String
  dateOfBirth       DateTime?
  occupation        String?
  monthlyIncome     Decimal?
  relationship      String?
  createdAt         DateTime             @default(now())
  updatedAt         DateTime             @updatedAt
  documents         GuarantorDocument[]
  loanGuarantors    LoanGuarantor[]

  @@map("guarantors")
}

model GuarantorDocument {
  id           String    @id @default(cuid())
  guarantorId  String
  documentName String
  documentType String
  fileName     String
  fileSize     Int
  fileKey      String
  uploadedAt   DateTime  @default(now())
  guarantor    Guarantor @relation(fields: [guarantorId], references: [id], onDelete: Cascade)

  @@map("guarantor_documents")
}

model LoanGuarantor {
  id                String     @id @default(cuid())
  loanId            String
  customerId        String?
  guarantorId       String?
  guarantorType     String
  liabilityAmount   Decimal
  guarantorStatus   String     @default("ACTIVE")
  verificationDate  DateTime?
  verificationNotes String?
  createdAt         DateTime   @default(now())
  updatedAt         DateTime   @updatedAt
  customer          Customer?  @relation(fields: [customerId], references: [id])
  guarantor         Guarantor? @relation(fields: [guarantorId], references: [id])
  loan              Loan       @relation(fields: [loanId], references: [id], onDelete: Cascade)

  @@unique([loanId, customerId, guarantorId], name: "loan_guarantors_unique_reference")
  @@map("loan_guarantors")
}

model Payment {
  id              String           @id @default(cuid())
  loanId          String
  scheduleId      String?
  amount          Decimal
  paymentDate     DateTime
  paymentMethod   PaymentMethod
  referenceNumber String?
  principalAmount Decimal
  interestAmount  Decimal
  penaltyAmount   Decimal          @default(0)
  feeAmount       Decimal          @default(0)
  status          PaymentStatus    @default(PENDING)
  processedBy     String?
  processedAt     DateTime?
  collectedBy     String?
  collectionDate  DateTime?
  receiptNumber   String?
  notes           String?
  createdBy       String
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  loan            Loan             @relation(fields: [loanId], references: [id])
  schedule        PaymentSchedule? @relation(fields: [scheduleId], references: [id])
  createdByUser   User             @relation("PaymentCreatedBy", fields: [createdBy], references: [id])

  @@map("payments")
}

model PaymentSchedule {
  id                String         @id @default(cuid())
  loanId            String
  installmentNumber Int
  dueDate           DateTime
  principalAmount   Decimal
  interestAmount    Decimal
  totalAmount       Decimal
  status            ScheduleStatus @default(PENDING)
  paidAmount        Decimal        @default(0)
  paidDate          DateTime?
  daysOverdue       Int            @default(0)
  penaltyAmount     Decimal        @default(0)
  loan              Loan           @relation(fields: [loanId], references: [id], onDelete: Cascade)
  payments          Payment[]

  @@unique([loanId, installmentNumber])
  @@map("payment_schedules")
}

model Document {
  id           String       @id @default(cuid())
  fileName     String
  originalName String
  fileSize     Int
  mimeType     String
  documentType DocumentType
  storageKey   String
  storageUrl   String?
  customerId   String?
  loanId       String?
  uploadedBy   String
  uploadedAt   DateTime     @default(now())
  verifiedBy   String?
  verifiedAt   DateTime?
  isVerified   Boolean      @default(false)
  notes        String?
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt
  customer     Customer?    @relation(fields: [customerId], references: [id], onDelete: Cascade)
  loan         Loan?        @relation(fields: [loanId], references: [id], onDelete: Cascade)

  @@map("documents")
}

model Collection {
  id             String           @id @default(cuid())
  assignedTo     String
  collectionDate DateTime
  route          String?
  area           String?
  status         String           @default("PENDING")
  startTime      DateTime?
  endTime        DateTime?
  totalCollected Decimal          @default(0)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  items          CollectionItem[]
  assignedUser   User             @relation(fields: [assignedTo], references: [id])

  @@map("collections")
}

model CollectionItem {
  id              String     @id @default(cuid())
  collectionId    String
  loanId          String
  expectedAmount  Decimal
  collectedAmount Decimal    @default(0)
  status          String     @default("PENDING")
  notes           String?
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt
  collection      Collection @relation(fields: [collectionId], references: [id], onDelete: Cascade)

  @@map("collection_items")
}

model AuditLog {
  id         String   @id @default(cuid())
  userId     String
  action     String
  resource   String
  resourceId String?
  oldValues  Json?
  newValues  Json?
  ipAddress  String?
  userAgent  String?
  timestamp  DateTime @default(now())
  user       User     @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}

model SystemConfig {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  description String?
  category    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("system_config")
}

model DatabaseBackup {
  id          String   @id @default(cuid())
  filename    String   @unique
  originalName String
  fileSize    BigInt
  backupType  String   // MANUAL, AUTOMATIC
  status      String   // CREATING, COMPLETED, FAILED
  filePath    String
  createdBy   String
  createdAt   DateTime @default(now())
  completedAt DateTime?
  errorMessage String?
  description String?
  user        User     @relation("DatabaseBackups", fields: [createdBy], references: [id])

  @@map("database_backups")
}

model RequiredDocument {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("required_documents")
}

model RolePermission {
  id         String   @id @default(cuid())
  role       UserRole
  permission String
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@unique([role, permission])
  @@map("role_permissions")
}

model Notification {
  id        String    @id @default(cuid())
  userId    String?
  title     String
  message   String
  type      String
  channel   String
  isRead    Boolean   @default(false)
  sentAt    DateTime?
  readAt    DateTime?
  metadata  Json?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  @@map("notifications")
}

model LoanType {
  id                    String                @id @default(cuid())
  name                  String                @unique
  description           String?
  category              LoanCategory          @default(PERSONAL)
  minAmount             Decimal
  maxAmount             Decimal
  defaultInterestRate   Decimal
  minInterestRate       Decimal
  maxInterestRate       Decimal
  defaultTenure         Int                   // in days
  minTenure             Int                   // in days
  maxTenure             Int                   // in days
  tenureUnit            TenureUnit            @default(MONTHS) // Days, Weeks, Months, Years
  collectionType        CollectionType        @default(MONTHLY)
  interestCalculationMethod InterestCalculationMethod @default(MONTHLY_INTEREST)
  processingFeeRate     Decimal               @default(0)
  insuranceFeeRate      Decimal               @default(0)
  gracePeriod           Int                   @default(0) // in days
  isActive              Boolean               @default(true)
  requiresGuarantor     Boolean               @default(false)
  maxGuarantors         Int                   @default(0)
  eligibilityCriteria   Json?                 // flexible criteria storage
  requiredDocuments     String[]              @default([]) // Array of required document IDs
  createdBy             String
  createdAt             DateTime              @default(now())
  updatedAt             DateTime              @updatedAt

  // Relations
  creator               User                  @relation("LoanTypeCreatedBy", fields: [createdBy], references: [id])
  loans                 Loan[]                @relation("LoanTypeLoan")

  @@map("loan_types")
}

enum Gender {
  MALE
  FEMALE
  OTHER
}

enum MaritalStatus {
  SINGLE
  MARRIED
  DIVORCED
  WIDOWED
}

enum EmploymentType {
  EMPLOYED
  SELF_EMPLOYED
  UNEMPLOYED
  RETIRED
  STUDENT
}



enum LoanStatus {
  DRAFT
  PENDING_APPROVAL
  PENDING_MORE_INFO
  APPROVED
  REJECTED
  DISBURSED
  ACTIVE
  COMPLETED
  DEFAULTED
  WRITTEN_OFF
}

enum RepaymentFrequency {
  DAILY
  WEEKLY
  MONTHLY
  QUARTERLY
}

enum PaymentMethod {
  CASH
  BANK_TRANSFER
  HAND_OVER_TO_OFFICER
  MOBILE_PAYMENT
  CHEQUE
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
}

enum ScheduleStatus {
  PENDING
  PAID
  OVERDUE
  PARTIAL
}

enum DocumentType {
  NATIONAL_ID
  PASSPORT
  DRIVING_LICENSE
  UTILITY_BILL
  BANK_STATEMENT
  INCOME_CERTIFICATE
  PROPERTY_DEED
  BUSINESS_REGISTRATION
  LOAN_APPLICATION
  GUARANTOR_ID
  OTHER
}

enum UserRole {
  SUPER_ADMIN
  HIGHER_MANAGEMENT
  MANAGER
  CREDIT_OFFICER
  CUSTOMER_SERVICE_OFFICER
}

enum LoanCategory {
  PERSONAL
  BUSINESS
  AGRICULTURAL
  EDUCATION
  HOUSING
  VEHICLE
  MICROFINANCE
  EMERGENCY
}

enum CollectionType {
  DAILY
  WEEKLY
  MONTHLY
  QUARTERLY
  YEARLY
}

enum TenureUnit {
  DAYS
  WEEKS
  MONTHS
  YEARS
}

enum InterestCalculationMethod {
  MONTHLY_INTEREST  // Method 1: Monthly Interest (Principal × Rate × Months) - Simple Interest
  COMPOUND_INTEREST // Method 2: Compound Interest EMI (Standard loan EMI calculation)
}

enum CustomerStatus {
  INACTIVE    // New customers, no active loans
  ACTIVE      // Customers with active loans
  SUSPENDED   // Temporarily suspended by admin
  BLACKLISTED // Permanently blacklisted by admin
}

model CreditOfficerTarget {
  id                String   @id @default(cuid())
  creditOfficerId   String
  month             Int      // 1-12
  year              Int
  loanTarget        Decimal  // Monthly loan disbursement target
  collectionTarget  Decimal  // Monthly collection target
  setBy             String   // User who set the target
  notes             String?
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  creditOfficer     User     @relation("CreditOfficerTargets", fields: [creditOfficerId], references: [id])
  setter            User     @relation("TargetSetBy", fields: [setBy], references: [id])

  @@unique([creditOfficerId, month, year])
  @@map("credit_officer_targets")
}
