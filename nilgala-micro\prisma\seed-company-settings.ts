import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedCompanySettings() {
  console.log('🏢 Seeding company settings...')

  const companySettings = [
    {
      key: 'COMPANY_NAME',
      value: 'Nilgala Micro Finance',
      description: 'Company name for documents and communications',
      category: 'COMPANY_SETTINGS'
    },
    {
      key: 'COMPANY_ADDRESS',
      value: 'Nilgala, Sri Lanka',
      description: 'Company address',
      category: 'COMPANY_SETTINGS'
    },
    {
      key: 'COMPANY_PHONE',
      value: '+94 XX XXX XXXX',
      description: 'Company phone number',
      category: 'COMPANY_SETTINGS'
    },
    {
      key: 'COMPANY_EMAIL',
      value: '<EMAIL>',
      description: 'Company email address',
      category: 'COMPANY_SETTINGS'
    },
    {
      key: 'COMPANY_WEBSITE',
      value: 'https://www.nilgalamicro.lk',
      description: 'Company website URL',
      category: 'COMPANY_SETTINGS'
    },
    {
      key: 'COMPANY_LOGO',
      value: '/logo-placeholder.svg',
      description: 'Company logo URL',
      category: 'COMPANY_SETTINGS'
    },
    {
      key: 'SYSTEM_TITLE',
      value: 'Nilgala Micro',
      description: 'System title displayed in header',
      category: 'COMPANY_SETTINGS'
    },
    {
      key: 'PRIMARY_COLOR',
      value: '#1f2937',
      description: 'Primary brand color',
      category: 'COMPANY_SETTINGS'
    },
    {
      key: 'SECONDARY_COLOR',
      value: '#3b82f6',
      description: 'Secondary brand color',
      category: 'COMPANY_SETTINGS'
    }
  ]

  for (const setting of companySettings) {
    await prisma.systemConfig.upsert({
      where: { key: setting.key },
      update: setting,
      create: setting
    })
  }

  console.log(`✅ Created ${companySettings.length} company settings`)
}

if (require.main === module) {
  seedCompanySettings()
    .catch((e) => {
      console.error(e)
      process.exit(1)
    })
    .finally(async () => {
      await prisma.$disconnect()
    })
}

export { seedCompanySettings }
