import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedLoanTypes() {
  // First, create a super admin user if not exists
  const superAdmin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      firstName: 'System',
      lastName: 'Admin',
      password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
      role: 'SUPER_ADMIN',
      isActive: true,
    }
  })

  const loanTypes = [
    {
      name: 'Daily Collection Personal Loan',
      description: 'Personal loan with daily collection for quick cash needs',
      category: 'PERSONAL' as const,
      minAmount: 5000,
      maxAmount: 100000,
      defaultInterestRate: 15.0,
      minInterestRate: 12.0,
      maxInterestRate: 20.0,
      defaultTenure: 90, // 3 months in days
      minTenure: 30,
      maxTenure: 365,
      collectionType: 'DAILY' as const,
      processingFeeRate: 2.0,
      gracePeriod: 3,
      requiresGuarantor: false,
      createdBy: superAdmin.id
    },
    {
      name: 'Weekly Collection Business Loan',
      description: 'Business loan with weekly collection for small businesses',
      category: 'BUSINESS' as const,
      minAmount: 25000,
      maxAmount: 500000,
      defaultInterestRate: 18.0,
      minInterestRate: 15.0,
      maxInterestRate: 25.0,
      defaultTenure: 180, // 6 months in days
      minTenure: 90,
      maxTenure: 730,
      collectionType: 'WEEKLY' as const,
      processingFeeRate: 3.0,
      gracePeriod: 7,
      requiresGuarantor: true,
      maxGuarantors: 2,
      createdBy: superAdmin.id
    },
    {
      name: 'Monthly Collection Agricultural Loan',
      description: 'Agricultural loan with monthly collection for farming activities',
      category: 'AGRICULTURAL' as const,
      minAmount: 50000,
      maxAmount: 1000000,
      defaultInterestRate: 12.0,
      minInterestRate: 10.0,
      maxInterestRate: 18.0,
      defaultTenure: 365, // 1 year in days
      minTenure: 180,
      maxTenure: 1095, // 3 years
      collectionType: 'MONTHLY' as const,
      processingFeeRate: 1.5,
      gracePeriod: 30,
      requiresGuarantor: true,
      maxGuarantors: 1,
      createdBy: superAdmin.id
    },
    {
      name: 'Emergency Loan - Daily Collection',
      description: 'Quick emergency loan with daily collection',
      category: 'EMERGENCY' as const,
      minAmount: 2000,
      maxAmount: 50000,
      defaultInterestRate: 20.0,
      minInterestRate: 18.0,
      maxInterestRate: 25.0,
      defaultTenure: 30, // 1 month
      minTenure: 15,
      maxTenure: 90,
      collectionType: 'DAILY' as const,
      processingFeeRate: 5.0,
      gracePeriod: 1,
      requiresGuarantor: false,
      createdBy: superAdmin.id
    },
    {
      name: 'Housing Loan - Monthly Collection',
      description: 'Housing loan with monthly collection for home improvements',
      category: 'HOUSING' as const,
      minAmount: 100000,
      maxAmount: 2000000,
      defaultInterestRate: 14.0,
      minInterestRate: 12.0,
      maxInterestRate: 18.0,
      defaultTenure: 1825, // 5 years in days
      minTenure: 365,
      maxTenure: 3650, // 10 years
      collectionType: 'MONTHLY' as const,
      processingFeeRate: 2.0,
      gracePeriod: 30,
      requiresGuarantor: true,
      maxGuarantors: 2,
      createdBy: superAdmin.id
    }
  ]

  for (const loanType of loanTypes) {
    await prisma.loanType.upsert({
      where: { name: loanType.name },
      update: loanType,
      create: loanType
    })
  }

  console.log('Loan types seeded successfully!')
}

seedLoanTypes()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
