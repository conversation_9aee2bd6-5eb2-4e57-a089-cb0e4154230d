import { PrismaClient, UserRole } from '@prisma/client'

const prisma = new PrismaClient()

const permissions = [
  // Loan permissions
  { permission: 'loans:create', roles: ['SUPER_ADMIN', 'MANAGER', 'CREDIT_OFFICER'] },
  { permission: 'loans:read', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGE<PERSON>', 'CREDIT_OFFICER', 'CUSTOMER_SERVICE_OFFICER'] },
  { permission: 'loans:update', roles: ['SUPER_ADMIN', 'MANAGER', 'CREDIT_OFFICER'] },
  { permission: 'loans:delete', roles: ['SUPER_ADMIN'] },
  { permission: 'loans:approve', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT'] },
  { permission: 'loans:disburse', roles: ['SUPER_ADMIN', 'MANAGER', 'CREDIT_OFFICER'] },

  // Customer permissions
  { permission: 'customers:create', roles: ['SUPER_ADMIN', '<PERSON><PERSON><PERSON><PERSON>', 'CREDIT_OFFICER'] },
  { permission: 'customers:read', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER', 'CREDIT_OFFICER', 'CUSTOMER_SERVICE_OFFICER'] },
  { permission: 'customers:update', roles: ['SUPER_ADMIN', 'MANAGER', 'CREDIT_OFFICER'] },
  { permission: 'customers:delete', roles: ['SUPER_ADMIN'] },

  // Payment permissions
  { permission: 'payments:create', roles: ['SUPER_ADMIN', 'MANAGER', 'CREDIT_OFFICER'] },
  { permission: 'payments:read', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER', 'CREDIT_OFFICER', 'CUSTOMER_SERVICE_OFFICER'] },
  { permission: 'payments:update', roles: ['SUPER_ADMIN', 'MANAGER'] },
  { permission: 'payments:delete', roles: ['SUPER_ADMIN'] },

  // User management permissions
  { permission: 'users:create', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT'] },
  { permission: 'users:read', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER'] },
  { permission: 'users:update', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT'] },
  { permission: 'users:delete', roles: ['SUPER_ADMIN'] },

  // Reports permissions
  { permission: 'reports:read', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER', 'CREDIT_OFFICER'] },
  { permission: 'reports:export', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER'] },
  { permission: 'reports:create', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER'] },

  // Audit permissions
  { permission: 'audit:read', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER'] },

  // Settings permissions
  { permission: 'settings:read', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER'] },
  { permission: 'settings:update', roles: ['SUPER_ADMIN'] },

  // Loan types permissions
  { permission: 'loan-types:create', roles: ['SUPER_ADMIN', 'MANAGER'] },
  { permission: 'loan-types:read', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER', 'CREDIT_OFFICER'] },
  { permission: 'loan-types:update', roles: ['SUPER_ADMIN', 'MANAGER'] },
  { permission: 'loan-types:delete', roles: ['SUPER_ADMIN'] },

  // Document permissions
  { permission: 'documents:create', roles: ['SUPER_ADMIN', 'MANAGER', 'CREDIT_OFFICER'] },
  { permission: 'documents:read', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER', 'CREDIT_OFFICER', 'CUSTOMER_SERVICE_OFFICER'] },
  { permission: 'documents:update', roles: ['SUPER_ADMIN', 'MANAGER', 'CREDIT_OFFICER'] },
  { permission: 'documents:delete', roles: ['SUPER_ADMIN'] },
]

async function seedPermissions() {
  console.log('🌱 Seeding permissions...')

  try {
    // Clear existing permissions
    await prisma.rolePermission.deleteMany()
    console.log('✅ Cleared existing permissions')

    // Add new permissions
    for (const { permission, roles } of permissions) {
      for (const role of roles) {
        await prisma.rolePermission.create({
          data: {
            role: role as UserRole,
            permission,
            isActive: true
          }
        })
      }
    }

    console.log(`✅ Seeded ${permissions.length} permissions for all roles`)
    
    // Display summary
    const permissionCounts = await prisma.rolePermission.groupBy({
      by: ['role'],
      _count: { permission: true }
    })
    
    console.log('\n📊 Permission Summary:')
    for (const { role, _count } of permissionCounts) {
      console.log(`  ${role}: ${_count.permission} permissions`)
    }

  } catch (error) {
    console.error('❌ Error seeding permissions:', error)
    throw error
  }
}

async function main() {
  await seedPermissions()
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
