import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedRequiredDocuments() {
  console.log('🗂️ Seeding required documents...')

  const documents = [
    {
      name: 'NIC (National Identity Card)',
      description: 'Copy of National Identity Card - front and back',
      isActive: true
    },
    {
      name: 'Address Proof',
      description: 'Utility bill, bank statement, or other address verification document',
      isActive: true
    },
    {
      name: 'Bank Statements',
      description: 'Last 3 months bank statements',
      isActive: true
    },
    {
      name: 'Business Registration',
      description: 'Business registration certificate (for business loans)',
      isActive: true
    },
    {
      name: 'Income Certificate',
      description: 'Salary certificate or income verification letter',
      isActive: true
    },
    {
      name: 'Guarantor NIC',
      description: 'Copy of guarantor\'s National Identity Card',
      isActive: true
    },
    {
      name: 'Property Documents',
      description: 'Land deed or property ownership documents (for collateral)',
      isActive: true
    },
    {
      name: 'Employment Letter',
      description: 'Letter from employer confirming employment',
      isActive: true
    },
    {
      name: 'Tax Returns',
      description: 'Last year\'s tax return documents',
      isActive: true
    },
    {
      name: 'Passport Photos',
      description: '2 recent passport-size photographs',
      isActive: true
    }
  ]

  for (const doc of documents) {
    await prisma.requiredDocument.upsert({
      where: { name: doc.name },
      update: doc,
      create: doc
    })
  }

  console.log(`✅ Created ${documents.length} required documents`)
}

if (require.main === module) {
  seedRequiredDocuments()
    .catch((e) => {
      console.error(e)
      process.exit(1)
    })
    .finally(async () => {
      await prisma.$disconnect()
    })
}

export { seedRequiredDocuments }
