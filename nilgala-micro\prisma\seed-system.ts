import { PrismaClient, UserRole } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function seedSystemData() {
  console.log('🌱 Starting system data seeding...')
  console.log('⚠️  This will create essential system data only (no dummy customers/loans)')

  try {
    // 1. Create Super Admin User
    console.log('👤 Creating Super Admin user...')
    
    const hashedPassword = await bcrypt.hash('admin123', 10)
    
    const existingAdmin = await prisma.user.findFirst({
      where: { role: 'SUPER_ADMIN' }
    })

    let adminUser
    if (!existingAdmin) {
      adminUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          firstName: 'System',
          lastName: 'Administrator',
          password: hashedPassword,
          role: UserRole.SUPER_ADMIN,
          phone: '+94771234567',
          isActive: true,
        }
      })
      console.log('✅ Created Super Admin user: <EMAIL> / admin123')
    } else {
      adminUser = existingAdmin
      console.log('✅ Super Admin user already exists')
    }

    // 2. Seed System Permissions
    console.log('🔐 Seeding system permissions...')
    
    const permissions = [
      // Loan permissions
      { permission: 'loans:create', roles: ['SUPER_ADMIN', 'MANAGER', 'CREDIT_OFFICER'] },
      { permission: 'loans:read', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER', 'CREDIT_OFFICER', 'CUSTOMER_SERVICE_OFFICER'] },
      { permission: 'loans:update', roles: ['SUPER_ADMIN', 'MANAGER', 'CREDIT_OFFICER'] },
      { permission: 'loans:delete', roles: ['SUPER_ADMIN'] },
      { permission: 'loans:approve', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT'] },
      { permission: 'loans:disburse', roles: ['SUPER_ADMIN', 'MANAGER', 'CREDIT_OFFICER'] },

      // Customer permissions
      { permission: 'customers:create', roles: ['SUPER_ADMIN', 'MANAGER', 'CREDIT_OFFICER'] },
      { permission: 'customers:read', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER', 'CREDIT_OFFICER', 'CUSTOMER_SERVICE_OFFICER'] },
      { permission: 'customers:update', roles: ['SUPER_ADMIN', 'MANAGER', 'CREDIT_OFFICER'] },
      { permission: 'customers:delete', roles: ['SUPER_ADMIN'] },

      // Payment permissions
      { permission: 'payments:create', roles: ['SUPER_ADMIN', 'MANAGER', 'CREDIT_OFFICER'] },
      { permission: 'payments:read', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER', 'CREDIT_OFFICER', 'CUSTOMER_SERVICE_OFFICER'] },
      { permission: 'payments:update', roles: ['SUPER_ADMIN', 'MANAGER'] },
      { permission: 'payments:delete', roles: ['SUPER_ADMIN'] },

      // User management permissions
      { permission: 'users:create', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT'] },
      { permission: 'users:read', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER'] },
      { permission: 'users:update', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT'] },
      { permission: 'users:delete', roles: ['SUPER_ADMIN'] },

      // Reports permissions
      { permission: 'reports:read', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER', 'CREDIT_OFFICER'] },
      { permission: 'reports:export', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER'] },
      { permission: 'reports:create', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER'] },

      // Audit permissions
      { permission: 'audit:read', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER'] },

      // Settings permissions
      { permission: 'settings:read', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER'] },
      { permission: 'settings:update', roles: ['SUPER_ADMIN'] },

      // Loan types permissions
      { permission: 'loan-types:create', roles: ['SUPER_ADMIN', 'MANAGER'] },
      { permission: 'loan-types:read', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER', 'CREDIT_OFFICER'] },
      { permission: 'loan-types:update', roles: ['SUPER_ADMIN', 'MANAGER'] },
      { permission: 'loan-types:delete', roles: ['SUPER_ADMIN'] },

      // Document permissions
      { permission: 'documents:create', roles: ['SUPER_ADMIN', 'MANAGER', 'CREDIT_OFFICER'] },
      { permission: 'documents:read', roles: ['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER', 'CREDIT_OFFICER', 'CUSTOMER_SERVICE_OFFICER'] },
      { permission: 'documents:update', roles: ['SUPER_ADMIN', 'MANAGER', 'CREDIT_OFFICER'] },
      { permission: 'documents:delete', roles: ['SUPER_ADMIN', 'MANAGER'] },
    ]

    // Clear existing permissions
    await prisma.rolePermission.deleteMany()

    // Create permissions
    for (const permissionData of permissions) {
      for (const role of permissionData.roles) {
        await prisma.rolePermission.create({
          data: {
            role: role as UserRole,
            permission: permissionData.permission,
            isActive: true
          }
        })
      }
    }

    console.log(`✅ Created ${permissions.length} permission types for all roles`)

    // 3. Seed Required Documents
    console.log('📄 Seeding required documents...')
    
    const documents = [
      {
        name: 'NIC (National Identity Card)',
        description: 'Copy of National Identity Card - front and back',
        isActive: true
      },
      {
        name: 'Address Proof',
        description: 'Utility bill, bank statement, or other address verification document',
        isActive: true
      },
      {
        name: 'Bank Statements',
        description: 'Last 3 months bank statements',
        isActive: true
      },
      {
        name: 'Business Registration',
        description: 'Business registration certificate (for business loans)',
        isActive: true
      },
      {
        name: 'Income Certificate',
        description: 'Salary certificate or income verification letter',
        isActive: true
      },
      {
        name: 'Guarantor NIC',
        description: 'Copy of guarantor\'s National Identity Card',
        isActive: true
      },
      {
        name: 'Property Documents',
        description: 'Land deed or property ownership documents (for collateral)',
        isActive: true
      },
      {
        name: 'Employment Letter',
        description: 'Letter from employer confirming employment',
        isActive: true
      },
      {
        name: 'Tax Returns',
        description: 'Last year tax returns (for high-value loans)',
        isActive: true
      },
      {
        name: 'Business License',
        description: 'Valid business license (for business loans)',
        isActive: true
      }
    ]

    // Clear existing documents
    await prisma.requiredDocument.deleteMany()

    // Create documents
    for (const doc of documents) {
      await prisma.requiredDocument.create({
        data: doc
      })
    }

    console.log(`✅ Created ${documents.length} required document types`)

    // 4. Seed System Configuration
    console.log('⚙️ Seeding system configuration...')

    const systemConfigs = [
      {
        key: 'COMPANY_NAME',
        value: 'Nilgala Micro Finance',
        description: 'Company name for documents and communications',
        category: 'COMPANY_SETTINGS'
      },
      {
        key: 'COMPANY_ADDRESS',
        value: 'Nilgala, Sri Lanka',
        description: 'Company address',
        category: 'COMPANY_SETTINGS'
      },
      {
        key: 'COMPANY_PHONE',
        value: '+94 XX XXX XXXX',
        description: 'Company phone number',
        category: 'COMPANY_SETTINGS'
      },
      {
        key: 'COMPANY_EMAIL',
        value: '<EMAIL>',
        description: 'Company email address',
        category: 'COMPANY_SETTINGS'
      },
      {
        key: 'SYSTEM_TITLE',
        value: 'Nilgala Micro',
        description: 'System title displayed in header',
        category: 'COMPANY_SETTINGS'
      },
      {
        key: 'manager_monthly_target',
        value: '200000',
        description: 'Default monthly target for managers',
        category: 'TARGETS'
      },
      {
        key: 'credit_officer_monthly_target',
        value: '15',
        description: 'Default monthly loan target for credit officers',
        category: 'TARGETS'
      },
      {
        key: 'max_loan_amount',
        value: '1000000',
        description: 'Maximum loan amount allowed',
        category: 'LOAN_SETTINGS'
      },
      {
        key: 'min_loan_amount',
        value: '5000',
        description: 'Minimum loan amount allowed',
        category: 'LOAN_SETTINGS'
      }
    ]

    // Clear existing system configs
    await prisma.systemConfig.deleteMany()

    // Create system configs
    for (const config of systemConfigs) {
      await prisma.systemConfig.create({
        data: config
      })
    }

    console.log(`✅ Created ${systemConfigs.length} system configuration settings`)

    console.log('🎉 System data seeding completed successfully!')
    console.log('\n📋 System Access:')
    console.log('Super Admin: <EMAIL> / admin123')
    console.log('\n📝 Next Steps:')
    console.log('1. Login as Super Admin')
    console.log('2. Create additional users (Higher Management, Manager, Credit Officers)')
    console.log('3. Create loan types as needed')
    console.log('4. Start adding real customers and loans')
    console.log('\n🗂️ System Data Created:')
    console.log(`• ${permissions.length} permission types across all roles`)
    console.log(`• ${documents.length} required document types`)
    console.log(`• ${systemConfigs.length} system configuration settings`)
    console.log('• 1 Super Admin user account')

  } catch (error) {
    console.error('❌ Error seeding system data:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

seedSystemData()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
