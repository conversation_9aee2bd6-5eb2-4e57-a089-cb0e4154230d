import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🔄 Updating existing customer statuses...')

  // Update all existing customers to have INACTIVE status by default
  const result = await prisma.customer.updateMany({
    data: {
      status: 'INACTIVE'
    }
  })

  console.log(`✅ Updated ${result.count} customers to INACTIVE status`)

  // Now check for customers with active loans and update their status to ACTIVE
  const customersWithActiveLoans = await prisma.customer.findMany({
    where: {
      loans: {
        some: {
          status: {
            in: ['ACTIVE', 'DISBURSED']
          }
        }
      }
    },
    select: {
      id: true,
      firstName: true,
      lastName: true
    }
  })

  if (customersWithActiveLoans.length > 0) {
    const customerIds = customersWithActiveLoans.map(c => c.id)
    
    const activeResult = await prisma.customer.updateMany({
      where: {
        id: {
          in: customerIds
        }
      },
      data: {
        status: 'ACTIVE'
      }
    })

    console.log(`✅ Updated ${activeResult.count} customers with active loans to ACTIVE status`)
    
    customersWithActiveLoans.forEach(customer => {
      console.log(`  - ${customer.firstName} ${customer.lastName} → ACTIVE`)
    })
  } else {
    console.log('ℹ️  No customers with active loans found')
  }

  // Summary
  const statusCounts = await prisma.customer.groupBy({
    by: ['status'],
    _count: {
      id: true
    }
  })

  console.log('\n📊 Customer Status Summary:')
  statusCounts.forEach(({ status, _count }) => {
    console.log(`  ${status}: ${_count.id} customers`)
  })
}

main()
  .catch((e) => {
    console.error('❌ Error updating customer statuses:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
