// Script to regenerate payment schedule for the problematic loan
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

// Import the interest calculation functions
function convertTenureToDays(tenure, unit) {
  switch (unit) {
    case 'DAYS': return tenure
    case 'WEEKS': return tenure * 7
    case 'MONTHS': return tenure * 30
    case 'YEARS': return tenure * 365
    default: return tenure
  }
}

function calculateMonthlyInterest(input) {
  const { principalAmount, interestRate, tenureInDays, collectionType } = input

  // Convert tenure to months (30 days = 1 month)
  const tenureInMonths = tenureInDays / 30

  // Calculate total interest: Principal × Monthly Rate × Months
  const totalInterest = principalAmount * (interestRate / 100) * tenureInMonths
  
  // Total amount to be repaid
  const totalAmount = principalAmount + totalInterest
  
  // Calculate number of payments based on collection type
  let numberOfPayments
  switch (collectionType) {
    case 'DAILY':
      numberOfPayments = tenureInDays
      break
    case 'WEEKLY':
      numberOfPayments = Math.ceil(tenureInDays / 7)
      break
    case 'MONTHLY':
      numberOfPayments = Math.ceil(tenureInMonths)
      break
    case 'QUARTERLY':
      numberOfPayments = Math.ceil(tenureInMonths / 3)
      break
    case 'YEARLY':
      numberOfPayments = Math.ceil(tenureInMonths / 12)
      break
    default:
      numberOfPayments = tenureInDays
  }
  
  // EMI = Total Amount / Number of Payments
  const emiAmount = totalAmount / numberOfPayments
  
  return {
    totalInterest,
    totalAmount,
    emiAmount,
    numberOfPayments
  }
}

function getNextDueDate(startDate, frequency, installmentNumber) {
  const date = new Date(startDate)
  
  switch (frequency) {
    case 'DAILY':
      date.setDate(date.getDate() + installmentNumber)
      return date
    case 'WEEKLY':
      const weeklyDate = new Date(date)
      weeklyDate.setDate(weeklyDate.getDate() + (installmentNumber * 7))
      return weeklyDate
    case 'MONTHLY':
      const monthlyDate = new Date(date)
      monthlyDate.setMonth(monthlyDate.getMonth() + installmentNumber)
      return monthlyDate
    default:
      const defaultDate = new Date(date)
      defaultDate.setMonth(defaultDate.getMonth() + installmentNumber)
      return defaultDate
  }
}

function generatePaymentSchedule(loan) {
  const principal = Number(loan.principalAmount)
  const interestRate = Number(loan.interestRate)
  const tenure = Number(loan.tenure)
  
  // Convert tenure to days
  const tenureInDays = convertTenureToDays(tenure, loan.tenureUnit)
  
  console.log('Schedule generation parameters:', {
    principal,
    interestRate,
    tenure,
    tenureUnit: loan.tenureUnit,
    tenureInDays,
    repaymentFrequency: loan.repaymentFrequency
  })
  
  // Calculate loan details using Monthly Interest method
  const calculation = calculateMonthlyInterest({
    principalAmount: principal,
    interestRate: interestRate,
    tenureInDays: tenureInDays,
    collectionType: loan.repaymentFrequency
  })
  
  console.log('Calculation result:', calculation)
  
  const schedule = []
  const startDate = new Date(loan.disbursementDate || new Date())
  
  // For monthly interest, each payment is the same amount
  const installmentAmount = calculation.emiAmount
  const totalInterest = calculation.totalInterest
  const numberOfPayments = calculation.numberOfPayments
  
  // Distribute interest evenly across payments
  const interestPerPayment = totalInterest / numberOfPayments
  const principalPerPayment = principal / numberOfPayments
  
  for (let i = 1; i <= numberOfPayments; i++) {
    const dueDate = getNextDueDate(startDate, loan.repaymentFrequency, i)
    
    schedule.push({
      installmentNumber: i,
      dueDate,
      principalAmount: Math.round(principalPerPayment * 100) / 100,
      interestAmount: Math.round(interestPerPayment * 100) / 100,
      totalAmount: Math.round(installmentAmount * 100) / 100,
      paidAmount: 0,
      status: 'PENDING'
    })
  }
  
  return schedule
}

async function main() {
  try {
    console.log('=== Regenerating Payment Schedule ===')
    
    // Find the problematic loan
    const loan = await prisma.loan.findFirst({
      where: {
        loanNumber: 'LN1755100684237'
      },
      include: {
        loanType: true
      }
    })
    
    if (!loan) {
      console.log('Loan not found')
      return
    }
    
    console.log('Found loan:', loan.loanNumber)
    
    // Delete existing payment schedules
    const deletedCount = await prisma.paymentSchedule.deleteMany({
      where: { loanId: loan.id }
    })
    
    console.log(`Deleted ${deletedCount.count} existing payment schedules`)
    
    // Create loan data for schedule generation
    const loanData = {
      principalAmount: loan.principalAmount,
      interestRate: loan.interestRate,
      tenure: loan.tenure,
      tenureUnit: loan.loanType.tenureUnit,
      repaymentFrequency: loan.repaymentFrequency,
      disbursementDate: loan.disbursementDate
    }
    
    // Generate new schedule
    const schedule = generatePaymentSchedule(loanData)
    
    console.log(`Generated ${schedule.length} payment schedules`)
    console.log('First payment:', schedule[0])
    console.log('Last payment:', schedule[schedule.length - 1])
    
    // Save new schedules to database
    await prisma.paymentSchedule.createMany({
      data: schedule.map(item => ({
        loanId: loan.id,
        installmentNumber: item.installmentNumber,
        dueDate: item.dueDate,
        principalAmount: item.principalAmount,
        interestAmount: item.interestAmount,
        totalAmount: item.totalAmount,
        status: 'PENDING'
      }))
    })
    
    console.log('✅ Payment schedule regenerated successfully!')
    
    // Verify the new count
    const newCount = await prisma.paymentSchedule.count({
      where: { loanId: loan.id }
    })
    
    console.log(`New payment schedules count: ${newCount}`)
    
  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()
