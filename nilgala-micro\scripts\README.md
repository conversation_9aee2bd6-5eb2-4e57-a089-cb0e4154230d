# Database Cleanup Scripts

This directory contains scripts to safely clear the database while preserving essential system configuration.

## 📋 What Gets Preserved

All scripts preserve these essential system components:
- ✅ **User Roles** (SUPER_ADMIN, HIGHER_MANAGEMENT, MANAGER, etc.)
- ✅ **Permissions** (all permission definitions)
- ✅ **Required Documents** (document type definitions)
- ✅ **Super Admin user accounts**

## 🗑️ What Gets Deleted

All transactional data is removed:
- ❌ All customer records
- ❌ All loan applications and records
- ❌ All payment records and schedules
- ❌ All guarantor data and documents
- ❌ All uploaded documents
- ❌ All loan types (can be recreated)
- ❌ All credit officer targets
- ❌ All audit logs
- ❌ All non-admin users
- ❌ Company settings (will use defaults)

## 🛠️ Available Scripts

### 1. Simple Cleanup (Recommended)
```bash
node scripts/clear-database-simple.js
```
- **Best for**: Quick cleanup during development
- **Features**: Fast, straightforward deletion
- **Safety**: Basic error handling

### 2. Safe Cleanup with Backup
```bash
node scripts/clear-database-safe.js
```
- **Best for**: Production or important data
- **Features**: 
  - Creates automatic PostgreSQL backup
  - User confirmation prompts
  - Detailed progress reporting
  - Backup file location provided
- **Requirements**: `pg_dump` must be installed and accessible

### 3. Full Cleanup with R2 Storage
```bash
node scripts/clear-database.js
```
- **Best for**: Complete system reset
- **Features**:
  - Clears database AND R2 file storage
  - User confirmation prompts
  - Detailed logging
- **Requirements**: R2 credentials in environment variables

## 🚀 Usage Instructions

### Prerequisites
1. Make sure your `.env` file is properly configured
2. For backup scripts, ensure PostgreSQL tools are installed
3. For R2 cleanup, ensure Cloudflare R2 credentials are set

### Running a Script
1. Navigate to the project root directory
2. Choose the appropriate script based on your needs
3. Run the script using Node.js
4. Follow the prompts (for interactive scripts)

### Example Usage
```bash
# Quick cleanup for development
node scripts/clear-database-simple.js

# Safe cleanup with backup
node scripts/clear-database-safe.js

# Complete cleanup including files
node scripts/clear-database.js
```

## ⚠️ Important Warnings

1. **These scripts are DESTRUCTIVE** - they permanently delete data
2. **Always backup your database** before running in production
3. **Test scripts in development** environment first
4. **R2 file cleanup is irreversible** - files cannot be recovered
5. **Super Admin accounts are preserved** but other users are deleted

## 🔧 Environment Variables Required

### Database (All Scripts)
```env
DATABASE_URL=postgresql://username:password@localhost:5432/database_name
```

### R2 Storage (Full Cleanup Script)
```env
CLOUDFLARE_R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
CLOUDFLARE_R2_ACCESS_KEY_ID=your-access-key
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your-secret-key
CLOUDFLARE_R2_BUCKET_NAME=your-bucket-name
```

## 🔄 After Running Cleanup

After successful cleanup, you may want to:

1. **Seed basic data**:
   ```bash
   npx prisma db seed
   ```

2. **Create new loan types**:
   ```bash
   node prisma/seed-loan-types.ts
   ```

3. **Set up company settings** through the admin panel

4. **Create new users** as needed

## 🆘 Recovery

If something goes wrong:

1. **With backup** (safe script): Restore from the generated backup file
2. **Without backup**: Restore from your regular database backups
3. **R2 files**: Cannot be recovered - ensure you have separate file backups

## 📝 Script Details

### clear-database-simple.js
- Fastest execution
- No external dependencies
- Basic error handling
- Good for development

### clear-database-safe.js
- Creates PostgreSQL backup automatically
- Requires `pg_dump` utility
- Interactive confirmation
- Detailed progress reporting
- Best for production use

### clear-database.js
- Most comprehensive cleanup
- Includes R2 file storage cleanup
- Interactive confirmation
- Requires AWS SDK dependencies
- Complete system reset

## 🔍 Troubleshooting

### Common Issues

1. **Permission errors**: Ensure database user has DELETE permissions
2. **Foreign key constraints**: Scripts handle deletion order automatically
3. **Backup failures**: Check PostgreSQL tools installation
4. **R2 connection errors**: Verify environment variables

### Getting Help

If you encounter issues:
1. Check the error messages carefully
2. Verify environment variables are set correctly
3. Ensure database connectivity
4. Test with the simple script first

---

**⚠️ Remember: These scripts are powerful tools. Use them responsibly and always have backups!**
