#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting production build process...\n');

// Step 1: Clean previous builds
console.log('🧹 Cleaning previous builds...');
try {
  execSync('npm run clean', { stdio: 'inherit' });
  console.log('✅ Clean completed\n');
} catch (error) {
  console.log('⚠️  Clean failed (this is normal if no previous build exists)\n');
}

// Step 2: Generate Prisma client
console.log('🔧 Generating Prisma client...');
try {
  execSync('npx prisma generate', { stdio: 'inherit' });
  console.log('✅ Prisma client generated\n');
} catch (error) {
  console.error('❌ Prisma client generation failed:', error.message);
  process.exit(1);
}

// Step 3: Build the application
console.log('🏗️  Building Next.js application...');
try {
  execSync('npm run build:production', { stdio: 'inherit' });
  console.log('✅ Build completed successfully\n');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}

// Step 4: Check build output
const buildDir = path.join(__dirname, '..', '.next');
if (fs.existsSync(buildDir)) {
  console.log('✅ Build output directory created successfully');
  
  // Check for standalone output
  const standaloneDir = path.join(buildDir, 'standalone');
  if (fs.existsSync(standaloneDir)) {
    console.log('✅ Standalone build created for deployment');
  }
  
  console.log('\n🎉 Production build completed successfully!');
  console.log('\n📦 Build artifacts:');
  console.log('   - .next/ (Next.js build output)');
  console.log('   - .next/standalone/ (Standalone deployment files)');
  console.log('\n🚀 To start the production server:');
  console.log('   npm run start:production');
} else {
  console.error('❌ Build directory not found');
  process.exit(1);
}
