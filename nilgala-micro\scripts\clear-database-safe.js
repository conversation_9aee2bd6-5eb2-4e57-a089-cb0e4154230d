// Safe Database cleanup script with backup functionality
const { PrismaClient } = require('@prisma/client')
const { exec } = require('child_process')
const { promisify } = require('util')
const fs = require('fs').promises
const path = require('path')
const readline = require('readline')

const execAsync = promisify(exec)
const prisma = new PrismaClient()

// Create readline interface for user confirmation
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

// Helper function to ask for user confirmation
function askConfirmation(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y')
    })
  })
}

// Function to create database backup
async function createBackup() {
  try {
    console.log('💾 Creating database backup...')
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const backupDir = path.join(process.cwd(), 'backups')
    const backupFile = path.join(backupDir, `backup_before_cleanup_${timestamp}.sql`)
    
    // Ensure backup directory exists
    try {
      await fs.access(backupDir)
    } catch {
      await fs.mkdir(backupDir, { recursive: true })
    }
    
    // Create PostgreSQL dump
    const databaseUrl = process.env.DATABASE_URL
    if (!databaseUrl) {
      throw new Error('DATABASE_URL not found in environment variables')
    }
    
    // Extract database connection details from URL
    const url = new URL(databaseUrl)
    const dbName = url.pathname.slice(1)
    const host = url.hostname
    const port = url.port || 5432
    const username = url.username
    const password = url.password
    
    // Set PGPASSWORD environment variable for pg_dump
    const env = { ...process.env, PGPASSWORD: password }
    
    const dumpCommand = `pg_dump -h ${host} -p ${port} -U ${username} -d ${dbName} -f "${backupFile}"`
    
    console.log('   Running pg_dump...')
    await execAsync(dumpCommand, { env })
    
    console.log(`   ✅ Backup created: ${backupFile}`)
    return backupFile
    
  } catch (error) {
    console.error('❌ Failed to create backup:', error.message)
    console.log('⚠️  Proceeding without backup. Make sure you have a recent backup!')
    
    const proceed = await askConfirmation('Continue without backup? (yes/no): ')
    if (!proceed) {
      throw new Error('User cancelled due to backup failure')
    }
    
    return null
  }
}

// Function to get data counts
async function getDataCounts() {
  return {
    payments: await prisma.payment.count(),
    paymentSchedules: await prisma.paymentSchedule.count(),
    loans: await prisma.loan.count(),
    loanApprovals: await prisma.loanApproval.count(),
    loanGuarantors: await prisma.loanGuarantor.count(),
    guarantors: await prisma.guarantor.count(),
    guarantorDocuments: await prisma.guarantorDocument.count(),
    documents: await prisma.document.count(),
    customers: await prisma.customer.count(),
    loanTypes: await prisma.loanType.count(),
    creditOfficerTargets: await prisma.creditOfficerTarget.count(),
    auditLogs: await prisma.auditLog.count(),
    users: await prisma.user.count(),
    userRoles: await prisma.userRole.count(),
    permissions: await prisma.permission.count(),
    requiredDocuments: await prisma.requiredDocument.count(),
    companySettings: await prisma.companySettings.count()
  }
}

// Main cleanup function
async function clearDatabaseSafe() {
  console.log('🧹 Safe Database Cleanup Script')
  console.log('===============================')
  console.log('')
  console.log('⚠️  This will delete ALL transactional data while preserving:')
  console.log('   ✅ User Roles')
  console.log('   ✅ Permissions') 
  console.log('   ✅ Required Documents')
  console.log('   ✅ Super Admin user accounts')
  console.log('')
  console.log('❌ This will DELETE:')
  console.log('   - All customer data')
  console.log('   - All loan applications and records')
  console.log('   - All payment records and schedules')
  console.log('   - All guarantor data and documents')
  console.log('   - All uploaded documents')
  console.log('   - All loan types')
  console.log('   - All credit officer targets')
  console.log('   - All audit logs')
  console.log('   - All non-admin users')
  console.log('   - Company settings')
  console.log('')
  
  // Show current data counts
  console.log('📊 Current database state:')
  const initialCounts = await getDataCounts()
  Object.entries(initialCounts).forEach(([table, count]) => {
    if (count > 0) {
      console.log(`   ${table}: ${count} records`)
    }
  })
  console.log('')
  
  // Ask for confirmation
  const confirmed = await askConfirmation('❓ Do you want to proceed with cleanup? (yes/no): ')
  
  if (!confirmed) {
    console.log('❌ Database cleanup cancelled by user.')
    rl.close()
    return
  }
  
  // Create backup
  const backupFile = await createBackup()
  
  console.log('')
  console.log('🗑️  Starting cleanup process...')
  
  try {
    // Start transaction to ensure atomicity
    await prisma.$transaction(async (tx) => {
      // Delete transactional data in correct order (respecting foreign key constraints)
      console.log('   Deleting payments...')
      await tx.payment.deleteMany({})

      console.log('   Deleting payment schedules...')
      await tx.paymentSchedule.deleteMany({})

      console.log('   Deleting loan approvals...')
      await tx.loanApproval.deleteMany({})

      console.log('   Deleting guarantor documents...')
      await tx.guarantorDocument.deleteMany({})

      console.log('   Deleting loan guarantors...')
      await tx.loanGuarantor.deleteMany({})

      console.log('   Deleting guarantors...')
      await tx.guarantor.deleteMany({})

      console.log('   Deleting documents...')
      await tx.document.deleteMany({})

      console.log('   Deleting loans...')
      await tx.loan.deleteMany({})

      console.log('   Deleting customers...')
      await tx.customer.deleteMany({})

      console.log('   Deleting loan types...')
      await tx.loanType.deleteMany({})

      console.log('   Deleting credit officer targets...')
      await tx.creditOfficerTarget.deleteMany({})

      console.log('   Deleting audit logs...')
      await tx.auditLog.deleteMany({})

      console.log('   Deleting company settings...')
      await tx.companySettings.deleteMany({})

      console.log('   Deleting non-system users...')
      await tx.user.deleteMany({
        where: {
          role: {
            not: 'SUPER_ADMIN'
          }
        }
      })

      console.log('✅ All transactional data deleted successfully!')
    })

    // Show final counts
    console.log('')
    console.log('📋 Final database state:')
    const finalCounts = await getDataCounts()
    Object.entries(finalCounts).forEach(([table, count]) => {
      if (count > 0) {
        console.log(`   ${table}: ${count} records`)
      }
    })

    console.log('')
    console.log('🎉 Database cleanup completed successfully!')
    
    if (backupFile) {
      console.log(`💾 Backup saved to: ${backupFile}`)
    }
    
    console.log('')
    console.log('⚡ The system is now ready for fresh data entry!')

  } catch (error) {
    console.error('❌ Error during database cleanup:', error)
    
    if (backupFile) {
      console.log(`💾 You can restore from backup: ${backupFile}`)
    }
    
    throw error
  } finally {
    await prisma.$disconnect()
    rl.close()
  }
}

// Run the cleanup
if (require.main === module) {
  clearDatabaseSafe()
    .then(() => {
      console.log('✨ Safe database cleanup script completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Safe database cleanup script failed:', error)
      process.exit(1)
    })
}

module.exports = { clearDatabaseSafe }
