// Simple Database cleanup script - Quick and straightforward
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function clearDatabaseSimple() {
  console.log('🧹 Simple Database Cleanup')
  console.log('==========================')
  console.log('')
  console.log('This will preserve:')
  console.log('✅ User Roles, Permissions, Required Documents')
  console.log('')
  console.log('This will delete:')
  console.log('❌ All transactional data (customers, loans, payments, etc.)')
  console.log('')

  try {
    console.log('🗑️  Clearing transactional data...')

    // Delete in correct order to respect foreign key constraints
    await prisma.payment.deleteMany({})
    console.log('   ✅ Payments cleared')

    await prisma.paymentSchedule.deleteMany({})
    console.log('   ✅ Payment schedules cleared')

    await prisma.loanApproval.deleteMany({})
    console.log('   ✅ Loan approvals cleared')

    await prisma.guarantorDocument.deleteMany({})
    console.log('   ✅ Guarantor documents cleared')

    await prisma.loanGuarantor.deleteMany({})
    console.log('   ✅ Loan guarantors cleared')

    await prisma.guarantor.deleteMany({})
    console.log('   ✅ Guarantors cleared')

    await prisma.document.deleteMany({})
    console.log('   ✅ Documents cleared')

    await prisma.loan.deleteMany({})
    console.log('   ✅ Loans cleared')

    await prisma.customer.deleteMany({})
    console.log('   ✅ Customers cleared')

    await prisma.loanType.deleteMany({})
    console.log('   ✅ Loan types cleared')

    await prisma.creditOfficerTarget.deleteMany({})
    console.log('   ✅ Credit officer targets cleared')

    await prisma.auditLog.deleteMany({})
    console.log('   ✅ Audit logs cleared')

    await prisma.companySettings.deleteMany({})
    console.log('   ✅ Company settings cleared')

    // Delete non-admin users
    const deletedUsers = await prisma.user.deleteMany({
      where: {
        role: {
          not: 'SUPER_ADMIN'
        }
      }
    })
    console.log(`   ✅ Non-admin users cleared (${deletedUsers.count} users)`)

    console.log('')
    console.log('🎉 Database cleanup completed!')
    
    // Show what's left
    const remainingUsers = await prisma.user.count()
    const remainingRoles = await prisma.userRole.count()
    const remainingPermissions = await prisma.permission.count()
    const remainingRequiredDocs = await prisma.requiredDocument.count()

    console.log('📋 Preserved data:')
    console.log(`   Users: ${remainingUsers}`)
    console.log(`   User Roles: ${remainingRoles}`)
    console.log(`   Permissions: ${remainingPermissions}`)
    console.log(`   Required Documents: ${remainingRequiredDocs}`)
    console.log('')
    console.log('⚡ System is ready for fresh data!')

  } catch (error) {
    console.error('❌ Error during cleanup:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the cleanup
if (require.main === module) {
  clearDatabaseSimple()
    .then(() => {
      console.log('✨ Simple cleanup completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Cleanup failed:', error)
      process.exit(1)
    })
}

module.exports = { clearDatabaseSimple }
