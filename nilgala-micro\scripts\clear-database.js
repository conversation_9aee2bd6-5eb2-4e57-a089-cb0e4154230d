// Database cleanup script - Clears all transactional data while preserving system configuration
const { PrismaClient } = require('@prisma/client')
const { S3Client, ListObjectsV2Command, DeleteObjectsCommand } = require('@aws-sdk/client-s3')
const readline = require('readline')

const prisma = new PrismaClient()

// Initialize R2 client for file cleanup
const r2Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
  },
})

const BUCKET_NAME = process.env.CLOUDFLARE_R2_BUCKET_NAME

// Create readline interface for user confirmation
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

// Helper function to ask for user confirmation
function askConfirmation(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y')
    })
  })
}

// Function to clean up R2 storage
async function cleanupR2Storage() {
  if (!BUCKET_NAME || !process.env.CLOUDFLARE_R2_ENDPOINT) {
    console.log('⚠️  R2 configuration not found. Skipping file cleanup.')
    return
  }

  try {
    console.log('🗂️  Cleaning up R2 storage...')

    // List all objects in the bucket
    const listCommand = new ListObjectsV2Command({
      Bucket: BUCKET_NAME,
    })

    const response = await r2Client.send(listCommand)

    if (!response.Contents || response.Contents.length === 0) {
      console.log('   No files found in R2 storage.')
      return
    }

    console.log(`   Found ${response.Contents.length} files to delete...`)

    // Delete all objects
    const deleteCommand = new DeleteObjectsCommand({
      Bucket: BUCKET_NAME,
      Delete: {
        Objects: response.Contents.map(obj => ({ Key: obj.Key })),
        Quiet: false
      }
    })

    const deleteResponse = await r2Client.send(deleteCommand)

    if (deleteResponse.Deleted) {
      console.log(`   ✅ Successfully deleted ${deleteResponse.Deleted.length} files from R2 storage`)
    }

    if (deleteResponse.Errors && deleteResponse.Errors.length > 0) {
      console.log(`   ⚠️  ${deleteResponse.Errors.length} files failed to delete:`)
      deleteResponse.Errors.forEach(error => {
        console.log(`      - ${error.Key}: ${error.Message}`)
      })
    }

  } catch (error) {
    console.error('❌ Error cleaning up R2 storage:', error.message)
    console.log('   Continuing with database cleanup...')
  }
}

async function clearDatabase() {
  console.log('🧹 Starting database cleanup...')
  console.log('⚠️  This will delete ALL transactional data while preserving:')
  console.log('   - User Roles')
  console.log('   - Permissions')
  console.log('   - Required Documents')
  console.log('   - Super Admin user accounts')
  console.log('')

  // Ask for confirmation
  const confirmed = await askConfirmation('❓ Are you sure you want to proceed? This action cannot be undone! (yes/no): ')

  if (!confirmed) {
    console.log('❌ Database cleanup cancelled by user.')
    rl.close()
    return
  }

  console.log('✅ User confirmed. Proceeding with cleanup...')
  console.log('')

  try {
    // Start transaction to ensure atomicity
    await prisma.$transaction(async (tx) => {
      console.log('📊 Getting current data counts...')
      
      // Get counts before deletion
      const counts = {
        payments: await tx.payment.count(),
        paymentSchedules: await tx.paymentSchedule.count(),
        loans: await tx.loan.count(),
        loanApprovals: await tx.loanApproval.count(),
        loanGuarantors: await tx.loanGuarantor.count(),
        guarantors: await tx.guarantor.count(),
        guarantorDocuments: await tx.guarantorDocument.count(),
        documents: await tx.document.count(),
        customers: await tx.customer.count(),
        loanTypes: await tx.loanType.count(),
        creditOfficerTargets: await tx.creditOfficerTarget.count(),
        auditLogs: await tx.auditLog.count(),
        users: await tx.user.count(),
        userRoles: await tx.userRole.count(),
        permissions: await tx.permission.count(),
        requiredDocuments: await tx.requiredDocument.count(),
        companySettings: await tx.companySettings.count()
      }

      console.log('Current database state:')
      Object.entries(counts).forEach(([table, count]) => {
        console.log(`   ${table}: ${count} records`)
      })
      console.log('')

      // Delete transactional data in correct order (respecting foreign key constraints)
      console.log('🗑️  Deleting transactional data...')

      // 1. Delete payments first (references payment schedules and loans)
      console.log('   Deleting payments...')
      await tx.payment.deleteMany({})

      // 2. Delete payment schedules (references loans)
      console.log('   Deleting payment schedules...')
      await tx.paymentSchedule.deleteMany({})

      // 3. Delete loan approvals (references loans)
      console.log('   Deleting loan approvals...')
      await tx.loanApproval.deleteMany({})

      // 4. Delete guarantor documents (references guarantors)
      console.log('   Deleting guarantor documents...')
      await tx.guarantorDocument.deleteMany({})

      // 5. Delete loan guarantors (references loans and guarantors)
      console.log('   Deleting loan guarantors...')
      await tx.loanGuarantor.deleteMany({})

      // 6. Delete guarantors
      console.log('   Deleting guarantors...')
      await tx.guarantor.deleteMany({})

      // 7. Delete documents (references loans and customers)
      console.log('   Deleting documents...')
      await tx.document.deleteMany({})

      // 8. Delete loans (references customers and loan types)
      console.log('   Deleting loans...')
      await tx.loan.deleteMany({})

      // 9. Delete customers
      console.log('   Deleting customers...')
      await tx.customer.deleteMany({})

      // 10. Delete loan types
      console.log('   Deleting loan types...')
      await tx.loanType.deleteMany({})

      // 11. Delete credit officer targets
      console.log('   Deleting credit officer targets...')
      await tx.creditOfficerTarget.deleteMany({})

      // 12. Delete audit logs
      console.log('   Deleting audit logs...')
      await tx.auditLog.deleteMany({})

      // 13. Delete company settings (will be recreated with defaults)
      console.log('   Deleting company settings...')
      await tx.companySettings.deleteMany({})

      // 14. Delete users (except system users - keep Super Admin and system accounts)
      console.log('   Deleting non-system users...')
      await tx.user.deleteMany({
        where: {
          role: {
            not: 'SUPER_ADMIN'
          }
        }
      })

      console.log('✅ Transactional data deleted successfully!')
      console.log('')

      // Verify what's preserved
      const preservedCounts = {
        users: await tx.user.count(),
        userRoles: await tx.userRole.count(),
        permissions: await tx.permission.count(),
        requiredDocuments: await tx.requiredDocument.count()
      }

      console.log('📋 Preserved system data:')
      Object.entries(preservedCounts).forEach(([table, count]) => {
        console.log(`   ${table}: ${count} records`)
      })
    })

    // Clean up R2 storage
    await cleanupR2Storage()

    console.log('')
    console.log('🎉 Database cleanup completed successfully!')
    console.log('')
    console.log('📝 What was preserved:')
    console.log('   ✅ User Roles (SUPER_ADMIN, HIGHER_MANAGEMENT, etc.)')
    console.log('   ✅ Permissions (all permission definitions)')
    console.log('   ✅ Required Documents (document type definitions)')
    console.log('   ✅ Super Admin user accounts')
    console.log('')
    console.log('🗑️  What was cleared:')
    console.log('   ❌ All customer data')
    console.log('   ❌ All loan applications and records')
    console.log('   ❌ All payment records and schedules')
    console.log('   ❌ All guarantor data and documents')
    console.log('   ❌ All uploaded documents')
    console.log('   ❌ All loan types (can be recreated)')
    console.log('   ❌ All credit officer targets')
    console.log('   ❌ All audit logs')
    console.log('   ❌ All non-admin users')
    console.log('   ❌ Company settings (will use defaults)')
    console.log('')
    console.log('⚡ The system is now ready for fresh data entry!')

  } catch (error) {
    console.error('❌ Error during database cleanup:', error)
    throw error
  } finally {
    await prisma.$disconnect()
    rl.close()
  }
}

// Run the cleanup
if (require.main === module) {
  clearDatabase()
    .then(() => {
      console.log('✨ Database cleanup script completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Database cleanup script failed:', error)
      process.exit(1)
    })
}

module.exports = { clearDatabase }
