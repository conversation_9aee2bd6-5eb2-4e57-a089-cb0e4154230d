const fs = require('fs')
const path = require('path')

// Files to update
const filesToUpdate = [
  'src/app/api/customers/route.ts',
  'src/app/api/payments/route.ts',
  'src/app/api/admin/users/route.ts',
  'src/app/api/required-documents/route.ts'
]

function updateFile(filePath) {
  const fullPath = path.join(__dirname, '..', filePath)
  
  if (!fs.existsSync(fullPath)) {
    console.log(`❌ File not found: ${filePath}`)
    return
  }

  let content = fs.readFileSync(fullPath, 'utf8')
  
  // Replace hasPermission with hasPermissionSync
  const updated = content.replace(/hasPermission\(/g, 'hasPermissionSync(')
  
  // Update imports
  const updatedImports = updated.replace(
    /import { ([^}]*?)hasPermission([^}]*?) } from '@\/lib\/auth'/g,
    (match, before, after) => {
      const beforeClean = before.replace(/,\s*$/, '')
      const afterClean = after.replace(/^\s*,/, '')
      return `import { ${beforeClean}hasPermissionSync${afterClean} } from '@/lib/auth'`
    }
  )
  
  if (updatedImports !== content) {
    fs.writeFileSync(fullPath, updatedImports)
    console.log(`✅ Updated: ${filePath}`)
  } else {
    console.log(`⚪ No changes needed: ${filePath}`)
  }
}

console.log('🔧 Updating API routes to use hasPermissionSync...')

filesToUpdate.forEach(updateFile)

console.log('✅ Permission updates completed!')
