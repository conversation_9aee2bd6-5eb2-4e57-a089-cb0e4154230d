const { PrismaClient, UserRole } = require('@prisma/client')

const prisma = new PrismaClient()

// Default role permissions from auth.ts
const ROLE_PERMISSIONS = {
  [UserRole.SUPER_ADMIN]: [
    'users:create', 'users:read', 'users:update', 'users:delete',
    'customers:create', 'customers:read', 'customers:update', 'customers:delete',
    'loans:create', 'loans:read', 'loans:update', 'loans:delete', 'loans:approve',
    'loan_types:create', 'loan_types:read', 'loan_types:update', 'loan_types:delete',
    'payments:create', 'payments:read', 'payments:update', 'payments:delete',
    'reports:read', 'reports:export',
    'settings:read', 'settings:update',
    'audit:read'
  ],
  [UserRole.HIGHER_MANAGEMENT]: [
    'customers:read', 'customers:update',
    'loans:create', 'loans:read', 'loans:update', 'loans:approve',
    'loan_types:read', 'loan_types:update',
    'payments:create', 'payments:read', 'payments:update',
    'reports:read', 'reports:export',
    'collections:read', 'collections:assign'
  ],
  [UserRole.MANAGER]: [
    'customers:create', 'customers:read', 'customers:update',
    'loans:create', 'loans:read', 'loans:update', 'loans:approve',
    'payments:create', 'payments:read', 'payments:update',
    'reports:read',
    'collections:read', 'collections:assign'
  ],
  [UserRole.CREDIT_OFFICER]: [
    'customers:create', 'customers:read', 'customers:update',
    'loans:create', 'loans:read', 'loans:update',
    'payments:create', 'payments:read', 'payments:update',
    'documents:create', 'documents:read', 'documents:update',
    'collections:read'
  ],
  [UserRole.CUSTOMER_SERVICE_OFFICER]: [
    'customers:read', 'customers:update',
    'loans:read',
    'payments:create', 'payments:read',
    'reports:read'
  ]
}

async function seedRolePermissions() {
  console.log('🌱 Seeding role permissions...')

  try {
    // Clear existing permissions
    await prisma.rolePermission.deleteMany({})
    console.log('🗑️ Cleared existing role permissions')

    // Insert default permissions for each role
    for (const [role, permissions] of Object.entries(ROLE_PERMISSIONS)) {
      console.log(`📝 Adding permissions for ${role}...`)
      
      for (const permission of permissions) {
        await prisma.rolePermission.create({
          data: {
            role: role,
            permission: permission,
            isActive: true
          }
        })
      }
      
      console.log(`✅ Added ${permissions.length} permissions for ${role}`)
    }

    console.log('🎉 Role permissions seeded successfully!')
    
    // Display summary
    const totalPermissions = await prisma.rolePermission.count()
    console.log(`📊 Total permissions in database: ${totalPermissions}`)

  } catch (error) {
    console.error('❌ Error seeding role permissions:', error)
  } finally {
    await prisma.$disconnect()
  }
}

seedRolePermissions()
