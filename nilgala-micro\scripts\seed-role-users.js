const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function seedRoleUsers() {
  try {
    console.log('🌱 Seeding role-based users...')

    // Define users for each role
    const roleUsers = [
      {
        email: '<EMAIL>',
        password: 'admin123',
        firstName: 'System',
        lastName: 'Administrator',
        role: 'SUPER_ADMIN',
        phone: '+94771234567',
        isActive: true
      },
      {
        email: '<EMAIL>',
        password: 'management123',
        firstName: '<PERSON><PERSON>',
        lastName: '<PERSON>',
        role: 'HIGHER_MANAGEMENT',
        phone: '+94771234568',
        isActive: true
      },
      {
        email: '<EMAIL>',
        password: 'manager123',
        firstName: 'Priya',
        lastName: 'Silva',
        role: 'MANAGER',
        phone: '+94771234569',
        isActive: true
      },
      {
        email: '<EMAIL>',
        password: 'credit123',
        firstName: '<PERSON>',
        lastName: 'Perera',
        role: 'CREDIT_OFFICER',
        phone: '+94771234570',
        isActive: true
      },
      {
        email: '<EMAIL>',
        password: 'service123',
        firstName: 'Nisha',
        lastName: 'Jayawardena',
        role: 'CUSTOMER_SERVICE_OFFICER',
        phone: '+94771234571',
        isActive: true
      }
    ]

    for (const userData of roleUsers) {
      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email: userData.email }
      })

      if (existingUser) {
        console.log(`✅ User ${userData.email} already exists - skipping`)
        continue
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(userData.password, 12)

      // Create user
      const user = await prisma.user.create({
        data: {
          ...userData,
          password: hashedPassword
        }
      })

      console.log(`✅ Created ${userData.role}: ${userData.firstName} ${userData.lastName} (${userData.email})`)
    }

    console.log('\n🎉 Role-based user seeding completed!')
    
    // Display login credentials
    console.log('\n📋 LOGIN CREDENTIALS:')
    console.log('=====================')
    roleUsers.forEach(user => {
      console.log(`${user.role}:`)
      console.log(`  Email: ${user.email}`)
      console.log(`  Password: ${user.password}`)
      console.log(`  Name: ${user.firstName} ${user.lastName}`)
      console.log('  ---')
    })

  } catch (error) {
    console.error('❌ Error seeding users:', error)
  } finally {
    await prisma.$disconnect()
  }
}

seedRoleUsers()
