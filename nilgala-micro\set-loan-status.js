// <PERSON><PERSON><PERSON> to set a loan status to PENDING_MORE_INFO for testing edit functionality
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  try {
    console.log('=== Setting Loan Status to PENDING_MORE_INFO ===')
    
    // Find a loan that's not disbursed yet
    const loan = await prisma.loan.findFirst({
      where: {
        status: {
          in: ['PENDING_APPROVAL', 'APPROVED']
        }
      },
      include: {
        customer: true,
        loanType: true
      }
    })
    
    if (!loan) {
      console.log('No suitable loan found to modify')
      return
    }
    
    console.log('Found loan:', {
      loanNumber: loan.loanNumber,
      customer: `${loan.customer.firstName} ${loan.customer.lastName}`,
      currentStatus: loan.status,
      principalAmount: loan.principalAmount
    })
    
    // Update the loan status to PENDING_MORE_INFO
    const updatedLoan = await prisma.loan.update({
      where: { id: loan.id },
      data: {
        status: 'PENDING_MORE_INFO'
      }
    })
    
    console.log('✅ Updated loan status to PENDING_MORE_INFO')
    console.log('Loan ID:', updatedLoan.id)
    console.log('Edit URL:', `http://localhost:3000/loans/${updatedLoan.id}/edit`)
    
  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()
