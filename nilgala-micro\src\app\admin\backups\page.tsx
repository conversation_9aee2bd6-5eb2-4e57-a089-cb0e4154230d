'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import BackupManager from '@/components/admin/BackupManager'
import PageHeader from '@/components/layout/PageHeader'
import { Database } from 'lucide-react'

export default function BackupsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'loading') return // Still loading

    if (!session) {
      router.push('/signin')
      return
    }

    // Only Super Admin can access backup management
    if (session.user.role !== 'SUPER_ADMIN') {
      router.push('/dashboard')
      return
    }
  }, [session, status, router])

  // Show loading while checking authentication
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  // Show access denied if not Super Admin
  if (!session || session.user.role !== 'SUPER_ADMIN') {
    return (
      <PageHeader
        title="Access Denied"
        description="You don't have permission to access backup management."
      >
        <div className="text-center py-8">
          <Database className="h-16 w-16 mx-auto mb-4 text-gray-300" />
          <p className="text-gray-600 mb-4">Only Super Administrators can manage database backups.</p>
          <p className="text-sm text-gray-500">Please contact your system administrator for access.</p>
        </div>
      </PageHeader>
    )
  }

  return (
    <PageHeader
      title="Database Backup Management"
      description="Create, manage, and restore database backups"
    >
      <BackupManager />
    </PageHeader>
  )
}
