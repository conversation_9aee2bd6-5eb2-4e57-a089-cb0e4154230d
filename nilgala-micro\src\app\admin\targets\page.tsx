'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import PageHeader from '@/components/layout/PageHeader'
import { useToast } from '@/hooks/use-toast'
import { usePermissions } from '@/hooks/usePermissions'
import { Target, Users, Calendar, DollarSign, TrendingUp, BarChart3, CheckCircle, AlertCircle } from 'lucide-react'

interface CreditOfficer {
  id: string
  name: string
  email: string
  assignedCustomers: number
  workload: number
}

interface Target {
  id: string
  creditOfficerId: string
  month: number
  year: number
  loanTarget: number
  collectionTarget: number
  notes?: string
  creditOfficer: {
    id: string
    firstName: string
    lastName: string
    email: string
  }
  setter: {
    id: string
    firstName: string
    lastName: string
    role: string
  }
  createdAt: string
  updatedAt: string
}

interface FormData {
  creditOfficerId: string
  month: number
  year: number
  loanTarget: number
  collectionTarget: number
  notes: string
}

interface CreditOfficerPerformance {
  id: string
  firstName: string
  lastName: string
  email: string
  assignedCustomers: number
  currentTarget?: {
    id: string
    month: number
    year: number
    loanTarget: number
    collectionTarget: number
    notes?: string
  }
  monthlyAchievement: {
    loansAmount: number
    collectionsAmount: number
    loansCount: number
    collectionsCount: number
  }
  progressPercentage: {
    loans: number
    collections: number
    overall: number
  }
  status: 'on-track' | 'behind' | 'exceeded' | 'no-target'
}

export default function TargetsManagementPage() {
  const { data: session } = useSession()
  const { toast } = useToast()

  // Check if user can manage targets (matches API logic)
  const canManageTargets = session?.user && (
    ['MANAGER', 'HIGHER_MANAGEMENT', 'SUPER_ADMIN'].includes(session.user.role)
  )
  const [creditOfficers, setCreditOfficers] = useState<CreditOfficer[]>([])
  const [targets, setTargets] = useState<Target[]>([])
  const [creditOfficerPerformance, setCreditOfficerPerformance] = useState<CreditOfficerPerformance[]>([])
  const [loading, setLoading] = useState(true)
  const [performanceLoading, setPerformanceLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [selectedTarget, setSelectedTarget] = useState<Target | null>(null)
  const [activeTab, setActiveTab] = useState('overview')
  
  const currentDate = new Date()
  const [formData, setFormData] = useState<FormData>({
    creditOfficerId: '',
    month: currentDate.getMonth() + 1,
    year: currentDate.getFullYear(),
    loanTarget: 0,
    collectionTarget: 0,
    notes: ''
  })

  useEffect(() => {
    fetchCreditOfficers()
    fetchTargets()
    fetchCreditOfficerPerformance()
  }, [])

  const fetchCreditOfficers = async () => {
    try {
      const response = await fetch('/api/users/credit-officers')
      if (response.ok) {
        const data = await response.json()
        setCreditOfficers(data.creditOfficers)
      }
    } catch (error) {
      console.error('Error fetching credit officers:', error)
    }
  }

  const fetchTargets = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/credit-officer-targets')
      if (response.ok) {
        const data = await response.json()
        setTargets(data.targets)
      }
    } catch (error) {
      console.error('Error fetching targets:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchCreditOfficerPerformance = async () => {
    try {
      setPerformanceLoading(true)
      const currentDate = new Date()
      const response = await fetch(`/api/admin/credit-officer-performance?month=${currentDate.getMonth() + 1}&year=${currentDate.getFullYear()}`)
      if (response.ok) {
        const data = await response.json()
        setCreditOfficerPerformance(data.performance)
      }
    } catch (error) {
      console.error('Error fetching credit officer performance:', error)
    } finally {
      setPerformanceLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.creditOfficerId) {
      toast({
        title: "Error",
        description: "Please select a Credit Officer",
        variant: "destructive"
      })
      return
    }

    setSaving(true)
    try {
      const response = await fetch('/api/credit-officer-targets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        const data = await response.json()
        toast({
          title: "Success",
          description: selectedTarget ? "Target updated successfully" : "Target created successfully",
        })
        
        // Reset form
        setFormData({
          creditOfficerId: '',
          month: currentDate.getMonth() + 1,
          year: currentDate.getFullYear(),
          loanTarget: 0,
          collectionTarget: 0,
          notes: ''
        })
        setSelectedTarget(null)
        
        // Refresh targets
        fetchTargets()
      } else {
        const errorData = await response.json()
        toast({
          title: "Error",
          description: errorData.error || "Failed to save target",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save target",
        variant: "destructive"
      })
    } finally {
      setSaving(false)
    }
  }

  const handleEditTarget = (target: Target) => {
    setSelectedTarget(target)
    setFormData({
      creditOfficerId: target.creditOfficerId,
      month: target.month,
      year: target.year,
      loanTarget: target.loanTarget,
      collectionTarget: target.collectionTarget,
      notes: target.notes || ''
    })
  }

  const handleCancelEdit = () => {
    setSelectedTarget(null)
    setFormData({
      creditOfficerId: '',
      month: currentDate.getMonth() + 1,
      year: currentDate.getFullYear(),
      loanTarget: 0,
      collectionTarget: 0,
      notes: ''
    })
  }

  const getMonthName = (month: number) => {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ]
    return months[month - 1]
  }

  // Show access denied if user doesn't have permission
  if (!canManageTargets) {
    return (
      <PageHeader
        title="Access Denied"
        description="You don't have permission to manage Credit Officer targets."
      >
        <div className="text-center py-8">
          <p className="text-gray-600">Please contact your administrator for access.</p>
        </div>
      </PageHeader>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'exceeded': return 'bg-green-100 text-green-800'
      case 'on-track': return 'bg-blue-100 text-blue-800'
      case 'behind': return 'bg-red-100 text-red-800'
      case 'no-target': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'exceeded': return <CheckCircle className="h-4 w-4" />
      case 'on-track': return <TrendingUp className="h-4 w-4" />
      case 'behind': return <AlertCircle className="h-4 w-4" />
      default: return <Target className="h-4 w-4" />
    }
  }

  return (
      <PageHeader
        title="Credit Officer Targets"
        description="Set monthly loan and collection targets for Credit Officers"
      >
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="overview">Performance Overview</TabsTrigger>
            <TabsTrigger value="manage">Manage Targets</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Performance Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Officers</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{creditOfficerPerformance.length}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">With Targets</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600">
                    {creditOfficerPerformance.filter(p => p.currentTarget).length}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">On Track</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">
                    {creditOfficerPerformance.filter(p => p.status === 'on-track' || p.status === 'exceeded').length}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Behind Target</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-red-600">
                    {creditOfficerPerformance.filter(p => p.status === 'behind').length}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Credit Officers Performance List */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Credit Officer Performance
                </CardTitle>
                <CardDescription>
                  Real-time progress tracking for all Credit Officers
                </CardDescription>
              </CardHeader>
              <CardContent>
                {performanceLoading ? (
                  <div className="space-y-4">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="animate-pulse">
                        <div className="h-20 bg-gray-200 rounded-lg"></div>
                      </div>
                    ))}
                  </div>
                ) : creditOfficerPerformance.length === 0 ? (
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p className="text-gray-500">No Credit Officers found</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {creditOfficerPerformance.map((officer) => (
                      <div key={officer.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <h3 className="font-semibold text-lg">
                                {officer.firstName} {officer.lastName}
                              </h3>
                              <Badge className={getStatusColor(officer.status)}>
                                {getStatusIcon(officer.status)}
                                <span className="ml-1 capitalize">{officer.status.replace('-', ' ')}</span>
                              </Badge>
                            </div>
                            <p className="text-sm text-gray-600 mb-1">{officer.email}</p>
                            <p className="text-sm text-gray-600">
                              Assigned Customers: {officer.assignedCustomers}
                            </p>
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold text-blue-600">
                              {officer.progressPercentage.overall.toFixed(1)}%
                            </div>
                            <div className="text-sm text-gray-500">Overall Progress</div>
                          </div>
                        </div>

                        {officer.currentTarget ? (
                          <div className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              {/* Loan Target Progress */}
                              <div className="space-y-2">
                                <div className="flex justify-between items-center">
                                  <span className="text-sm font-medium">Loan Target</span>
                                  <span className="text-sm text-gray-600">
                                    LKR {officer.monthlyAchievement.loansAmount.toLocaleString()} /
                                    LKR {officer.currentTarget.loanTarget.toLocaleString()}
                                  </span>
                                </div>
                                <Progress
                                  value={Math.min(officer.progressPercentage.loans, 100)}
                                  className="h-2"
                                />
                                <div className="flex justify-between text-xs text-gray-500">
                                  <span>{officer.monthlyAchievement.loansCount} loans</span>
                                  <span>{officer.progressPercentage.loans.toFixed(1)}%</span>
                                </div>
                              </div>

                              {/* Collection Target Progress */}
                              <div className="space-y-2">
                                <div className="flex justify-between items-center">
                                  <span className="text-sm font-medium">Collection Target</span>
                                  <span className="text-sm text-gray-600">
                                    LKR {officer.monthlyAchievement.collectionsAmount.toLocaleString()} /
                                    LKR {officer.currentTarget.collectionTarget.toLocaleString()}
                                  </span>
                                </div>
                                <Progress
                                  value={Math.min(officer.progressPercentage.collections, 100)}
                                  className="h-2"
                                />
                                <div className="flex justify-between text-xs text-gray-500">
                                  <span>{officer.monthlyAchievement.collectionsCount} payments</span>
                                  <span>{officer.progressPercentage.collections.toFixed(1)}%</span>
                                </div>
                              </div>
                            </div>

                            {officer.currentTarget.notes && (
                              <div className="mt-3 p-2 bg-gray-50 rounded text-sm">
                                <strong>Notes:</strong> {officer.currentTarget.notes}
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="text-center py-4 text-gray-500">
                            <Target className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                            <p>No target set for this month</p>
                            <Button
                              size="sm"
                              variant="outline"
                              className="mt-2"
                              onClick={() => {
                                setFormData(prev => ({ ...prev, creditOfficerId: officer.id }))
                                setActiveTab('manage')
                              }}
                            >
                              Set Target
                            </Button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="manage" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Target Form */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                {selectedTarget ? 'Edit Target' : 'Set New Target'}
              </CardTitle>
              <CardDescription>
                {selectedTarget ? 'Update existing target' : 'Create monthly targets for Credit Officers'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="creditOfficerId">Credit Officer</Label>
                  <Select
                    value={formData.creditOfficerId}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, creditOfficerId: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select Credit Officer" />
                    </SelectTrigger>
                    <SelectContent>
                      {creditOfficers.map((officer) => (
                        <SelectItem key={officer.id} value={officer.id}>
                          <div className="flex flex-col">
                            <span>{officer.name}</span>
                            <span className="text-xs text-gray-500">
                              {officer.assignedCustomers} customers • {officer.email}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="month">Month</Label>
                    <Select
                      value={formData.month.toString()}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, month: parseInt(value) }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                          <SelectItem key={month} value={month.toString()}>
                            {getMonthName(month)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="year">Year</Label>
                    <Select
                      value={formData.year.toString()}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, year: parseInt(value) }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.from({ length: 5 }, (_, i) => currentDate.getFullYear() + i).map((year) => (
                          <SelectItem key={year} value={year.toString()}>
                            {year}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="loanTarget">Loan Target (Amount)</Label>
                  <Input
                    id="loanTarget"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.loanTarget}
                    onChange={(e) => setFormData(prev => ({ ...prev, loanTarget: parseFloat(e.target.value) || 0 }))}
                    placeholder="Monthly loan disbursement target"
                  />
                </div>

                <div>
                  <Label htmlFor="collectionTarget">Collection Target (Amount)</Label>
                  <Input
                    id="collectionTarget"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.collectionTarget}
                    onChange={(e) => setFormData(prev => ({ ...prev, collectionTarget: parseFloat(e.target.value) || 0 }))}
                    placeholder="Monthly collection target"
                  />
                </div>

                <div>
                  <Label htmlFor="notes">Notes (Optional)</Label>
                  <Textarea
                    id="notes"
                    value={formData.notes}
                    onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                    placeholder="Additional notes or instructions"
                    rows={3}
                  />
                </div>

                <div className="flex gap-2">
                  <Button type="submit" disabled={saving}>
                    {saving ? 'Saving...' : selectedTarget ? 'Update Target' : 'Set Target'}
                  </Button>
                  {selectedTarget && (
                    <Button type="button" variant="outline" onClick={handleCancelEdit}>
                      Cancel
                    </Button>
                  )}
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Current Targets */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Current Targets
              </CardTitle>
              <CardDescription>Recently set targets for Credit Officers</CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-3">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  ))}
                </div>
              ) : targets.length === 0 ? (
                <p className="text-gray-500 text-center py-4">No targets set yet</p>
              ) : (
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {targets.slice(0, 10).map((target) => (
                    <div key={target.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium">
                            {target.creditOfficer.firstName} {target.creditOfficer.lastName}
                          </h4>
                          <p className="text-sm text-gray-600 mb-2">
                            {getMonthName(target.month)} {target.year}
                          </p>
                          <div className="flex gap-4 text-sm">
                            <div className="flex items-center gap-1">
                              <DollarSign className="h-3 w-3" />
                              <span>Loan: ${target.loanTarget.toLocaleString()}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <TrendingUp className="h-3 w-3" />
                              <span>Collection: ${target.collectionTarget.toLocaleString()}</span>
                            </div>
                          </div>
                          {target.notes && (
                            <p className="text-xs text-gray-500 mt-1">{target.notes}</p>
                          )}
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEditTarget(target)}
                        >
                          Edit
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
            </div>
          </TabsContent>
        </Tabs>
      </PageHeader>
  )
}
