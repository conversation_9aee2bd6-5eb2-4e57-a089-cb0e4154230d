'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import UserManagement from '@/components/admin/UserManagement'
import PageHeader from '@/components/layout/PageHeader'
import { PermissionGuard } from '@/components/auth/PermissionGuard'

export default function UsersPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'loading') return // Still loading

    if (!session) {
      router.push('/auth/signin')
      return
    }

    // Check if user has permission to access user management
    if (!['SUPER_ADMIN', 'HIGHER_MANAGEMENT'].includes(session.user.role)) {
      router.push('/unauthorized')
      return
    }
  }, [session, status, router])

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <PermissionGuard permission="users:read">
      <PageHeader
        title="User Management"
        description="Manage system users and their roles"
      >
        <UserManagement />
      </PageHeader>
    </PermissionGuard>
  )
}
