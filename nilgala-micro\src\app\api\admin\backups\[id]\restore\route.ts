import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, hasPermission } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { exec } from 'child_process'
import { promisify } from 'util'
import { existsSync } from 'fs'

const execAsync = promisify(exec)

// POST /api/admin/backups/[id]/restore - Restore database from backup
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Unauthorized - Super Admin access required' }, { status: 401 })
    }

    const { id } = await params

    const backup = await prisma.databaseBackup.findUnique({
      where: { id }
    })

    if (!backup) {
      return NextResponse.json({ error: 'Backup not found' }, { status: 404 })
    }

    if (backup.status !== 'COMPLETED') {
      return NextResponse.json({ error: 'Backup is not ready for restore' }, { status: 400 })
    }

    // Check if backup file exists
    if (!existsSync(backup.filePath)) {
      return NextResponse.json({ error: 'Backup file not found on disk' }, { status: 404 })
    }

    try {
      // Perform database restore
      await restoreDatabase(backup.filePath)

      // Create audit log for restore operation
      await prisma.auditLog.create({
        data: {
          action: 'RESTORE_DATABASE',
          resource: 'DatabaseBackup',
          resourceId: id,
          userId: session.user.id,
          newValues: {
            backupFilename: backup.filename,
            backupCreatedAt: backup.createdAt,
            restoredAt: new Date()
          }
        }
      })

      return NextResponse.json({ 
        message: 'Database restored successfully',
        backupInfo: {
          filename: backup.filename,
          createdAt: backup.createdAt,
          restoredAt: new Date()
        }
      })
    } catch (error) {
      console.error('Database restore failed:', error)
      return NextResponse.json(
        { error: 'Database restore failed', details: error.message },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Error in restore endpoint:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Function to restore database from backup file
async function restoreDatabase(backupFilePath: string) {
  try {
    const dbUrl = process.env.DATABASE_URL
    if (!dbUrl) {
      throw new Error('DATABASE_URL not found')
    }

    // Parse database URL
    const url = new URL(dbUrl)
    const host = url.hostname
    const port = url.port || '5432'
    const database = url.pathname.slice(1)
    const username = url.username
    const password = url.password

    // Set environment variables for psql
    const env = {
      ...process.env,
      PGPASSWORD: password
    }

    // Create psql command to restore database
    const command = `psql -h ${host} -p ${port} -U ${username} -d ${database} --no-password -f "${backupFilePath}"`

    console.log('Starting database restore...')
    const { stdout, stderr } = await execAsync(command, { env })

    // Filter out expected warnings when restoring to existing database
    if (stderr) {
      const lines = stderr.split('\n')
      const criticalErrors = lines.filter(line =>
        line.includes('ERROR:') &&
        !line.includes('already exists') &&
        !line.includes('multiple primary keys') &&
        !line.includes('duplicate key value') &&
        !line.includes('cannot drop the currently open database') &&
        !line.includes('restricted superuser cannot grant')
      )

      if (criticalErrors.length > 0) {
        console.error('Critical restore errors:', criticalErrors.join('\n'))
        throw new Error(`Restore failed with critical errors: ${criticalErrors.join('; ')}`)
      }

      console.warn('Restore warnings:', stderr)
    }

    console.log('Database restore completed successfully')
    return { stdout, stderr }
  } catch (error) {
    console.error('Database restore failed:', error)
    throw new Error(`Database restore failed: ${error.message}`)
  }
}
