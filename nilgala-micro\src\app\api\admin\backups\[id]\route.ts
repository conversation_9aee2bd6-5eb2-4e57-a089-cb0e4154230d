import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, hasPermission } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import fs from 'fs/promises'
import { existsSync } from 'fs'

// GET /api/admin/backups/[id] - Download backup file
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Unauthorized - Super Admin access required' }, { status: 401 })
    }

    const { id } = await params

    const backup = await prisma.databaseBackup.findUnique({
      where: { id }
    })

    if (!backup) {
      return NextResponse.json({ error: 'Backup not found' }, { status: 404 })
    }

    if (backup.status !== 'COMPLETED') {
      return NextResponse.json({ error: 'Backup not ready for download' }, { status: 400 })
    }

    // Check if file exists
    if (!existsSync(backup.filePath)) {
      return NextResponse.json({ error: 'Backup file not found on disk' }, { status: 404 })
    }

    // Read file
    const fileBuffer = await fs.readFile(backup.filePath)

    // Return file as download
    return new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': 'application/sql',
        'Content-Disposition': `attachment; filename="${backup.originalName}"`,
        'Content-Length': fileBuffer.length.toString()
      }
    })
  } catch (error) {
    console.error('Error downloading backup:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/backups/[id] - Delete backup
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Unauthorized - Super Admin access required' }, { status: 401 })
    }

    const { id } = await params

    const backup = await prisma.databaseBackup.findUnique({
      where: { id }
    })

    if (!backup) {
      return NextResponse.json({ error: 'Backup not found' }, { status: 404 })
    }

    // Delete file from disk if it exists
    if (existsSync(backup.filePath)) {
      try {
        await fs.unlink(backup.filePath)
      } catch (error) {
        console.error('Error deleting backup file:', error)
      }
    }

    // Delete backup record from database
    await prisma.databaseBackup.delete({
      where: { id }
    })

    // Create audit log
    await prisma.auditLog.create({
      data: {
        action: 'DELETE',
        resource: 'DatabaseBackup',
        resourceId: id,
        userId: session.user.id,
        oldValues: {
          filename: backup.filename,
          createdAt: backup.createdAt
        }
      }
    })

    return NextResponse.json({ message: 'Backup deleted successfully' })
  } catch (error) {
    console.error('Error deleting backup:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
