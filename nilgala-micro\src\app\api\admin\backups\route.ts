import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, hasPermission } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { exec } from 'child_process'
import { promisify } from 'util'
import path from 'path'
import fs from 'fs/promises'
import { existsSync } from 'fs'

const execAsync = promisify(exec)

const createBackupSchema = z.object({
  description: z.string().optional(),
  backupType: z.enum(['MANUAL', 'AUTOMATIC']).default('MANUAL')
})

// GET /api/admin/backups - List all backups
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Unauthorized - Super Admin access required' }, { status: 401 })
    }

    const backups = await prisma.databaseBackup.findMany({
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    // Convert BigInt to string for JSON serialization
    const serializedBackups = backups.map(backup => ({
      ...backup,
      fileSize: backup.fileSize.toString()
    }))

    return NextResponse.json(serializedBackups)
  } catch (error) {
    console.error('Error fetching backups:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/admin/backups - Create a new backup
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Unauthorized - Super Admin access required' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createBackupSchema.parse(body)

    // Generate unique filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const filename = `backup_${timestamp}.sql`
    const backupDir = path.join(process.cwd(), 'backups')
    const filePath = path.join(backupDir, filename)

    // Ensure backup directory exists
    try {
      await fs.mkdir(backupDir, { recursive: true })
    } catch (error) {
      console.error('Error creating backup directory:', error)
    }

    // Create backup record in database
    const backup = await prisma.databaseBackup.create({
      data: {
        filename,
        originalName: filename,
        fileSize: 0,
        backupType: validatedData.backupType,
        status: 'CREATING',
        filePath,
        createdBy: session.user.id,
        description: validatedData.description
      },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    })

    // Start backup process asynchronously
    createDatabaseBackup(backup.id, filePath)
      .catch(error => {
        console.error('Backup creation failed:', error)
        // Update backup status to failed
        prisma.databaseBackup.update({
          where: { id: backup.id },
          data: {
            status: 'FAILED',
            errorMessage: error.message,
            completedAt: new Date()
          }
        }).catch(console.error)
      })

    // Convert BigInt to string for JSON serialization
    const serializedBackup = {
      ...backup,
      fileSize: backup.fileSize.toString()
    }

    return NextResponse.json(serializedBackup, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating backup:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Function to create database backup
async function createDatabaseBackup(backupId: string, filePath: string) {
  try {
    const dbUrl = process.env.DATABASE_URL
    if (!dbUrl) {
      throw new Error('DATABASE_URL not found')
    }

    // Parse database URL
    const url = new URL(dbUrl)
    const host = url.hostname
    const port = url.port || '5432'
    const database = url.pathname.slice(1)
    const username = url.username
    const password = url.password

    // Set environment variables for pg_dump
    const env = {
      ...process.env,
      PGPASSWORD: password
    }

    // Create pg_dump command
    const command = `pg_dump -h ${host} -p ${port} -U ${username} -d ${database} --no-password --verbose --clean --if-exists --create > "${filePath}"`

    console.log('Starting database backup...')
    await execAsync(command, { env })

    // Get file size
    const stats = await fs.stat(filePath)
    const fileSize = stats.size

    // Update backup record
    await prisma.databaseBackup.update({
      where: { id: backupId },
      data: {
        status: 'COMPLETED',
        fileSize: BigInt(fileSize),
        completedAt: new Date()
      }
    })

    console.log('Database backup completed successfully')
  } catch (error) {
    console.error('Database backup failed:', error)
    throw error
  }
}
