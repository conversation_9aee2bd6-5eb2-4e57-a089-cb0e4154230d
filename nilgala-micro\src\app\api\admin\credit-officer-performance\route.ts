import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, hasPermission } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/admin/credit-officer-performance - Get performance data for all Credit Officers
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !await hasPermission(session.user.role, 'users:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const month = parseInt(searchParams.get('month') || new Date().getMonth() + 1 + '')
    const year = parseInt(searchParams.get('year') || new Date().getFullYear() + '')

    // Get all active Credit Officers
    const creditOfficers = await prisma.user.findMany({
      where: {
        role: 'CREDIT_OFFICER',
        isActive: true
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        assignedCustomers: {
          select: {
            id: true
          }
        }
      }
    })

    // Get current month targets for all Credit Officers
    const targets = await prisma.creditOfficerTarget.findMany({
      where: {
        month,
        year,
        creditOfficer: {
          isActive: true
        }
      },
      include: {
        creditOfficer: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      }
    })

    // Create a map of targets by Credit Officer ID
    const targetMap = new Map()
    targets.forEach(target => {
      targetMap.set(target.creditOfficerId, target)
    })

    // Get performance data for each Credit Officer
    const performance = await Promise.all(
      creditOfficers.map(async (officer) => {
        const target = targetMap.get(officer.id)

        // Get loans disbursed this month by this Credit Officer's customers
        const loansThisMonth = await prisma.loan.findMany({
          where: {
            customer: {
              assignedTo: officer.id
            },
            status: { in: ['ACTIVE', 'COMPLETED'] },
            disbursedAt: {
              gte: new Date(year, month - 1, 1),
              lt: new Date(year, month, 1)
            }
          },
          select: {
            principalAmount: true,
            id: true
          }
        })

        // Get collections this month for this Credit Officer's customers
        const collectionsThisMonth = await prisma.payment.findMany({
          where: {
            loan: {
              customer: {
                assignedTo: officer.id
              }
            },
            paymentDate: {
              gte: new Date(year, month - 1, 1),
              lt: new Date(year, month, 1)
            }
          },
          select: {
            amount: true,
            id: true
          }
        })

        const loansAmount = loansThisMonth.reduce((sum, loan) => sum + Number(loan.principalAmount), 0)
        const collectionsAmount = collectionsThisMonth.reduce((sum, payment) => sum + Number(payment.amount), 0)

        // Calculate progress percentages
        let loansProgress = 0
        let collectionsProgress = 0
        let overallProgress = 0
        let status: 'on-track' | 'behind' | 'exceeded' | 'no-target' = 'no-target'

        if (target) {
          loansProgress = target.loanTarget > 0 ? (loansAmount / target.loanTarget) * 100 : 0
          collectionsProgress = target.collectionTarget > 0 ? (collectionsAmount / target.collectionTarget) * 100 : 0
          overallProgress = (loansProgress + collectionsProgress) / 2

          if (overallProgress >= 100) {
            status = 'exceeded'
          } else if (overallProgress >= 75) {
            status = 'on-track'
          } else {
            status = 'behind'
          }
        }

        return {
          id: officer.id,
          firstName: officer.firstName,
          lastName: officer.lastName,
          email: officer.email,
          assignedCustomers: officer.assignedCustomers.length,
          currentTarget: target ? {
            id: target.id,
            month: target.month,
            year: target.year,
            loanTarget: Number(target.loanTarget),
            collectionTarget: Number(target.collectionTarget),
            notes: target.notes
          } : undefined,
          monthlyAchievement: {
            loansAmount,
            collectionsAmount,
            loansCount: loansThisMonth.length,
            collectionsCount: collectionsThisMonth.length
          },
          progressPercentage: {
            loans: Math.min(loansProgress, 999), // Cap at 999% for display
            collections: Math.min(collectionsProgress, 999),
            overall: Math.min(overallProgress, 999)
          },
          status
        }
      })
    )

    // Sort by overall progress (descending) and then by name
    performance.sort((a, b) => {
      if (a.progressPercentage.overall !== b.progressPercentage.overall) {
        return b.progressPercentage.overall - a.progressPercentage.overall
      }
      return `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`)
    })

    return NextResponse.json({ 
      performance,
      month,
      year,
      summary: {
        totalOfficers: performance.length,
        withTargets: performance.filter(p => p.currentTarget).length,
        onTrack: performance.filter(p => p.status === 'on-track' || p.status === 'exceeded').length,
        behind: performance.filter(p => p.status === 'behind').length
      }
    })
  } catch (error) {
    console.error('Error fetching credit officer performance:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
