import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, hasPermissionSync } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { getFileFromR2 } from '@/lib/r2-client'

// GET /api/admin/guarantor-documents-status - Check status of all guarantor documents
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermissionSync(session.user.role, 'admin:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get all guarantor documents
    const documents = await prisma.guarantorDocument.findMany({
      include: {
        guarantor: {
          select: {
            firstName: true,
            lastName: true,
            nationalId: true
          }
        }
      },
      orderBy: {
        uploadedAt: 'desc'
      }
    })

    const results = []

    for (const doc of documents) {
      let status = 'unknown'
      let error = null

      // Check if file exists in R2
      try {
        await getFileFromR2(doc.fileKey)
        status = 'available'
      } catch (fileError) {
        if (fileError.message && fileError.message.includes('NoSuchKey')) {
          status = 'missing'
          error = 'File not found in storage'
        } else {
          status = 'error'
          error = fileError.message
        }
      }

      // Determine key format
      const isOldFormat = doc.fileKey.includes('/') && !doc.fileKey.match(/guarantor-documents\/\d+_[a-z0-9]+\.[a-z]+/)
      const keyFormat = isOldFormat ? 'old' : 'new'

      results.push({
        id: doc.id,
        documentName: doc.documentName,
        fileName: doc.fileName,
        fileKey: doc.fileKey,
        fileSize: doc.fileSize,
        uploadedAt: doc.uploadedAt,
        guarantor: {
          name: `${doc.guarantor.firstName} ${doc.guarantor.lastName}`,
          nationalId: doc.guarantor.nationalId
        },
        status,
        keyFormat,
        error,
        needsReupload: status === 'missing' || keyFormat === 'old'
      })
    }

    // Summary statistics
    const summary = {
      total: results.length,
      available: results.filter(r => r.status === 'available').length,
      missing: results.filter(r => r.status === 'missing').length,
      error: results.filter(r => r.status === 'error').length,
      oldFormat: results.filter(r => r.keyFormat === 'old').length,
      newFormat: results.filter(r => r.keyFormat === 'new').length,
      needsReupload: results.filter(r => r.needsReupload).length
    }

    return NextResponse.json({
      summary,
      documents: results
    })

  } catch (error) {
    console.error('Error checking guarantor documents status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
