import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, hasPermissionSync, getRolePermissions, clearPermissionsCache } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { UserRole } from '@prisma/client'
import { z } from 'zod'

const updatePermissionsSchema = z.object({
  role: z.enum(['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER', 'CREDIT_OFFICER', 'CUSTOMER_SERVICE_OFFICER']),
  permissions: z.array(z.string())
})

// Get all available permissions and current role permissions
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Unauthorized - Super Admin access required' }, { status: 401 })
    }

    // Define all available permissions with categories
    const allPermissions = {
      users: [
        'users:create',
        'users:read', 
        'users:update',
        'users:delete'
      ],
      customers: [
        'customers:create',
        'customers:read',
        'customers:update', 
        'customers:delete'
      ],
      loans: [
        'loans:create',
        'loans:read',
        'loans:update',
        'loans:delete',
        'loans:approve',
        'loans:disburse'
      ],
      payments: [
        'payments:create',
        'payments:read',
        'payments:update',
        'payments:delete'
      ],
      reports: [
        'reports:read',
        'reports:export',
        'reports:create'
      ],
      settings: [
        'settings:read',
        'settings:update'
      ],
      audit: [
        'audit:read'
      ],
      documents: [
        'documents:create',
        'documents:read',
        'documents:update',
        'documents:delete'
      ]
    }

    // Get current role permissions from database
    const currentPermissions = await getRolePermissions()

    return NextResponse.json({
      allPermissions,
      currentPermissions,
      roles: Object.keys(currentPermissions)
    })

  } catch (error) {
    console.error('Permissions fetch error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch permissions' },
      { status: 500 }
    )
  }
}

// Update role permissions (this would require a more sophisticated system in production)
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Unauthorized - Super Admin access required' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = updatePermissionsSchema.parse(body)

    // Get current permissions for audit log
    const currentPermissions = await getRolePermissions()
    const oldPermissions = currentPermissions[validatedData.role] || []

    // Update role permissions in database
    await prisma.$transaction(async (tx) => {
      // Delete existing permissions for this role
      await tx.rolePermission.deleteMany({
        where: { role: validatedData.role as UserRole }
      })

      // Insert new permissions
      if (validatedData.permissions.length > 0) {
        await tx.rolePermission.createMany({
          data: validatedData.permissions.map(permission => ({
            role: validatedData.role as UserRole,
            permission: permission,
            isActive: true
          }))
        })
      }
    })

    // Clear permissions cache to force reload
    clearPermissionsCache()

    // Log the permission change
    try {
      // Verify user exists before creating audit log
      const userExists = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: { id: true }
      })

      if (userExists) {
        await prisma.auditLog.create({
          data: {
            action: 'PERMISSIONS_UPDATED',
            resource: 'Role',
            resourceId: validatedData.role,
            userId: session.user.id,
            oldValues: {
              permissions: oldPermissions
            },
            newValues: {
              permissions: validatedData.permissions
            }
          }
        })
      } else {
        console.error('User not found for audit log:', session.user.id)
      }
    } catch (auditError) {
      console.error('Failed to create audit log:', auditError)
      // Continue execution even if audit log fails
    }

    return NextResponse.json({
      message: 'Permissions updated successfully',
      role: validatedData.role,
      permissions: validatedData.permissions
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Permission update error:', error)
    return NextResponse.json(
      { error: 'Failed to update permissions' },
      { status: 500 }
    )
  }
}

// Get audit logs for permission changes
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || !hasPermissionSync(session.user.role as UserRole, 'audit:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { action, page = 1, limit = 10 } = body

    const skip = (page - 1) * limit

    const where = {
      action: { in: ['PERMISSIONS_UPDATED', 'USER_CREATED', 'USER_UPDATED', 'USER_DELETED'] }
    }

    const [logs, total] = await Promise.all([
      prisma.auditLog.findMany({
        where,
        skip,
        take: limit,
        orderBy: { timestamp: 'desc' },
        include: {
          user: {
            select: {
              firstName: true,
              lastName: true,
              email: true
            }
          }
        }
      }),
      prisma.auditLog.count({ where })
    ])

    return NextResponse.json({
      logs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Audit logs fetch error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch audit logs' },
      { status: 500 }
    )
  }
}
