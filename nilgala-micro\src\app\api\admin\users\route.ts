import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, hasPermissionSync } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { UserRole } from '@prisma/client'
import bcrypt from 'bcryptjs'
import { z } from 'zod'

const createUserSchema = z.object({
  email: z.string().email(),
  firstName: z.string().min(1),
  lastName: z.string().min(1),
  phone: z.string().min(1),
  role: z.enum(['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER', 'CREDIT_OFFICER', 'CUSTOMER_SERVICE_OFFICER']),
  password: z.string().min(6),
  isActive: z.boolean().default(true)
})

const updateUserSchema = z.object({
  email: z.string().email().optional(),
  firstName: z.string().min(1).optional(),
  lastName: z.string().min(1).optional(),
  phone: z.string().min(1).optional(),
  role: z.enum(['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER', 'CREDIT_OFFICER', 'CUSTOMER_SERVICE_OFFICER']).optional(),
  isActive: z.boolean().optional(),
  password: z.string().min(6).optional()
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || !hasPermissionSync(session.user.role as UserRole, 'users:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const searchParams = request.nextUrl.searchParams
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const role = searchParams.get('role') || ''

    const skip = (page - 1) * limit

    const where = {
      AND: [
        search ? {
          OR: [
            { firstName: { contains: search, mode: 'insensitive' as const } },
            { lastName: { contains: search, mode: 'insensitive' as const } },
            { email: { contains: search, mode: 'insensitive' as const } }
          ]
        } : {},
        role ? { role: role as UserRole } : {},
        // Higher Management cannot see Super Admin users
        session.user.role === 'HIGHER_MANAGEMENT' ? {
          role: { not: 'SUPER_ADMIN' }
        } : {}
      ]
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          phone: true,
          role: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
          lastLogin: true
        }
      }),
      prisma.user.count({ where })
    ])

    return NextResponse.json({
      users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Users fetch error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || !hasPermissionSync(session.user.role as UserRole, 'users:create')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createUserSchema.parse(body)

    // Higher Management cannot create Super Admin users
    if (session.user.role === 'HIGHER_MANAGEMENT' && validatedData.role === 'SUPER_ADMIN') {
      return NextResponse.json(
        { error: 'Higher Management cannot create Super Admin users' },
        { status: 403 }
      )
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 400 }
      )
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(validatedData.password, 12)

    // Create user
    const user = await prisma.user.create({
      data: {
        ...validatedData,
        password: hashedPassword
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        phone: true,
        role: true,
        isActive: true,
        createdAt: true
      }
    })

    // Log the action
    await prisma.auditLog.create({
      data: {
        action: 'USER_CREATED',
        resource: 'User',
        resourceId: user.id,
        userId: session.user.id,
        newValues: {
          email: user.email,
          role: user.role,
          firstName: user.firstName,
          lastName: user.lastName
        }
      }
    })

    return NextResponse.json(user, { status: 201 })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('User creation error:', error)
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || !hasPermissionSync(session.user.role as UserRole, 'users:update')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { id, action, ...updateData } = body

    if (!id) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 })
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id }
    })

    if (!existingUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Higher Management cannot modify Super Admin users
    if (session.user.role === 'HIGHER_MANAGEMENT' && existingUser.role === 'SUPER_ADMIN') {
      return NextResponse.json(
        { error: 'Higher Management cannot modify Super Admin users' },
        { status: 403 }
      )
    }

    // Handle toggle status action
    if (action === 'toggle_status') {
      // Prevent self-deactivation
      if (existingUser.id === session.user.id && existingUser.isActive) {
        return NextResponse.json({ error: 'Cannot deactivate your own account' }, { status: 400 })
      }

      const newStatus = !existingUser.isActive
      const user = await prisma.user.update({
        where: { id },
        data: { isActive: newStatus },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          isActive: true
        }
      })

      // Log the action
      await prisma.auditLog.create({
        data: {
          action: newStatus ? 'USER_ACTIVATED' : 'USER_BLOCKED',
          resource: 'User',
          resourceId: user.id,
          userId: session.user.id,
          oldValues: {
            isActive: existingUser.isActive
          },
          newValues: {
            isActive: user.isActive,
            userEmail: user.email
          }
        }
      })

      return NextResponse.json({
        message: `User ${newStatus ? 'activated' : 'blocked'} successfully`,
        user
      })
    }

    // Handle regular update
    const validatedData = updateUserSchema.parse(updateData)

    // Higher Management cannot set role to Super Admin
    if (session.user.role === 'HIGHER_MANAGEMENT' && validatedData.role === 'SUPER_ADMIN') {
      return NextResponse.json(
        { error: 'Higher Management cannot assign Super Admin role' },
        { status: 403 }
      )
    }

    // Prepare update data
    const updatePayload: any = { ...validatedData }
    
    // Hash password if provided
    if (validatedData.password) {
      updatePayload.password = await bcrypt.hash(validatedData.password, 12)
    }

    // Update user
    const user = await prisma.user.update({
      where: { id },
      data: updatePayload,
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        phone: true,
        role: true,
        isActive: true,
        updatedAt: true
      }
    })

    // Log the action
    await prisma.auditLog.create({
      data: {
        action: 'USER_UPDATED',
        resource: 'User',
        resourceId: user.id,
        userId: session.user.id,
        newValues: {
          email: user.email,
          role: user.role,
          isActive: user.isActive
        }
      }
    })

    return NextResponse.json(user)

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('User update error:', error)
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || !hasPermissionSync(session.user.role as UserRole, 'users:delete')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const searchParams = request.nextUrl.searchParams
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 })
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id }
    })

    if (!existingUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Prevent self-deletion
    if (existingUser.id === session.user.id) {
      return NextResponse.json({ error: 'Cannot delete your own account' }, { status: 400 })
    }

    // Soft delete by deactivating
    const user = await prisma.user.update({
      where: { id },
      data: { isActive: false },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        isActive: true
      }
    })

    // Log the action
    await prisma.auditLog.create({
      data: {
        action: 'USER_DELETED',
        resource: 'User',
        resourceId: user.id,
        userId: session.user.id,
        newValues: {
          userEmail: user.email,
          isActive: user.isActive
        }
      }
    })

    return NextResponse.json({ message: 'User deactivated successfully' })

  } catch (error) {
    console.error('User deletion error:', error)
    return NextResponse.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    )
  }
}
