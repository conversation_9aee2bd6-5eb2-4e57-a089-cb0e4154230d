import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, hasPermission } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !await hasPermission(session.user.role, 'audit:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const action = searchParams.get('action') || ''
    const resource = searchParams.get('resource') || ''
    const userId = searchParams.get('userId') || ''
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}

    if (action) {
      where.action = { contains: action, mode: 'insensitive' }
    }

    if (resource) {
      where.resource = { contains: resource, mode: 'insensitive' }
    }

    if (userId) {
      where.userId = userId
    }

    if (startDate && endDate) {
      where.timestamp = {
        gte: new Date(startDate),
        lte: new Date(endDate)
      }
    }

    const [auditLogs, total, stats] = await Promise.all([
      // Get audit logs with pagination
      prisma.auditLog.findMany({
        where,
        skip,
        take: limit,
        orderBy: { timestamp: 'desc' },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              role: true
            }
          }
        }
      }),
      
      // Get total count
      prisma.auditLog.count({ where }),
      
      // Get statistics
      getAuditStats(where)
    ])

    // Convert BigInt values to numbers for JSON serialization
    const serializedStats = {
      ...stats,
      recentActivity: stats.recentActivity.map((item: any) => ({
        ...item,
        count: typeof item.count === 'bigint' ? Number(item.count) : item.count
      }))
    }

    return NextResponse.json({
      auditLogs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      stats: serializedStats
    })

  } catch (error) {
    console.error('Error fetching audit logs:', error)
    return NextResponse.json(
      { error: 'Failed to fetch audit logs' },
      { status: 500 }
    )
  }
}

async function getAuditStats(where: any) {
  const [
    actionStats,
    resourceStats,
    userStats,
    recentActivity
  ] = await Promise.all([
    // Actions breakdown
    prisma.auditLog.groupBy({
      by: ['action'],
      where,
      _count: { id: true },
      orderBy: { _count: { id: 'desc' } },
      take: 10
    }),
    
    // Resources breakdown
    prisma.auditLog.groupBy({
      by: ['resource'],
      where,
      _count: { id: true },
      orderBy: { _count: { id: 'desc' } },
      take: 10
    }),
    
    // Most active users
    prisma.auditLog.groupBy({
      by: ['userId'],
      where,
      _count: { id: true },
      orderBy: { _count: { id: 'desc' } },
      take: 5
    }),
    
    // Recent activity trend (last 7 days)
    prisma.$queryRaw`
      SELECT
        DATE_TRUNC('day', timestamp) as day,
        COUNT(*)::int as count
      FROM audit_logs
      WHERE timestamp >= NOW() - INTERVAL '7 days'
      GROUP BY DATE_TRUNC('day', timestamp)
      ORDER BY day
    `
  ])

  // Get user details for top users
  const userIds = userStats.map(stat => stat.userId)
  const users = await prisma.user.findMany({
    where: { id: { in: userIds } },
    select: {
      id: true,
      firstName: true,
      lastName: true,
      role: true
    }
  })

  const userStatsWithDetails = userStats.map(stat => ({
    ...stat,
    user: users.find(user => user.id === stat.userId)
  }))

  return {
    actionStats,
    resourceStats,
    userStats: userStatsWithDetails,
    recentActivity
  }
}

// Export audit logs
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !await hasPermission(session.user.role, 'reports:export')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { format = 'json', filters = {} } = body

    // Build where clause from filters
    const where: any = {}
    
    if (filters.action) {
      where.action = { contains: filters.action, mode: 'insensitive' }
    }
    
    if (filters.resource) {
      where.resource = { contains: filters.resource, mode: 'insensitive' }
    }
    
    if (filters.userId) {
      where.userId = filters.userId
    }
    
    if (filters.startDate && filters.endDate) {
      where.timestamp = {
        gte: new Date(filters.startDate),
        lte: new Date(filters.endDate)
      }
    }

    const auditLogs = await prisma.auditLog.findMany({
      where,
      orderBy: { timestamp: 'desc' },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
            role: true
          }
        }
      }
    })

    if (format === 'csv') {
      // Convert to CSV format
      const csvHeaders = [
        'Timestamp',
        'Action',
        'Resource',
        'Resource ID',
        'User',
        'User Role',
        'IP Address',
        'User Agent',
        'Old Values',
        'New Values'
      ]

      const csvRows = auditLogs.map(log => [
        log.timestamp.toISOString(),
        log.action,
        log.resource,
        log.resourceId || '',
        `${log.user.firstName} ${log.user.lastName}`,
        log.user.role,
        log.ipAddress || '',
        log.userAgent || '',
        log.oldValues ? JSON.stringify(log.oldValues) : '',
        log.newValues ? JSON.stringify(log.newValues) : ''
      ])

      const csvContent = [csvHeaders, ...csvRows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n')

      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="audit-logs-${new Date().toISOString().split('T')[0]}.csv"`
        }
      })
    }

    // Default JSON format
    return NextResponse.json({
      auditLogs,
      exportedAt: new Date().toISOString(),
      totalRecords: auditLogs.length,
      filters
    })

  } catch (error) {
    console.error('Error exporting audit logs:', error)
    return NextResponse.json(
      { error: 'Failed to export audit logs' },
      { status: 500 }
    )
  }
}
