import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('startDate') || new Date().toISOString().split('T')[0]
    const endDate = searchParams.get('endDate') || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]

    // Fetch payment schedules as calendar events
    const paymentSchedules = await prisma.paymentSchedule.findMany({
      where: {
        dueDate: {
          gte: new Date(startDate),
          lte: new Date(endDate)
        }
      },
      include: {
        loan: {
          include: {
            customer: true
          }
        }
      },
      orderBy: {
        dueDate: 'asc'
      }
    })

    // Transform payment schedules to calendar events
    const events = paymentSchedules.map(schedule => ({
      id: schedule.id,
      title: `Payment Due - ${schedule.loan.customer.firstName} ${schedule.loan.customer.lastName}`,
      date: schedule.dueDate.toISOString().split('T')[0],
      time: '10:00', // Default time for payments
      type: 'payment',
      status: schedule.status === 'PAID' ? 'completed' :
              (new Date(schedule.dueDate) < new Date() ? 'overdue' : 'pending'),
      amount: Number(schedule.totalAmount),
      customer: {
        name: `${schedule.loan.customer.firstName} ${schedule.loan.customer.lastName}`,
        id: schedule.loan.customer.id
      },
      loan: {
        id: schedule.loan.id,
        amount: Number(schedule.loan.amount)
      }
    }))

    // Calculate statistics
    const today = new Date().toISOString().split('T')[0]
    const todayPayments = events.filter(event => 
      event.date === today && event.type === 'payment' && event.status === 'pending'
    ).length

    const overduePayments = events.filter(event => 
      event.status === 'overdue' && event.type === 'payment'
    ).length

    const upcomingMeetings = 0 // Placeholder for meetings
    const totalScheduled = events.length

    const stats = {
      todayPayments,
      overduePayments,
      upcomingMeetings,
      totalScheduled
    }

    return NextResponse.json({
      events,
      stats
    })

  } catch (error) {
    console.error('Calendar fetch error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch calendar data' },
      { status: 500 }
    )
  }
}

// Create new calendar event
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { title, date, time, type, customerId, loanId, amount, description } = body

    // For now, we'll create a simple event record
    // In a full implementation, you might want a separate CalendarEvent model
    
    // If it's a payment event, create a payment schedule
    if (type === 'payment' && loanId) {
      // Get the loan to calculate installment number
      const existingSchedules = await prisma.paymentSchedule.count({
        where: { loanId }
      })

      const paymentSchedule = await prisma.paymentSchedule.create({
        data: {
          loanId,
          installmentNumber: existingSchedules + 1,
          dueDate: new Date(`${date}T${time || '10:00'}:00`),
          principalAmount: amount || 0,
          interestAmount: 0,
          totalAmount: amount || 0,
          status: 'PENDING'
        }
      })

      return NextResponse.json({
        id: paymentSchedule.id,
        title,
        date,
        time: time || '10:00',
        type,
        status: 'pending',
        amount,
        message: 'Payment schedule created successfully'
      })
    }

    // For other event types, you might want to create a separate events table
    // For now, return a mock response
    return NextResponse.json({
      id: `event_${Date.now()}`,
      title,
      date,
      time: time || '10:00',
      type,
      status: 'pending',
      message: 'Event created successfully'
    })

  } catch (error) {
    console.error('Calendar create error:', error)
    return NextResponse.json(
      { error: 'Failed to create calendar event' },
      { status: 500 }
    )
  }
}
