import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { hasPermission } from '@/lib/auth'
import { z } from 'zod'

// Validation schema for company settings
const updateCompanySettingsSchema = z.object({
  companyName: z.string().min(1, 'Company name is required').optional(),
  companyAddress: z.string().optional(),
  companyPhone: z.string().optional(),
  companyEmail: z.string().email().optional(),
  companyWebsite: z.string().url().optional(),
  companyLogo: z.string().optional(),
  primaryColor: z.string().optional(),
  secondaryColor: z.string().optional(),
  systemTitle: z.string().optional(),
})

// GET /api/company-settings - Get company settings
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || !hasPermission(session.user.role, 'settings:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get all company-related system configs
    const configs = await prisma.systemConfig.findMany({
      where: {
        category: 'COMPANY_SETTINGS'
      }
    })

    // Convert to key-value object
    const settings = configs.reduce((acc, config) => {
      acc[config.key] = config.value
      return acc
    }, {} as Record<string, string>)

    // Set defaults if not found
    const companySettings = {
      companyName: settings.COMPANY_NAME || 'Nilgala Micro Finance',
      companyAddress: settings.COMPANY_ADDRESS || 'Nilgala, Sri Lanka',
      companyPhone: settings.COMPANY_PHONE || '',
      companyEmail: settings.COMPANY_EMAIL || '',
      companyWebsite: settings.COMPANY_WEBSITE || '',
      companyLogo: settings.COMPANY_LOGO || '',
      primaryColor: settings.PRIMARY_COLOR || '#1f2937',
      secondaryColor: settings.SECONDARY_COLOR || '#3b82f6',
      systemTitle: settings.SYSTEM_TITLE || 'Nilgala Micro',
    }

    return NextResponse.json(companySettings)
  } catch (error) {
    console.error('Error fetching company settings:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/company-settings - Update company settings
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermission(session.user.role, 'settings:update')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = updateCompanySettingsSchema.parse(body)

    // Map form fields to system config keys
    const configMappings = {
      companyName: 'COMPANY_NAME',
      companyAddress: 'COMPANY_ADDRESS',
      companyPhone: 'COMPANY_PHONE',
      companyEmail: 'COMPANY_EMAIL',
      companyWebsite: 'COMPANY_WEBSITE',
      companyLogo: 'COMPANY_LOGO',
      primaryColor: 'PRIMARY_COLOR',
      secondaryColor: 'SECONDARY_COLOR',
      systemTitle: 'SYSTEM_TITLE',
    }

    // Update each setting
    const updatedSettings = {}
    for (const [fieldName, configKey] of Object.entries(configMappings)) {
      const value = validatedData[fieldName as keyof typeof validatedData]
      if (value !== undefined) {
        await prisma.systemConfig.upsert({
          where: { key: configKey },
          update: { 
            value: value.toString(),
            category: 'COMPANY_SETTINGS'
          },
          create: { 
            key: configKey,
            value: value.toString(),
            category: 'COMPANY_SETTINGS',
            description: `Company ${fieldName.replace(/([A-Z])/g, ' $1').toLowerCase()}`
          }
        })
        updatedSettings[fieldName] = value
      }
    }

    // Create audit log
    try {
      const userExists = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: { id: true }
      })

      if (userExists) {
        await prisma.auditLog.create({
          data: {
            action: 'UPDATE',
            resource: 'CompanySettings',
            resourceId: 'company-settings',
            userId: session.user.id,
            newValues: updatedSettings
          }
        })
      }
    } catch (auditError) {
      console.error('Failed to create audit log:', auditError)
    }

    return NextResponse.json({ 
      message: 'Company settings updated successfully',
      settings: updatedSettings
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating company settings:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
