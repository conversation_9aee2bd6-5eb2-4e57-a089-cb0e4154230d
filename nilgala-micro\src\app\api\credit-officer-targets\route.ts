import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, hasPermissionSync } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { UserRole } from '@prisma/client'
import { z } from 'zod'

// Validation schema for creating/updating targets
const targetSchema = z.object({
  creditOfficerId: z.string().min(1, 'Credit Officer is required'),
  month: z.number().min(1).max(12, 'Month must be between 1 and 12'),
  year: z.number().min(2020).max(2050, 'Year must be between 2020 and 2050'),
  loanTarget: z.number().min(0, 'Loan target cannot be negative'),
  collectionTarget: z.number().min(0, 'Collection target cannot be negative'),
  notes: z.string().optional(),
})

// GET /api/credit-officer-targets - Get targets for Credit Officers
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions - only Manager, Higher Management, and Super Admin can view targets
    const canManageTargets = hasPermissionSync(session.user.role as UserRole, 'users:update') ||
                            ['MANAGER', 'HIGHER_MANAGEMENT', 'SUPER_ADMIN'].includes(session.user.role)

    if (!canManageTargets) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const searchParams = request.nextUrl.searchParams
    const creditOfficerId = searchParams.get('creditOfficerId')
    const month = searchParams.get('month')
    const year = searchParams.get('year')

    // Build where clause
    const where: any = {}
    if (creditOfficerId) where.creditOfficerId = creditOfficerId
    if (month) where.month = parseInt(month)
    if (year) where.year = parseInt(year)

    const targets = await prisma.creditOfficerTarget.findMany({
      where,
      include: {
        creditOfficer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        setter: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            role: true
          }
        }
      },
      orderBy: [
        { year: 'desc' },
        { month: 'desc' },
        { creditOfficer: { firstName: 'asc' } }
      ]
    })

    return NextResponse.json({ targets })

  } catch (error) {
    console.error('Error fetching credit officer targets:', error)
    return NextResponse.json(
      { error: 'Failed to fetch targets' },
      { status: 500 }
    )
  }
}

// POST /api/credit-officer-targets - Create or update target
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions - only Manager, Higher Management, and Super Admin can set targets
    const canManageTargets = hasPermissionSync(session.user.role as UserRole, 'users:update') ||
                            ['MANAGER', 'HIGHER_MANAGEMENT', 'SUPER_ADMIN'].includes(session.user.role)

    if (!canManageTargets) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = targetSchema.parse(body)

    // Verify the Credit Officer exists and is active
    const creditOfficer = await prisma.user.findUnique({
      where: { id: validatedData.creditOfficerId },
      select: { id: true, role: true, isActive: true, firstName: true, lastName: true }
    })

    if (!creditOfficer) {
      return NextResponse.json(
        { error: 'Credit Officer not found' },
        { status: 404 }
      )
    }

    if (creditOfficer.role !== 'CREDIT_OFFICER') {
      return NextResponse.json(
        { error: 'User is not a Credit Officer' },
        { status: 400 }
      )
    }

    if (!creditOfficer.isActive) {
      return NextResponse.json(
        { error: 'Cannot set targets for inactive Credit Officer' },
        { status: 400 }
      )
    }

    // Check if target already exists for this officer, month, and year
    const existingTarget = await prisma.creditOfficerTarget.findUnique({
      where: {
        creditOfficerId_month_year: {
          creditOfficerId: validatedData.creditOfficerId,
          month: validatedData.month,
          year: validatedData.year
        }
      }
    })

    let target
    if (existingTarget) {
      // Update existing target
      target = await prisma.creditOfficerTarget.update({
        where: { id: existingTarget.id },
        data: {
          loanTarget: validatedData.loanTarget,
          collectionTarget: validatedData.collectionTarget,
          notes: validatedData.notes,
          setBy: session.user.id,
          updatedAt: new Date()
        },
        include: {
          creditOfficer: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          },
          setter: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              role: true
            }
          }
        }
      })
    } else {
      // Create new target
      target = await prisma.creditOfficerTarget.create({
        data: {
          ...validatedData,
          setBy: session.user.id
        },
        include: {
          creditOfficer: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          },
          setter: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              role: true
            }
          }
        }
      })
    }

    return NextResponse.json({ target }, { status: existingTarget ? 200 : 201 })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating/updating credit officer target:', error)
    return NextResponse.json(
      { error: 'Failed to save target' },
      { status: 500 }
    )
  }
}
