import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { hasPermission } from '@/lib/auth'

// GET /api/customers/[id]/detailed - Get detailed customer information for loan application
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !await hasPermission(session.user.role, 'customers:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Get detailed customer information
    const customer = await prisma.customer.findUnique({
      where: { id },
      include: {
        // Previous loans
        loans: {
          select: {
            id: true,
            loanNumber: true,
            principalAmount: true,
            totalAmount: true,
            status: true,
            applicationDate: true,
            disbursementDate: true,
            loanType: {
              select: {
                name: true,
                category: true
              }
            },
            payments: {
              select: {
                amount: true,
                paymentDate: true
              }
            }
          },
          orderBy: {
            applicationDate: 'desc'
          }
        },
        // Guarantor history - loans where this customer was a guarantor
        guarantorFor: {
          select: {
            id: true,
            guarantorType: true,
            liabilityAmount: true,
            loan: {
              select: {
                id: true,
                loanNumber: true,
                principalAmount: true,
                status: true,
                applicationDate: true,
                customer: {
                  select: {
                    firstName: true,
                    lastName: true,
                    phone: true
                  }
                },
                loanType: {
                  select: {
                    name: true
                  }
                }
              }
            }
          },
          orderBy: {
            loan: {
              applicationDate: 'desc'
            }
          }
        },
        // Assigned officer
        assignedOfficer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            role: true
          }
        }
      }
    })

    if (!customer) {
      return NextResponse.json({ error: 'Customer not found' }, { status: 404 })
    }

    // Calculate loan statistics
    const activeLoanCount = customer.loans.filter(loan => 
      ['ACTIVE', 'DISBURSED'].includes(loan.status)
    ).length

    const totalLoanAmount = customer.loans
      .filter(loan => ['ACTIVE', 'DISBURSED'].includes(loan.status))
      .reduce((sum, loan) => sum + Number(loan.totalAmount), 0)

    const totalPaidAmount = customer.loans.reduce((sum, loan) => {
      const loanPaid = loan.payments.reduce((paidSum, payment) => 
        paidSum + Number(payment.amount), 0
      )
      return sum + loanPaid
    }, 0)

    const outstandingAmount = totalLoanAmount - totalPaidAmount

    // Calculate guarantor statistics
    const activeGuarantorCount = customer.guarantorFor.filter(lg =>
      ['ACTIVE', 'DISBURSED'].includes(lg.loan.status)
    ).length

    const totalGuarantorLiability = customer.guarantorFor
      .filter(lg => ['ACTIVE', 'DISBURSED'].includes(lg.loan.status))
      .reduce((sum, lg) => sum + Number(lg.liabilityAmount), 0)

    // Recent loan history (last 5 loans)
    const recentLoans = customer.loans.slice(0, 5).map(loan => ({
      id: loan.id,
      loanNumber: loan.loanNumber,
      loanType: loan.loanType.name,
      category: loan.loanType.category,
      principalAmount: Number(loan.principalAmount),
      totalAmount: Number(loan.totalAmount),
      status: loan.status,
      applicationDate: loan.applicationDate,
      disbursedDate: loan.disbursementDate,
      totalPaid: loan.payments.reduce((sum, payment) => sum + Number(payment.amount), 0)
    }))

    // Recent guarantor history (last 5 guarantor roles)
    const recentGuarantorRoles = customer.guarantorFor.slice(0, 5).map(lg => ({
      id: lg.id,
      loanNumber: lg.loan.loanNumber,
      loanType: lg.loan.loanType.name,
      borrowerName: `${lg.loan.customer.firstName} ${lg.loan.customer.lastName}`,
      borrowerPhone: lg.loan.customer.phone,
      principalAmount: Number(lg.loan.principalAmount),
      liabilityAmount: Number(lg.liabilityAmount),
      status: lg.loan.status,
      applicationDate: lg.loan.applicationDate,
      guarantorType: lg.guarantorType
    }))

    // Risk assessment
    const riskFactors = []
    
    if (activeLoanCount > 2) {
      riskFactors.push(`Has ${activeLoanCount} active loans`)
    }
    
    if (outstandingAmount > customer.monthlyIncome * 12) {
      riskFactors.push('Outstanding amount exceeds annual income')
    }
    
    if (activeGuarantorCount > 1) {
      riskFactors.push(`Active guarantor for ${activeGuarantorCount} loans`)
    }
    
    if (totalGuarantorLiability > customer.monthlyIncome * 6) {
      riskFactors.push('Guarantor liability exceeds 6 months income')
    }

    const hasDefaultHistory = customer.loans.some(loan => loan.status === 'DEFAULTED')
    if (hasDefaultHistory) {
      riskFactors.push('Has loan default history')
    }

    const response = {
      customer: {
        id: customer.id,
        firstName: customer.firstName,
        lastName: customer.lastName,
        email: customer.email,
        phone: customer.phone,
        nationalId: customer.nationalId,
        monthlyIncome: Number(customer.monthlyIncome),
        assignedOfficer: customer.assignedOfficer
      },
      loanSummary: {
        totalLoans: customer.loans.length,
        activeLoans: activeLoanCount,
        totalLoanAmount,
        totalPaidAmount,
        outstandingAmount,
        recentLoans
      },
      guarantorSummary: {
        totalGuarantorRoles: customer.guarantorFor.length,
        activeGuarantorRoles: activeGuarantorCount,
        totalGuarantorLiability,
        recentGuarantorRoles
      },
      riskAssessment: {
        riskFactors,
        riskLevel: riskFactors.length === 0 ? 'LOW' : 
                  riskFactors.length <= 2 ? 'MEDIUM' : 'HIGH'
      }
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error fetching detailed customer information:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
