import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, hasPermission } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { readFile } from 'fs/promises'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; documentId: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !await hasPermission(session.user.role, 'documents:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: customerId, documentId } = await params

    // Find the document
    const document = await prisma.document.findFirst({
      where: {
        id: documentId,
        customerId
      }
    })

    if (!document) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 })
    }

    try {
      // Read the file from disk
      const fileBuffer = await readFile(document.storageUrl!)

      // Return the file for inline viewing
      return new NextResponse(fileBuffer, {
        headers: {
          'Content-Type': document.mimeType,
          'Content-Disposition': `inline; filename="${document.originalName}"`,
          'Content-Length': document.fileSize.toString(),
          'Cache-Control': 'private, max-age=3600', // Cache for 1 hour
        }
      })
    } catch (error) {
      console.error('Error reading file:', error)
      return NextResponse.json({ error: 'File not found on disk' }, { status: 404 })
    }

  } catch (error) {
    console.error('Error viewing document:', error)
    return NextResponse.json(
      { error: 'Failed to view document' },
      { status: 500 }
    )
  }
}
