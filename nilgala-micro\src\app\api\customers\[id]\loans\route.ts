import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, hasPermission } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || !await hasPermission(session.user.role, 'customers:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: customerId } = await params
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    // First, verify the customer exists
    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        nationalId: true
      }
    })

    if (!customer) {
      return NextResponse.json({ error: 'Customer not found' }, { status: 404 })
    }

    // Fetch loans for the customer
    const [loans, total] = await Promise.all([
      prisma.loan.findMany({
        where: { customerId },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          loanType: {
            select: {
              id: true,
              name: true
            }
          },
          payments: {
            select: {
              amount: true
            }
          },
          _count: {
            select: {
              payments: true,
              schedules: true
            }
          }
        }
      }),
      prisma.loan.count({
        where: { customerId }
      })
    ])

    const pages = Math.ceil(total / limit)

    return NextResponse.json({
      customer,
      loans,
      pagination: {
        page,
        limit,
        total,
        pages
      }
    })

  } catch (error) {
    console.error('Error fetching customer loans:', error)
    return NextResponse.json(
      { error: 'Failed to fetch customer loans' },
      { status: 500 }
    )
  }
}
