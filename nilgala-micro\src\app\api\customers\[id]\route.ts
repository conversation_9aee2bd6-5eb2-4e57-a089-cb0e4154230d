import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { hasPermission } from '@/lib/auth'
import { z } from 'zod'

// Validation schema for customer update
const updateCustomerSchema = z.object({
  firstName: z.string().min(1, 'First name is required').optional(),
  lastName: z.string().min(1, 'Last name is required').optional(),
  email: z.string().email('Invalid email address').optional(),
  phone: z.string().min(10, 'Phone number must be at least 10 digits').optional(),
  additionalPhones: z.array(z.string().min(10, 'Phone number must be at least 10 digits')).optional(),
  nationalId: z.string().min(10, 'National ID is required').optional(),
  dateOfBirth: z.string().transform((str) => new Date(str)).optional(),
  gender: z.enum(['MALE', 'FEMALE', 'OTHER']).optional(),
  maritalStatus: z.enum(['SINGLE', 'MARRIED', 'DIVORCED', 'WIDOWED']).optional(),
  address: z.string().min(1, 'Address is required').optional(),
  city: z.string().min(1, 'City is required').optional(),
  state: z.string().min(1, 'State is required').optional(),
  postalCode: z.string().optional(),
  employmentType: z.enum(['EMPLOYED', 'SELF_EMPLOYED', 'UNEMPLOYED', 'RETIRED']).optional(),
  employer: z.string().optional(),
  monthlyIncome: z.number().positive('Monthly income must be positive').optional(),
  employmentDuration: z.number().optional(),
  bankAccount: z.string().optional(),
  bankName: z.string().optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'SUSPENDED', 'BLACKLISTED']).optional(),
})

// GET /api/customers/[id] - Get customer by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || !hasPermission(session.user.role, 'customers:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const customer = await prisma.customer.findUnique({
      where: { id },
      include: {
        loans: {
          orderBy: { createdAt: 'desc' }
        },
        documents: {
          orderBy: { createdAt: 'desc' }
        },
        guarantorFor: {
          include: {
            loan: {
              include: {
                customer: {
                  select: {
                    firstName: true,
                    lastName: true,
                    id: true,
                  }
                }
              }
            }
          }
        },
        _count: {
          select: {
            loans: true,
            documents: true,
          }
        }
      }
    })

    if (!customer) {
      return NextResponse.json({ error: 'Customer not found' }, { status: 404 })
    }

    return NextResponse.json(customer)
  } catch (error) {
    console.error('Error fetching customer:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/customers/[id] - Update customer
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || !hasPermission(session.user.role, 'customers:update')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()
    const validatedData = updateCustomerSchema.parse(body)

    // Check if customer exists
    const existingCustomer = await prisma.customer.findUnique({
      where: { id }
    })

    if (!existingCustomer) {
      return NextResponse.json({ error: 'Customer not found' }, { status: 404 })
    }

    // If phone numbers are being updated, check for duplicates
    const allPhones = [
      ...(validatedData.phone ? [validatedData.phone] : []),
      ...(validatedData.additionalPhones || [])
    ].filter(Boolean)

    if (allPhones.length > 0) {
      const phoneExists = await prisma.customer.findFirst({
        where: {
          AND: [
            { id: { not: id } },
            {
              OR: [
                { phone: { in: allPhones } },
                { additionalPhones: { hasSome: allPhones } }
              ]
            }
          ]
        }
      })

      if (phoneExists) {
        return NextResponse.json(
          { error: 'One or more phone numbers already exist' },
          { status: 400 }
        )
      }
    }

    const customer = await prisma.customer.update({
      where: { id },
      data: {
        ...validatedData,
        updatedAt: new Date(),
      },
      include: {
        _count: {
          select: {
            loans: true,
            documents: true,
          }
        }
      }
    })

    // Create audit log
    await prisma.auditLog.create({
      data: {
        action: 'UPDATE',
        resource: 'Customer',
        resourceId: customer.id,
        userId: session.user.id,
        newValues: {
          firstName: customer.firstName,
          lastName: customer.lastName,
          id: customer.id
        }
      }
    })

    return NextResponse.json(customer)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating customer:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/customers/[id] - Delete customer (soft delete)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || !hasPermission(session.user.role, 'customers:delete')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    // Check if customer exists
    const existingCustomer = await prisma.customer.findUnique({
      where: { id },
      include: {
        loans: {
          where: {
            status: {
              in: ['PENDING', 'APPROVED', 'DISBURSED', 'ACTIVE']
            }
          }
        }
      }
    })

    if (!existingCustomer) {
      return NextResponse.json({ error: 'Customer not found' }, { status: 404 })
    }

    // Check if customer has active loans
    if (existingCustomer.loans.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete customer with active loans' },
        { status: 400 }
      )
    }

    // Soft delete by updating status
    const customer = await prisma.customer.update({
      where: { id },
      data: {
        status: 'INACTIVE',
        updatedAt: new Date(),
      }
    })

    // Create audit log
    await prisma.auditLog.create({
      data: {
        action: 'DELETE',
        resource: 'Customer',
        resourceId: customer.id,
        userId: session.user.id,
        newValues: {
          firstName: customer.firstName,
          lastName: customer.lastName,
          status: customer.status
        }
      }
    })

    return NextResponse.json({ message: 'Customer deleted successfully' })
  } catch (error) {
    console.error('Error deleting customer:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
