import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, hasPermissionSync } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateStatusSchema = z.object({
  status: z.enum(['INACTIVE', 'ACTIVE', 'SUSPENDED', 'BLACKLISTED']),
  notes: z.string().optional()
})

// Update customer status
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermissionSync(session.user.role, 'customers:update')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only Admin, Higher Management, and Manager can change status to SUSPENDED or BLACKLISTED
    const body = await request.json()
    const { status, notes } = updateStatusSchema.parse(body)

    if ((status === 'SUSPENDED' || status === 'BLACKLISTED') && 
        !['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER'].includes(session.user.role)) {
      return NextResponse.json({ 
        error: 'Insufficient permissions to suspend or blacklist customers' 
      }, { status: 403 })
    }

    // Get current customer
    const customer = await prisma.customer.findUnique({
      where: { id: params.id },
      include: {
        loans: {
          where: {
            status: {
              in: ['ACTIVE', 'DISBURSED']
            }
          }
        }
      }
    })

    if (!customer) {
      return NextResponse.json({ error: 'Customer not found' }, { status: 404 })
    }

    // Business logic validation
    if (status === 'ACTIVE' && customer.loans.length === 0) {
      return NextResponse.json({ 
        error: 'Cannot set customer to ACTIVE status without active loans' 
      }, { status: 400 })
    }

    // Update customer status
    const updatedCustomer = await prisma.customer.update({
      where: { id: params.id },
      data: {
        status,
        statusUpdatedBy: session.user.id,
        statusUpdatedAt: new Date(),
        statusNotes: notes || null
      },
      include: {
        loans: {
          select: {
            id: true,
            loanNumber: true,
            status: true,
            principalAmount: true
          }
        }
      }
    })

    // Create audit log
    await prisma.auditLog.create({
      data: {
        action: 'UPDATE_STATUS',
        resource: 'Customer',
        resourceId: customer.id,
        userId: session.user.id,
        oldValues: {
          status: customer.status,
          statusNotes: customer.statusNotes
        },
        newValues: {
          status,
          statusNotes: notes
        }
      }
    })

    return NextResponse.json({
      message: 'Customer status updated successfully',
      customer: updatedCustomer
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating customer status:', error)
    return NextResponse.json(
      { error: 'Failed to update customer status' },
      { status: 500 }
    )
  }
}

// Get customer status history
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermissionSync(session.user.role, 'customers:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get customer with current status
    const customer = await prisma.customer.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        status: true,
        statusUpdatedBy: true,
        statusUpdatedAt: true,
        statusNotes: true
      }
    })

    if (!customer) {
      return NextResponse.json({ error: 'Customer not found' }, { status: 404 })
    }

    // Get status change history from audit logs
    const statusHistory = await prisma.auditLog.findMany({
      where: {
        resource: 'Customer',
        resourceId: params.id,
        action: 'UPDATE_STATUS'
      },
      orderBy: { timestamp: 'desc' },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            role: true
          }
        }
      }
    })

    return NextResponse.json({
      customer,
      statusHistory
    })

  } catch (error) {
    console.error('Error fetching customer status:', error)
    return NextResponse.json(
      { error: 'Failed to fetch customer status' },
      { status: 500 }
    )
  }
}
