import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { hasPermissionSync } from '@/lib/auth'
import { AuditLogger } from '@/lib/audit-logger'
import { z } from 'zod'
import { handleApiError } from '@/lib/validation-utils'

// Validation schema for customer creation
const createCustomerSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email address').optional(),
  phone: z.string().min(10, 'Phone number must be at least 10 digits'),
  additionalPhones: z.array(z.string().min(10, 'Phone number must be at least 10 digits')).optional().default([]),
  nationalId: z.string().min(10, 'National ID is required'),
  dateOfBirth: z.string().transform((str) => new Date(str)),
  gender: z.enum(['MALE', 'FEMALE', 'OTHER']),
  maritalStatus: z.enum(['SINGLE', 'MARRIED', 'DIVORCED', 'WIDOWED']),
  address: z.string().min(1, 'Address is required'),
  city: z.string().min(1, 'City is required'),
  state: z.string().min(1, 'State is required'),
  postalCode: z.string().min(1, 'Postal code is required'),
  employmentType: z.enum(['EMPLOYED', 'SELF_EMPLOYED', 'UNEMPLOYED', 'RETIRED', 'STUDENT']),
  employer: z.string().optional(),
  monthlyIncome: z.number().positive('Monthly income must be positive'),
  employmentDuration: z.number().optional(),
  bankAccount: z.string().optional(),
  bankName: z.string().optional(),
  assignedTo: z.string().optional(), // Credit Officer assignment
})

// GET /api/customers - List customers with pagination and search
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermissionSync(session.user.role, 'customers:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status')
    const assignmentFilter = searchParams.get('assignmentFilter')

    const skip = (page - 1) * limit

    // Build where clause for search and filters
    const where: any = {}

    if (search) {
      where.OR = [
        { firstName: { contains: search, mode: 'insensitive' } },
        { lastName: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search } },
        { nationalId: { contains: search } },
      ]
    }

    // Status filter
    if (status && status !== 'ALL') {
      where.status = status
    }

    // Assignment filter for Credit Officers
    if (assignmentFilter === 'ASSIGNED_TO_ME' && session.user.role === 'CREDIT_OFFICER') {
      where.assignedTo = session.user.id
    }



    const [customers, total] = await Promise.all([
      prisma.customer.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          phone: true,
          nationalId: true,
          status: true,
          monthlyIncome: true,
          createdAt: true,
          assignedTo: true,
          assignedOfficer: {
            select: {
              id: true,
              firstName: true,
              lastName: true
            }
          },
          _count: {
            select: {
              loans: true,
            }
          }
        }
      }),
      prisma.customer.count({ where })
    ])

    return NextResponse.json({
      customers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching customers:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/customers - Create new customer
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermissionSync(session.user.role, 'customers:create')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    console.log('Received customer data:', JSON.stringify(body, null, 2))

    // Transform empty strings to undefined for optional fields
    const transformedBody = {
      ...body,
      email: body.email === '' ? undefined : body.email,
      employer: body.employer === '' ? undefined : body.employer,
      bankAccount: body.bankAccount === '' ? undefined : body.bankAccount,
      bankName: body.bankName === '' ? undefined : body.bankName,
      employmentDuration: body.employmentDuration === '' ? undefined : body.employmentDuration,
    }

    const validatedData = createCustomerSchema.parse(transformedBody)

    // If no assignedTo is provided, assign to current user if they are a Credit Officer
    if (!validatedData.assignedTo && session.user.role === 'CREDIT_OFFICER') {
      validatedData.assignedTo = session.user.id
    }

    // Validate assignedTo user exists and is a Credit Officer
    if (validatedData.assignedTo) {
      const assignedUser = await prisma.user.findUnique({
        where: { id: validatedData.assignedTo },
        select: { id: true, role: true, isActive: true }
      })

      if (!assignedUser) {
        return NextResponse.json(
          { error: 'Assigned user not found' },
          { status: 400 }
        )
      }

      if (assignedUser.role !== 'CREDIT_OFFICER') {
        return NextResponse.json(
          { error: 'Customer can only be assigned to Credit Officers' },
          { status: 400 }
        )
      }

      if (!assignedUser.isActive) {
        return NextResponse.json(
          { error: 'Cannot assign customer to inactive user' },
          { status: 400 }
        )
      }
    }

    // Collect all phone numbers for duplicate checking
    const allPhones = [validatedData.phone, ...(validatedData.additionalPhones || [])]

    // Check if customer with same National ID or any phone number already exists
    const existingCustomer = await prisma.customer.findFirst({
      where: {
        OR: [
          { nationalId: validatedData.nationalId },
          { phone: { in: allPhones } },
          { additionalPhones: { hasSome: allPhones } }
        ]
      }
    })

    if (existingCustomer) {
      return NextResponse.json(
        { error: 'Customer with this National ID or phone number already exists' },
        { status: 400 }
      )
    }

    const customer = await prisma.customer.create({
      data: validatedData,
      include: {
        assignedOfficer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            role: true
          }
        },
        _count: {
          select: {
            loans: true,
            documents: true,
          }
        }
      }
    })

    // Create enhanced audit log
    const auditContext = AuditLogger.getContextFromRequest(request, session.user.id)
    await AuditLogger.logCRUD(
      auditContext,
      'CREATE',
      'Customer',
      customer.id,
      undefined,
      {
        firstName: customer.firstName,
        lastName: customer.lastName,
        nationalId: customer.nationalId,
        phone: customer.phone,
        email: customer.email,
        employmentType: customer.employmentType,
        monthlyIncome: customer.monthlyIncome,
        status: customer.status
      }
    )

    return NextResponse.json(customer, { status: 201 })
  } catch (error) {
    return handleApiError(error, 'creating customer')
  }
}
