import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { UserRole } from '@prisma/client'

// Removed caching for real-time dashboard updates
// Dashboard stats will now always fetch fresh data from database

// Function to clear cache (kept for compatibility but does nothing)
export function clearDashboardCache(userId?: string) {
  // No-op - caching disabled for real-time updates
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userRole = session.user.role as UserRole
    const searchParams = request.nextUrl.searchParams
    const role = searchParams.get('role') || userRole

    // Real-time dashboard - no caching for immediate updates

    // Get basic counts with optimized queries
    const [
      userStats,
      customerStats,
      loanStats,
      paymentStats,
      overduePayments
    ] = await Promise.all([
      // Single query for user counts
      prisma.user.groupBy({
        by: ['isActive'],
        _count: true
      }),
      // Single query for customer counts
      prisma.customer.aggregate({
        _count: true
      }),
      // Single query for loan counts and amounts by status
      prisma.loan.groupBy({
        by: ['status'],
        _count: true,
        _sum: { principalAmount: true }
      }),
      // Single query for payment counts and amounts
      prisma.payment.aggregate({
        _count: true,
        _sum: { amount: true }
      }),
      // Overdue payments count
      prisma.paymentSchedule.count({
        where: {
          status: 'OVERDUE',
          dueDate: { lt: new Date() }
        }
      })
    ])

    // Process aggregated results
    const totalUsers = userStats.reduce((sum, stat) => sum + stat._count, 0)
    const totalCustomers = customerStats._count
    const totalLoans = loanStats.reduce((sum, stat) => sum + stat._count, 0)
    const activeLoans = loanStats.find(stat => stat.status === 'DISBURSED')?._count || 0
    const totalPayments = paymentStats._count
    const totalLoanAmount = loanStats
      .filter(stat => ['ACTIVE', 'DISBURSED'].includes(stat.status))
      .reduce((sum, stat) => sum + (stat._sum.principalAmount || 0), 0)
    const totalPaymentAmount = paymentStats._sum.amount || 0
    const pendingApplications = loanStats.find(stat => stat.status === 'PENDING_APPROVAL')?._count || 0

    // Calculate additional metrics based on role
    let roleSpecificStats = {}

    switch (role) {
      case 'SUPER_ADMIN':
        roleSpecificStats = {
          totalUsers,
          totalCustomers,
          totalLoans,
          totalLoanAmount: totalLoanAmount || 0,
          activeLoans,
          overdueLoans: overduePayments,
          totalPayments,
          totalPaymentAmount: totalPaymentAmount || 0,
          systemHealth: overduePayments > 20 ? 'critical' : overduePayments > 10 ? 'warning' : 'good'
        }
        break

      case 'HIGHER_MANAGEMENT':
        const monthlyStats = await getMonthlyStats()

        // Calculate profitability based on actual data
        const profitability = await calculateProfitability()

        // Get additional metrics for higher management
        const totalCustomers = await prisma.customer.count()
        const totalCreditOfficers = await prisma.user.count({
          where: { role: 'CREDIT_OFFICER', isActive: true }
        })

        // Calculate average loan size
        const avgLoanSize = totalLoans > 0 ? (totalLoanAmount || 0) / totalLoans : 0

        roleSpecificStats = {
          portfolioValue: totalLoanAmount || 0,
          monthlyDisbursement: monthlyStats.disbursement,
          monthlyCollection: monthlyStats.collection,
          collectionRate: monthlyStats.collectionRate,
          activeLoans,
          overdueLoans: overduePayments,
          nplRatio: totalLoans > 0 ? (overduePayments / totalLoans) * 100 : 0,
          profitability: profitability,
          customerGrowth: await getCustomerGrowthRate(),
          totalCustomers,
          totalCreditOfficers,
          averageLoanSize: avgLoanSize,
          totalLoans
        }
        break

      case 'MANAGER':
        const teamStats = await getTeamStats(session.user.id)

        // Get manager monthly target from system configuration
        const managerTargetConfig = await prisma.systemConfig.findUnique({
          where: { key: 'manager_monthly_target' }
        })
        const managerMonthlyTarget = managerTargetConfig ? parseInt(managerTargetConfig.value) : 200000

        roleSpecificStats = {
          teamSize: teamStats.teamSize,
          teamLoans: teamStats.teamLoans,
          teamCollections: teamStats.teamCollections,
          pendingApprovals: pendingApplications,
          monthlyTarget: managerMonthlyTarget,
          monthlyAchievement: teamStats.teamCollections,
          teamPerformance: teamStats.teamPerformance,
          recentActivities: await getRecentActivities(session.user.id)
        }
        break

      case 'CREDIT_OFFICER':
        const officerStats = await getCreditOfficerStats(session.user.id)

        // Get monthly target from Credit Officer targets or use default
        const currentMonth = new Date().getMonth() + 1
        const currentYear = new Date().getFullYear()

        const officerTarget = await prisma.creditOfficerTarget.findUnique({
          where: {
            creditOfficerId_month_year: {
              creditOfficerId: session.user.id,
              month: currentMonth,
              year: currentYear
            }
          }
        })

        const monthlyTarget = officerTarget ? Number(officerTarget.loanTarget) : 500000 // Default to LKR 500,000 if no target set

        roleSpecificStats = {
          assignedCustomers: officerStats.assignedCustomers,
          activeLoans: officerStats.activeLoans,
          pendingApplications: officerStats.pendingApplications,
          pendingDisburse: officerStats.pendingDisburse,
          rejectedLoans: officerStats.rejectedLoans,
          monthlyTarget: monthlyTarget,
          monthlyAchievement: officerStats.monthlyAchievement,
          recentApplications: officerStats.recentApplications
        }
        break

      case 'CUSTOMER_SERVICE_OFFICER':
        const serviceStats = await getCustomerServiceStats(session.user.id)
        roleSpecificStats = {
          dailyPayments: serviceStats.dailyPayments,
          dailyPaymentAmount: serviceStats.dailyPaymentAmount,
          customerInquiries: serviceStats.customerInquiries,
          resolvedIssues: serviceStats.resolvedIssues,
          pendingFollowUps: serviceStats.pendingFollowUps,
          responseTime: serviceStats.responseTime,
          recentPayments: serviceStats.recentPayments,
          recentInquiries: serviceStats.recentInquiries,
          todaysTasks: serviceStats.todaysTasks
        }
        break

      default:
        roleSpecificStats = {
          totalCustomers,
          totalLoans,
          activeLoans,
          totalPayments
        }
    }

    // No caching - return fresh data immediately

    return NextResponse.json(roleSpecificStats)

  } catch (error) {
    console.error('Dashboard stats error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch dashboard statistics' },
      { status: 500 }
    )
  }
}

// Helper functions
async function getMonthlyStats() {
  const startOfMonth = new Date()
  startOfMonth.setDate(1)
  startOfMonth.setHours(0, 0, 0, 0)

  const [disbursement, collection] = await Promise.all([
    prisma.loan.aggregate({
      _sum: { principalAmount: true },
      where: {
        disbursementDate: { gte: startOfMonth },
        status: 'DISBURSED'
      }
    }),
    prisma.payment.aggregate({
      _sum: { amount: true },
      where: {
        createdAt: { gte: startOfMonth }
      }
    })
  ])

  const disbursementAmount = disbursement._sum.principalAmount || 0
  const collectionAmount = collection._sum.amount || 0
  const collectionRate = disbursementAmount > 0 ? (collectionAmount / disbursementAmount) * 100 : 0

  return {
    disbursement: disbursementAmount,
    collection: collectionAmount,
    collectionRate
  }
}

async function getCustomerGrowthRate() {
  const lastMonth = new Date()
  lastMonth.setMonth(lastMonth.getMonth() - 1)
  
  const [currentCount, lastMonthCount] = await Promise.all([
    prisma.customer.count(),
    prisma.customer.count({
      where: { createdAt: { lt: lastMonth } }
    })
  ])

  return lastMonthCount > 0 ? ((currentCount - lastMonthCount) / lastMonthCount) * 100 : 0
}



async function getTeamStats(managerId: string) {
  try {
    // Get all team members (Credit Officers and Customer Service Officers)
    const teamMembers = await prisma.user.findMany({
      where: {
        role: {
          in: ['CREDIT_OFFICER', 'CUSTOMER_SERVICE_OFFICER']
        },
        isActive: true
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        role: true,
        assignedCustomers: {
          select: {
            loans: {
              where: {
                status: {
                  in: ['ACTIVE', 'DISBURSED']
                }
              },
              select: {
                id: true,
                principalAmount: true,
                payments: {
                  select: {
                    amount: true,
                    createdAt: true
                  }
                }
              }
            }
          }
        },
        // For Customer Service Officers, get payment records they created
        paymentsCreated: {
          select: {
            amount: true,
            createdAt: true
          }
        }
      }
    })

    // Calculate team performance metrics
    const teamPerformance = teamMembers.map(member => {
      let loans = 0
      let collections = 0

      if (member.role === 'CREDIT_OFFICER') {
        // For Credit Officers, count loans from assigned customers
        loans = member.assignedCustomers.reduce((sum, customer) => sum + customer.loans.length, 0)
        collections = member.assignedCustomers.reduce((sum, customer) => {
          return sum + customer.loans.reduce((loanSum, loan) => {
            return loanSum + loan.payments.reduce((paymentSum, payment) => paymentSum + Number(payment.amount), 0)
          }, 0)
        }, 0)
      } else {
        // For Customer Service Officers, count payments they created
        collections = member.paymentsCreated.reduce((sum, payment) => sum + Number(payment.amount), 0)
      }

      // Determine performance level based on collections
      let performance = 'needs_improvement'
      if (collections > 100000) performance = 'excellent'
      else if (collections > 50000) performance = 'good'

      return {
        name: `${member.firstName} ${member.lastName}`,
        role: member.role === 'CREDIT_OFFICER' ? 'Credit Officer' : 'Customer Service',
        loans,
        collections,
        performance
      }
    })

    // Calculate total team metrics
    const teamLoans = teamPerformance.reduce((sum, member) => sum + member.loans, 0)
    const teamCollections = teamPerformance.reduce((sum, member) => sum + member.collections, 0)

    return {
      teamSize: teamMembers.length,
      teamLoans,
      teamCollections,
      teamPerformance
    }
  } catch (error) {
    console.error('Error fetching team stats:', error)
    // Return fallback data on error
    return {
      teamSize: 0,
      teamLoans: 0,
      teamCollections: 0,
      teamPerformance: []
    }
  }
}

async function getRecentActivities(userId: string) {
  // This would query actual audit logs
  return [
    { type: 'approval', description: 'Approved loan application #L2025001', time: '2 hours ago' },
    { type: 'meeting', description: 'Team meeting scheduled for tomorrow', time: '4 hours ago' },
    { type: 'target', description: 'Monthly target updated', time: '1 day ago' }
  ]
}

async function getCreditOfficerStats(officerId: string) {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  const startOfMonth = new Date()
  startOfMonth.setDate(1)
  startOfMonth.setHours(0, 0, 0, 0)

  const [
    assignedCustomersCount,
    activeLoansCount,
    pendingApplicationsCount,
    pendingDisburseCount,
    rejectedLoansCount,
    monthlyAchievementCount,
    recentApplications,
    overduePaymentsCount
  ] = await Promise.all([
    // Count customers assigned to this credit officer
    prisma.customer.count({
      where: {
        assignedTo: officerId
      }
    }),

    // Count active loans for customers assigned to this credit officer
    prisma.loan.count({
      where: {
        customer: {
          assignedTo: officerId
        },
        status: {
          in: ['ACTIVE', 'DISBURSED']
        }
      }
    }),

    // Count pending applications for customers assigned to this credit officer
    prisma.loan.count({
      where: {
        customer: {
          assignedTo: officerId
        },
        status: 'PENDING_APPROVAL'
      }
    }),

    // Count pending disburse loans for customers assigned to this credit officer
    prisma.loan.count({
      where: {
        customer: {
          assignedTo: officerId
        },
        status: 'APPROVED'
      }
    }),

    // Count rejected loans for customers assigned to this credit officer
    prisma.loan.count({
      where: {
        customer: {
          assignedTo: officerId
        },
        status: 'REJECTED'
      }
    }),

    // Sum loan amounts disbursed this month for customers assigned to this credit officer
    prisma.loan.aggregate({
      _sum: { principalAmount: true },
      where: {
        customer: {
          assignedTo: officerId
        },
        status: { in: ['ACTIVE', 'DISBURSED'] },
        disbursementDate: {
          gte: startOfMonth
        }
      }
    }),

    // Get recent loan applications for customers assigned to this credit officer
    prisma.loan.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: { customer: true },
      where: {
        customer: {
          assignedTo: officerId
        }
      }
    }),

    // Count overdue payments for customers assigned to this officer
    prisma.paymentSchedule.count({
      where: {
        status: 'OVERDUE',
        dueDate: { lt: new Date() },
        loan: {
          customer: {
            assignedTo: officerId
          }
        }
      }
    })
  ])

  // Generate today's tasks based on real data
  const todaysTasks = []

  if (overduePaymentsCount > 0) {
    todaysTasks.push({
      type: 'follow_up',
      description: `Follow up on ${overduePaymentsCount} overdue payments`,
      priority: 'high'
    })
  }

  if (pendingApplicationsCount > 0) {
    todaysTasks.push({
      type: 'documentation',
      description: `Complete assessment for ${pendingApplicationsCount} pending applications`,
      priority: 'medium'
    })
  }

  // Add field visit task if there are active customers
  if (assignedCustomersCount > 0) {
    todaysTasks.push({
      type: 'visit',
      description: 'Scheduled field visits to customers',
      priority: 'medium',
      time: '10:00 AM'
    })
  }

  return {
    assignedCustomers: assignedCustomersCount,
    activeLoans: activeLoansCount,
    pendingApplications: pendingApplicationsCount,
    pendingDisburse: pendingDisburseCount,
    rejectedLoans: rejectedLoansCount,
    monthlyAchievement: Number(monthlyAchievementCount._sum.principalAmount || 0),
    recentApplications: recentApplications.map(loan => ({
      id: loan.loanNumber,
      customerName: `${loan.customer.firstName} ${loan.customer.lastName}`,
      amount: loan.principalAmount,
      status: loan.status.toLowerCase().replace('_', ' '),
      submittedDate: loan.createdAt.toISOString().split('T')[0]
    }))
  }
}

async function getCustomerServiceStats(serviceOfficerId: string) {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  const [
    dailyPayments,
    recentPayments,
    overduePayments,
    recentInquiries
  ] = await Promise.all([
    prisma.payment.aggregate({
      _count: true,
      _sum: { amount: true },
      where: { createdAt: { gte: today } }
    }),
    prisma.payment.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: { loan: { include: { customer: true } } }
    }),
    // Count overdue payments for follow-up tasks
    prisma.paymentSchedule.count({
      where: {
        status: 'OVERDUE',
        dueDate: { lt: new Date() }
      }
    }),
    // Get recent notifications or inquiries (using audit logs as proxy)
    prisma.auditLog.findMany({
      take: 3,
      orderBy: { timestamp: 'desc' },
      where: {
        action: { in: ['PAYMENT_INQUIRY', 'CUSTOMER_COMPLAINT', 'BALANCE_INQUIRY'] }
      },
      include: { user: true }
    })
  ])

  // Calculate average response time based on recent activities
  const responseTime = 2.5 // This would be calculated from actual response data

  // Generate today's tasks based on real data
  const todaysTasks = []

  if (overduePayments > 0) {
    todaysTasks.push({
      type: 'follow_up',
      description: `Follow up on ${overduePayments} overdue payments`,
      priority: 'high',
      time: '3:00 PM'
    })
  }

  return {
    dailyPayments: dailyPayments._count,
    dailyPaymentAmount: dailyPayments._sum.amount || 0,
    customerInquiries: recentInquiries.length,
    resolvedIssues: Math.max(0, recentInquiries.length - 2), // Estimate resolved issues
    pendingFollowUps: overduePayments,
    responseTime: responseTime,
    recentPayments: recentPayments.map(payment => ({
      id: payment.id,
      customerName: `${payment.loan.customer.firstName} ${payment.loan.customer.lastName}`,
      amount: payment.amount,
      method: payment.method,
      time: payment.createdAt.toLocaleTimeString()
    })),
    recentInquiries: recentInquiries.map((inquiry, index) => ({
      id: inquiry.id,
      customerName: inquiry.user?.firstName + ' ' + inquiry.user?.lastName || 'Unknown',
      type: inquiry.action.toLowerCase().replace('_', ' '),
      status: index === 0 ? 'open' : 'resolved',
      priority: index === 0 ? 'high' : 'medium',
      time: new Date(inquiry.timestamp).toLocaleString()
    })),
    todaysTasks
  }
}

// Helper function to calculate profitability
async function calculateProfitability() {
  const startOfMonth = new Date()
  startOfMonth.setDate(1)
  startOfMonth.setHours(0, 0, 0, 0)

  const [totalInterest, totalExpenses] = await Promise.all([
    // Calculate total interest collected this month
    prisma.payment.aggregate({
      _sum: { interestAmount: true },
      where: { createdAt: { gte: startOfMonth } }
    }),
    // This would be calculated from actual expense data
    // For now, we'll estimate based on loan processing costs
    prisma.loan.aggregate({
      _sum: { processingFee: true },
      where: { createdAt: { gte: startOfMonth } }
    })
  ])

  const interest = totalInterest._sum.interestAmount || 0
  const expenses = totalExpenses._sum.processingFee || 0

  // Simple profitability calculation (interest - expenses) / interest * 100
  return interest > 0 ? ((interest - expenses) / interest) * 100 : 0
}
