import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { hasPermission } from '@/lib/auth'
import { getFileFromR2 } from '@/lib/r2-client'

// GET /api/documents/[id]/download - Download document
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !await hasPermission(session.user.role, 'documents:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Find the document
    const document = await prisma.document.findUnique({
      where: { id },
      include: {
        loan: {
          select: {
            id: true,
            customerId: true,
          }
        },
        customer: {
          select: {
            id: true,
          }
        }
      }
    })

    if (!document) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 })
    }

    // Check if user has access to this document
    // Users can only access documents for loans/customers they have permission to view
    const hasLoanAccess = document.loan && await hasPermission(session.user.role, 'loans:read')
    const hasCustomerAccess = document.customer && await hasPermission(session.user.role, 'customers:read')
    
    if (!hasLoanAccess && !hasCustomerAccess) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    try {
      // Get file from R2 storage
      const fileBuffer = await getFileFromR2(document.storageKey)
      
      // Return file as response
      return new NextResponse(fileBuffer, {
        headers: {
          'Content-Type': document.mimeType,
          'Content-Disposition': `attachment; filename="${document.originalName}"`,
          'Content-Length': document.fileSize.toString(),
        },
      })
    } catch (storageError) {
      console.error('Error retrieving file from storage:', storageError)
      return NextResponse.json({ error: 'File not found in storage' }, { status: 404 })
    }

  } catch (error) {
    console.error('Error downloading document:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
