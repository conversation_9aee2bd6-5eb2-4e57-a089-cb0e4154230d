import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { hasPermission } from '@/lib/auth'
import { uploadFileToR2, generateFileKey } from '@/lib/r2-client'
import { z } from 'zod'

// Validation schema for document upload
const uploadDocumentSchema = z.object({
  documentName: z.string().min(1, 'Document name is required'),
  loanId: z.string().optional(),
  customerId: z.string().optional(),
  documentType: z.enum([
    'NATIONAL_ID',
    'PASSPORT',
    'DRIVING_LICENSE',
    'UTILITY_BILL',
    'BANK_STATEMENT',
    'INCOME_CERTIFICATE',
    'PROPERTY_DEED',
    'BUSINESS_REGISTRATION',
    'LOAN_APPLICATION',
    'GUARANTOR_ID',
    'OTHER'
  ]),
})

// POST /api/documents - Upload document
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermission(session.user.role, 'loans:create')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const documentName = formData.get('documentName') as string
    const loanId = formData.get('loanId') as string
    const customerId = formData.get('customerId') as string
    const documentType = formData.get('documentType') as string

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // Validate form data
    const validatedData = uploadDocumentSchema.parse({
      documentName,
      loanId: loanId || undefined,
      customerId: customerId || undefined,
      documentType,
    })

    // Check file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json({ error: 'File size must be less than 10MB' }, { status: 400 })
    }

    // Check file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ 
        error: 'Only JPEG, PNG, and PDF files are allowed' 
      }, { status: 400 })
    }

    // Verify loan or customer exists
    if (validatedData.loanId) {
      const loan = await prisma.loan.findUnique({
        where: { id: validatedData.loanId }
      })
      if (!loan) {
        return NextResponse.json({ error: 'Loan not found' }, { status: 404 })
      }
    }

    if (validatedData.customerId) {
      const customer = await prisma.customer.findUnique({
        where: { id: validatedData.customerId }
      })
      if (!customer) {
        return NextResponse.json({ error: 'Customer not found' }, { status: 404 })
      }
    }

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer())

    // Generate unique file key
    const entityId = validatedData.loanId || validatedData.customerId!
    const entityType = validatedData.loanId ? 'loan' : 'customer'
    const storageKey = generateFileKey(entityType, entityId, validatedData.documentName, file.name)

    // Upload to R2
    await uploadFileToR2(buffer, storageKey, file.type)

    // Save document record to database
    const document = await prisma.document.create({
      data: {
        fileName: file.name,
        originalName: file.name,
        fileSize: file.size,
        mimeType: file.type,
        documentType: validatedData.documentType,
        storageKey,
        loanId: validatedData.loanId,
        customerId: validatedData.customerId,
        uploadedBy: session.user.id,
      }
    })

    return NextResponse.json({
      id: document.id,
      fileName: document.fileName,
      documentName: validatedData.documentName,
      fileSize: document.fileSize,
      uploadedAt: document.uploadedAt,
    }, { status: 201 })

  } catch (error) {
    console.error('Error uploading document:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Validation error',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/documents - List documents
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermission(session.user.role, 'loans:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const loanId = searchParams.get('loanId')
    const customerId = searchParams.get('customerId')

    const where: any = {}
    if (loanId) where.loanId = loanId
    if (customerId) where.customerId = customerId

    const documents = await prisma.document.findMany({
      where,
      orderBy: { uploadedAt: 'desc' },
      include: {
        loan: {
          select: {
            loanId: true,
            customer: {
              select: {
                firstName: true,
                lastName: true,
              }
            }
          }
        },
        customer: {
          select: {
            firstName: true,
            lastName: true,
          }
        }
      }
    })

    return NextResponse.json({ documents })
  } catch (error) {
    console.error('Error fetching documents:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
