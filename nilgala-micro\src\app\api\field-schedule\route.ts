import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, hasPermissionSync } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { UserRole } from '@prisma/client'

// GET /api/field-schedule - Get field schedule for Credit Officer
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to read payments or is a Credit Officer
    const canReadPayments = hasPermissionSync(session.user.role as UserRole, 'payments:read')
    
    if (!canReadPayments) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const searchParams = request.nextUrl.searchParams
    const date = searchParams.get('date') // Optional specific date
    const days = parseInt(searchParams.get('days') || '7') // Number of days to look ahead
    const creditOfficerId = searchParams.get('creditOfficerId') || session.user.id

    // If requesting another officer's schedule, check permissions
    if (creditOfficerId !== session.user.id && 
        !['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const startDate = date ? new Date(date) : new Date()
    startDate.setHours(0, 0, 0, 0)
    
    const endDate = new Date(startDate)
    endDate.setDate(startDate.getDate() + days)
    endDate.setHours(23, 59, 59, 999)

    // Get payment schedules for loans created by this Credit Officer
    const schedules = await prisma.paymentSchedule.findMany({
      where: {
        dueDate: {
          gte: startDate,
          lte: endDate
        },
        loan: {
          createdBy: creditOfficerId
        },
        status: {
          in: ['PENDING', 'OVERDUE', 'PARTIAL']
        }
      },
      include: {
        loan: {
          include: {
            customer: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                phone: true,
                address: true,
                city: true,
                gpsCoordinates: true
              }
            }
          }
        }
      },
      orderBy: [
        { dueDate: 'asc' },
        { loan: { customer: { firstName: 'asc' } } }
      ]
    })

    // Group schedules by date and customer location for efficient routing
    const schedulesByDate = schedules.reduce((acc, schedule) => {
      const dateKey = schedule.dueDate.toISOString().split('T')[0]
      
      if (!acc[dateKey]) {
        acc[dateKey] = []
      }
      
      acc[dateKey].push({
        id: schedule.id,
        installmentNumber: schedule.installmentNumber,
        dueDate: schedule.dueDate,
        totalAmount: schedule.totalAmount,
        paidAmount: schedule.paidAmount,
        remainingAmount: Number(schedule.totalAmount) - Number(schedule.paidAmount),
        status: schedule.status,
        daysOverdue: schedule.daysOverdue,
        penaltyAmount: schedule.penaltyAmount,
        loan: {
          id: schedule.loan.id,
          loanNumber: schedule.loan.loanNumber,
          principalAmount: schedule.loan.principalAmount,
          repaymentFrequency: schedule.loan.repaymentFrequency
        },
        customer: {
          id: schedule.loan.customer.id,
          name: `${schedule.loan.customer.firstName} ${schedule.loan.customer.lastName}`,
          phone: schedule.loan.customer.phone,
          address: schedule.loan.customer.address,
          city: schedule.loan.customer.city,
          gpsCoordinates: schedule.loan.customer.gpsCoordinates,
          location: `${schedule.loan.customer.address}, ${schedule.loan.customer.city}`
        },
        priority: schedule.status === 'OVERDUE' ? 'high' : 
                 schedule.daysOverdue > 0 ? 'medium' : 'normal',
        visitType: schedule.status === 'OVERDUE' ? 'collection' : 'regular_payment'
      })
      
      return acc
    }, {} as Record<string, any[]>)

    // Calculate summary statistics
    const totalSchedules = schedules.length
    const overdueSchedules = schedules.filter(s => s.status === 'OVERDUE').length
    const totalAmount = schedules.reduce((sum, s) => sum + Number(s.totalAmount), 0)
    const totalOverdueAmount = schedules
      .filter(s => s.status === 'OVERDUE')
      .reduce((sum, s) => sum + Number(s.totalAmount) - Number(s.paidAmount), 0)

    // Get unique customers for route planning
    const uniqueCustomers = Array.from(
      new Set(schedules.map(s => s.loan.customer.id))
    ).map(customerId => {
      const schedule = schedules.find(s => s.loan.customer.id === customerId)
      return schedule?.loan.customer
    }).filter(Boolean)

    return NextResponse.json({
      schedulesByDate,
      summary: {
        totalSchedules,
        overdueSchedules,
        totalAmount,
        totalOverdueAmount,
        uniqueCustomers: uniqueCustomers.length,
        dateRange: {
          start: startDate.toISOString().split('T')[0],
          end: endDate.toISOString().split('T')[0]
        }
      },
      customers: uniqueCustomers,
      routeSuggestions: generateRouteSuggestions(uniqueCustomers)
    })

  } catch (error) {
    console.error('Error fetching field schedule:', error)
    return NextResponse.json(
      { error: 'Failed to fetch field schedule' },
      { status: 500 }
    )
  }
}

// Helper function to generate route suggestions based on customer locations
function generateRouteSuggestions(customers: any[]) {
  // Group customers by city for efficient routing
  const customersByCity = customers.reduce((acc, customer) => {
    const city = customer.city || 'Unknown'
    if (!acc[city]) {
      acc[city] = []
    }
    acc[city].push(customer)
    return acc
  }, {} as Record<string, any[]>)

  // Generate route suggestions
  const routes = Object.entries(customersByCity).map(([city, cityCustomers]) => ({
    city,
    customerCount: cityCustomers.length,
    customers: cityCustomers.map(c => ({
      id: c.id,
      name: `${c.firstName} ${c.lastName}`,
      address: c.address,
      phone: c.phone,
      gpsCoordinates: c.gpsCoordinates
    })),
    estimatedTime: cityCustomers.length * 30, // 30 minutes per customer
    priority: cityCustomers.length > 3 ? 'high' : 'normal'
  }))

  return routes.sort((a, b) => b.customerCount - a.customerCount)
}
