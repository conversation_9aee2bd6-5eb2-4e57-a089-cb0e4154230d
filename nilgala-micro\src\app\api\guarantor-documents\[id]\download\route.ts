import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, hasPermissionSync } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { getFileFromR2 } from '@/lib/r2-client'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermissionSync(session.user.role, 'documents:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Find the guarantor document
    const document = await prisma.guarantorDocument.findUnique({
      where: { id },
      include: {
        guarantor: {
          include: {
            loanGuarantors: {
              include: {
                loan: {
                  include: {
                    customer: {
                      select: {
                        assignedTo: true
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!document) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 })
    }

    // Check if user has permission to download this document
    // Credit officers can only download documents for their assigned customers
    if (session.user.role === 'CREDIT_OFFICER') {
      const hasAccess = document.guarantor.loanGuarantors.some(lg => 
        lg.loan.customer.assignedTo === session.user.id
      )
      
      if (!hasAccess) {
        return NextResponse.json({ error: 'Access denied' }, { status: 403 })
      }
    }

    try {
      // Read file from R2 storage
      const fileBuffer = await getFileFromR2(document.fileKey)
      
      // Determine content type based on file extension
      const extension = document.fileName.split('.').pop()?.toLowerCase()
      let contentType = 'application/octet-stream'
      
      switch (extension) {
        case 'pdf':
          contentType = 'application/pdf'
          break
        case 'jpg':
        case 'jpeg':
          contentType = 'image/jpeg'
          break
        case 'png':
          contentType = 'image/png'
          break
        case 'gif':
          contentType = 'image/gif'
          break
        case 'doc':
          contentType = 'application/msword'
          break
        case 'docx':
          contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          break
      }

      return new NextResponse(fileBuffer, {
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': `attachment; filename="${document.fileName}"`,
          'Cache-Control': 'private, max-age=3600'
        }
      })
    } catch (fileError) {
      console.error('Error reading guarantor document file:', fileError)
      console.error('File key that failed:', document.fileKey)

      // Check if this is a "NoSuchKey" error from R2
      if (fileError.message && fileError.message.includes('NoSuchKey')) {
        // Return an HTML error page for download attempts too
        const errorHtml = `
          <!DOCTYPE html>
          <html lang="en">
          <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Download Failed</title>
            <style>
              body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
              .error-container { text-align: center; background: #f8f9fa; padding: 40px; border-radius: 8px; border: 1px solid #dee2e6; }
              .error-icon { font-size: 48px; color: #dc3545; margin-bottom: 20px; }
              h1 { color: #dc3545; margin-bottom: 20px; }
              p { color: #6c757d; line-height: 1.6; margin-bottom: 15px; }
              .details { background: #fff; padding: 15px; border-radius: 4px; margin: 20px 0; text-align: left; }
              .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin-top: 20px; }
              .btn:hover { background: #0056b3; }
            </style>
          </head>
          <body>
            <div class="error-container">
              <div class="error-icon">📥❌</div>
              <h1>Download Failed</h1>
              <p>The document file could not be downloaded because it's not found in storage.</p>

              <div class="details">
                <strong>Document Details:</strong><br>
                Name: ${document.documentName}<br>
                File: ${document.fileName}<br>
                Guarantor: ${document.guarantor.firstName} ${document.guarantor.lastName}
              </div>

              <p>Please contact the system administrator or request a re-upload of this document.</p>

              <a href="javascript:history.back()" class="btn">Go Back</a>
            </div>
          </body>
          </html>
        `

        return new NextResponse(errorHtml, {
          status: 404,
          headers: {
            'Content-Type': 'text/html',
          }
        })
      }

      return NextResponse.json(
        { error: 'Document file not found or corrupted', details: fileError.message },
        { status: 404 }
      )
    }

  } catch (error) {
    console.error('Error downloading guarantor document:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
