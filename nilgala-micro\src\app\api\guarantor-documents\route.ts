import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { hasPermissionSync } from '@/lib/auth'
import { uploadFileToR2, generateGuarantorFileKey } from '@/lib/r2-client'
import { z } from 'zod'

// Validation schema for guarantor document upload
const uploadGuarantorDocumentSchema = z.object({
  documentName: z.string().min(1, 'Document name is required'),
  guarantorId: z.string().min(1, 'Guarantor ID is required'),
  documentType: z.enum([
    'NATIONAL_ID',
    'PASSPORT',
    'DRIVING_LICENSE',
    'UTILITY_BILL',
    'BANK_STATEMENT',
    'INCOME_CERTIFICATE',
    'PROPERTY_DEED',
    'BUSINESS_REGISTRATION',
    'LOAN_APPLICATION',
    'GUARANTOR_ID',
    'OTHER'
  ]).default('OTHER'),
})

// POST /api/guarantor-documents - Upload guarantor document
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermissionSync(session.user.role, 'documents:create')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const documentName = formData.get('documentName') as string
    const guarantorId = formData.get('guarantorId') as string
    const documentType = formData.get('documentType') as string || 'OTHER'

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // Validate form data
    const validatedData = uploadGuarantorDocumentSchema.parse({
      documentName,
      guarantorId,
      documentType
    })

    // Check if guarantor exists
    const guarantor = await prisma.guarantor.findUnique({
      where: { id: validatedData.guarantorId }
    })

    if (!guarantor) {
      return NextResponse.json({ error: 'Guarantor not found' }, { status: 404 })
    }

    // Validate file
    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      return NextResponse.json({ error: 'File size must be less than 10MB' }, { status: 400 })
    }

    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ error: 'Only JPEG, PNG, and PDF files are allowed' }, { status: 400 })
    }

    // Generate file key and upload to R2
    const fileKey = generateGuarantorFileKey(file.name, validatedData.guarantorId)
    const fileBuffer = Buffer.from(await file.arrayBuffer())

    await uploadFileToR2(fileBuffer, fileKey, file.type)

    // Save document record to database
    const document = await prisma.guarantorDocument.create({
      data: {
        guarantorId: validatedData.guarantorId,
        documentName: validatedData.documentName,
        documentType: validatedData.documentType,
        fileName: file.name,
        fileSize: file.size,
        fileKey: fileKey,
      }
    })

    return NextResponse.json({
      id: document.id,
      documentName: document.documentName,
      fileName: document.fileName,
      fileSize: document.fileSize,
      uploadedAt: document.uploadedAt,
      documentType: document.documentType
    }, { status: 201 })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error uploading guarantor document:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/guarantor-documents - List guarantor documents
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermissionSync(session.user.role, 'documents:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const searchParams = request.nextUrl.searchParams
    const guarantorId = searchParams.get('guarantorId')

    if (!guarantorId) {
      return NextResponse.json({ error: 'Guarantor ID is required' }, { status: 400 })
    }

    const documents = await prisma.guarantorDocument.findMany({
      where: { guarantorId },
      orderBy: { uploadedAt: 'desc' }
    })

    return NextResponse.json({ documents })

  } catch (error) {
    console.error('Error fetching guarantor documents:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
