import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { hasPermissionSync } from '@/lib/auth'
import { z } from 'zod'

const createGuarantorSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  nationalId: z.string().min(1, 'National ID is required'),
  phone: z.string().min(1, 'Phone number is required'),
  email: z.string().email().optional().or(z.literal('')),
  address: z.string().min(1, 'Address is required'),
  city: z.string().min(1, 'City is required'),
  state: z.string().min(1, 'State is required'),
  dateOfBirth: z.string().optional(),
  occupation: z.string().optional(),
  monthlyIncome: z.number().optional(),
  relationship: z.string().optional(),
})

// GET /api/guarantors - List guarantors
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermissionSync(session.user.role, 'loans:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const searchParams = request.nextUrl.searchParams
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search') || ''

    const skip = (page - 1) * limit

    const where = search ? {
      OR: [
        { firstName: { contains: search, mode: 'insensitive' as const } },
        { lastName: { contains: search, mode: 'insensitive' as const } },
        { nationalId: { contains: search, mode: 'insensitive' as const } },
        { phone: { contains: search, mode: 'insensitive' as const } },
      ]
    } : {}

    const [guarantors, total] = await Promise.all([
      prisma.guarantor.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          _count: {
            select: {
              loanGuarantors: true,
              documents: true
            }
          }
        }
      }),
      prisma.guarantor.count({ where })
    ])

    return NextResponse.json({
      guarantors,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Error fetching guarantors:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/guarantors - Create new guarantor
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermissionSync(session.user.role, 'loans:create')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createGuarantorSchema.parse(body)

    // Check if guarantor with same national ID already exists
    const existingGuarantor = await prisma.guarantor.findUnique({
      where: { nationalId: validatedData.nationalId }
    })

    if (existingGuarantor) {
      return NextResponse.json(
        { error: 'Guarantor with this National ID already exists' },
        { status: 400 }
      )
    }

    // Create guarantor
    const guarantor = await prisma.guarantor.create({
      data: {
        ...validatedData,
        email: validatedData.email || null,
        dateOfBirth: validatedData.dateOfBirth ? new Date(validatedData.dateOfBirth) : null,
      },
      include: {
        _count: {
          select: {
            loanGuarantors: true,
            documents: true
          }
        }
      }
    })

    return NextResponse.json(guarantor, { status: 201 })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating guarantor:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
