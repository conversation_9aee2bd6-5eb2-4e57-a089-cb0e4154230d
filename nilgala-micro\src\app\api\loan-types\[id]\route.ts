import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { hasPermission } from '@/lib/auth'
import { z } from 'zod'

// Validation schema for loan type update
const updateLoanTypeSchema = z.object({
  name: z.string().min(1, 'Loan type name is required').optional(),
  description: z.string().optional(),
  minAmount: z.number().positive('Minimum amount must be positive').optional(),
  maxAmount: z.number().positive('Maximum amount must be positive').optional(),
  defaultInterestRate: z.number().min(0, 'Interest rate cannot be negative').max(100, 'Interest rate cannot exceed 100%').optional(),
  minInterestRate: z.number().min(0, 'Interest rate cannot be negative').max(100, 'Interest rate cannot exceed 100%').optional(),
  maxInterestRate: z.number().min(0, 'Interest rate cannot be negative').max(100, 'Interest rate cannot exceed 100%').optional(),
  defaultTenure: z.number().positive('Tenure must be positive').optional(),
  minTenure: z.number().positive('Tenure must be positive').optional(),
  maxTenure: z.number().positive('Tenure must be positive').optional(),
  tenureUnit: z.enum(['DAYS', 'WEEKS', 'MONTHS', 'YEARS']).optional(),
  collectionType: z.enum(['DAILY', 'WEEKLY', 'MONTHLY', 'QUARTERLY', 'YEARLY']).optional(),
  interestCalculationMethod: z.enum(['MONTHLY_INTEREST', 'COMPOUND_INTEREST']).optional(),
  processingFeeRate: z.number().min(0, 'Processing fee cannot be negative').optional(),
  insuranceFeeRate: z.number().min(0, 'Insurance fee cannot be negative').optional(),
  gracePeriod: z.number().min(0, 'Grace period cannot be negative').optional(),
  requiresGuarantor: z.boolean().optional(),
  maxGuarantors: z.number().min(0, 'Max guarantors cannot be negative').optional(),
  eligibilityCriteria: z.any().optional(),
  requiredDocuments: z.array(z.string()).optional(),
  isActive: z.boolean().optional(),
})

// GET /api/loan-types/[id] - Get loan type by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || !hasPermission(session.user.role, 'loan_types:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const loanType = await prisma.loanType.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            loans: true
          }
        }
      }
    })

    if (!loanType) {
      return NextResponse.json({ error: 'Loan type not found' }, { status: 404 })
    }

    return NextResponse.json(loanType)
  } catch (error) {
    console.error('Error fetching loan type:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/loan-types/[id] - Update loan type
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || !hasPermission(session.user.role, 'loan_types:update')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()
    const validatedData = updateLoanTypeSchema.parse(body)

    // Check if loan type exists
    const existingLoanType = await prisma.loanType.findUnique({
      where: { id }
    })

    if (!existingLoanType) {
      return NextResponse.json({ error: 'Loan type not found' }, { status: 404 })
    }

    // Validate amount ranges if provided
    if (validatedData.maxAmount && validatedData.minAmount) {
      if (validatedData.maxAmount <= validatedData.minAmount) {
        return NextResponse.json(
          { error: 'Maximum amount must be greater than minimum amount' },
          { status: 400 }
        )
      }
    }

    // Validate interest rate ranges if provided
    if (validatedData.maxInterestRate && validatedData.minInterestRate) {
      if (validatedData.maxInterestRate <= validatedData.minInterestRate) {
        return NextResponse.json(
          { error: 'Maximum interest rate must be greater than minimum interest rate' },
          { status: 400 }
        )
      }
    }

    // Validate tenure ranges if provided
    if (validatedData.maxTenure && validatedData.minTenure) {
      if (validatedData.maxTenure <= validatedData.minTenure) {
        return NextResponse.json(
          { error: 'Maximum tenure must be greater than minimum tenure' },
          { status: 400 }
        )
      }
    }

    // Check if name is unique (if being updated)
    if (validatedData.name && validatedData.name !== existingLoanType.name) {
      const existingWithName = await prisma.loanType.findFirst({
        where: { 
          name: validatedData.name,
          id: { not: params.id }
        }
      })

      if (existingWithName) {
        return NextResponse.json(
          { error: 'Loan type with this name already exists' },
          { status: 400 }
        )
      }
    }

    // Convert tenure values to days for storage if they are being updated
    const convertTenureToDays = (tenure: number, unit: string): number => {
      switch (unit) {
        case 'DAYS': return tenure
        case 'WEEKS': return tenure * 7
        case 'MONTHS': return tenure * 30
        case 'YEARS': return tenure * 365
        default: return tenure
      }
    }

    // Prepare update data with tenure conversion
    const updateData = { ...validatedData }
    const tenureUnit = validatedData.tenureUnit || existingLoanType.tenureUnit

    if (validatedData.defaultTenure !== undefined) {
      updateData.defaultTenure = convertTenureToDays(validatedData.defaultTenure, tenureUnit)
    }
    if (validatedData.minTenure !== undefined) {
      updateData.minTenure = convertTenureToDays(validatedData.minTenure, tenureUnit)
    }
    if (validatedData.maxTenure !== undefined) {
      updateData.maxTenure = convertTenureToDays(validatedData.maxTenure, tenureUnit)
    }

    const loanType = await prisma.loanType.update({
      where: { id },
      data: updateData,
      include: {
        _count: {
          select: {
            loans: true
          }
        }
      }
    })

    // Create audit log
    await prisma.auditLog.create({
      data: {
        action: 'UPDATE',
        resource: 'LoanType',
        resourceId: loanType.id,
        userId: session.user.id,
        oldValues: {
          name: existingLoanType.name,
          minAmount: existingLoanType.minAmount,
          maxAmount: existingLoanType.maxAmount,
        },
        newValues: {
          name: loanType.name,
          minAmount: loanType.minAmount,
          maxAmount: loanType.maxAmount,
        }
      }
    })

    return NextResponse.json(loanType)
  } catch (error) {
    if (error instanceof z.ZodError) {
      // Create user-friendly error message from Zod errors
      const errorMessages = error.errors.map(err => {
        const field = err.path.join('.')
        return `${field}: ${err.message}`
      }).join(', ')

      return NextResponse.json(
        { error: `Please fix the following errors: ${errorMessages}` },
        { status: 400 }
      )
    }

    console.error('Error updating loan type:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/loan-types/[id] - Delete loan type (soft delete)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermission(session.user.role, 'loan_types:delete')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if loan type exists
    const existingLoanType = await prisma.loanType.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            loans: true
          }
        }
      }
    })

    if (!existingLoanType) {
      return NextResponse.json({ error: 'Loan type not found' }, { status: 404 })
    }

    // Check if there are active loans using this loan type
    if (existingLoanType._count.loans > 0) {
      return NextResponse.json(
        { error: 'Cannot delete loan type with active loans. Deactivate instead.' },
        { status: 400 }
      )
    }

    // Soft delete by setting isActive to false
    const loanType = await prisma.loanType.update({
      where: { id: params.id },
      data: { isActive: false }
    })

    // Create audit log
    await prisma.auditLog.create({
      data: {
        action: 'DELETE',
        resource: 'LoanType',
        resourceId: loanType.id,
        userId: session.user.id,
        oldValues: {
          name: existingLoanType.name,
          isActive: existingLoanType.isActive
        },
        newValues: {
          name: loanType.name,
          isActive: loanType.isActive
        }
      }
    })

    return NextResponse.json({ message: 'Loan type deactivated successfully' })
  } catch (error) {
    console.error('Error deleting loan type:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
