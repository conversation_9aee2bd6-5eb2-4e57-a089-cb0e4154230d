import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { hasPermission } from '@/lib/auth'
import { z } from 'zod'

// Validation schema for loan type creation
const createLoanTypeSchema = z.object({
  name: z.string().min(1, 'Loan type name is required'),
  description: z.string().optional(),
  minAmount: z.number().positive('Minimum amount must be positive'),
  maxAmount: z.number().positive('Maximum amount must be positive'),
  defaultInterestRate: z.number().min(0, 'Default interest rate cannot be negative').max(100, 'Default interest rate cannot exceed 100%'),
  minInterestRate: z.number().min(0, 'Minimum interest rate cannot be negative').max(100, 'Minimum interest rate cannot exceed 100%'),
  maxInterestRate: z.number().min(0, 'Maximum interest rate cannot be negative').max(100, 'Maximum interest rate cannot exceed 100%'),
  defaultTenure: z.number().positive('Default tenure must be positive'),
  minTenure: z.number().positive('Minimum tenure must be positive'),
  maxTenure: z.number().positive('Maximum tenure must be positive'),
  tenureUnit: z.enum(['DAYS', 'WEEKS', 'MONTHS', 'YEARS']),
  collectionType: z.enum(['DAILY', 'WEEKLY', 'MONTHLY', 'QUARTERLY', 'YEARLY']),
  interestCalculationMethod: z.enum(['MONTHLY_INTEREST', 'COMPOUND_INTEREST']),
  processingFeeRate: z.number().min(0, 'Processing fee rate cannot be negative'),
  insuranceFeeRate: z.number().min(0, 'Insurance fee rate cannot be negative').optional(),
  gracePeriod: z.number().min(0, 'Grace period cannot be negative').optional(),
  requiresGuarantor: z.boolean(),
  maxGuarantors: z.number().min(0, 'Maximum guarantors cannot be negative').optional(),
  eligibilityCriteria: z.string().optional(),
  requiredDocuments: z.array(z.string()).optional(),
  isActive: z.boolean().optional(),
})

// GET /api/loan-types - List all loan types
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermission(session.user.role, 'loans:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const active = searchParams.get('active')

    const where: any = {}
    if (active === 'true') {
      where.isActive = true
    }

    const loanTypes = await prisma.loanType.findMany({
      where,
      orderBy: { name: 'asc' },
      include: {
        _count: {
          select: {
            loans: true
          }
        }
      }
    })

    return NextResponse.json(loanTypes)
  } catch (error) {
    console.error('Error fetching loan types:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/loan-types - Create new loan type
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermission(session.user.role, 'loan_types:create')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createLoanTypeSchema.parse(body)

    // Validate that max amount is greater than min amount
    if (validatedData.maxAmount <= validatedData.minAmount) {
      return NextResponse.json(
        { error: 'Maximum amount must be greater than minimum amount' },
        { status: 400 }
      )
    }

    // Validate interest rate ranges
    if (validatedData.maxInterestRate <= validatedData.minInterestRate) {
      return NextResponse.json(
        { error: 'Maximum interest rate must be greater than minimum interest rate' },
        { status: 400 }
      )
    }

    if (validatedData.defaultInterestRate < validatedData.minInterestRate ||
        validatedData.defaultInterestRate > validatedData.maxInterestRate) {
      return NextResponse.json(
        { error: 'Default interest rate must be between minimum and maximum interest rates' },
        { status: 400 }
      )
    }

    // Validate tenure ranges
    if (validatedData.maxTenure <= validatedData.minTenure) {
      return NextResponse.json(
        { error: 'Maximum tenure must be greater than minimum tenure' },
        { status: 400 }
      )
    }

    if (validatedData.defaultTenure < validatedData.minTenure ||
        validatedData.defaultTenure > validatedData.maxTenure) {
      return NextResponse.json(
        { error: 'Default tenure must be between minimum and maximum tenure' },
        { status: 400 }
      )
    }

    // Check if loan type with same name already exists
    const existingLoanType = await prisma.loanType.findFirst({
      where: { name: validatedData.name }
    })

    if (existingLoanType) {
      return NextResponse.json(
        { error: 'Loan type with this name already exists' },
        { status: 400 }
      )
    }

    console.log('Session user ID:', session.user.id)
    console.log('Session user email:', session.user.email)

    // Convert tenure values to days for storage
    const convertTenureToDays = (tenure: number, unit: string): number => {
      switch (unit) {
        case 'DAYS': return tenure
        case 'WEEKS': return tenure * 7
        case 'MONTHS': return tenure * 30
        case 'YEARS': return tenure * 365
        default: return tenure
      }
    }

    const loanType = await prisma.loanType.create({
      data: {
        ...validatedData,
        defaultTenure: convertTenureToDays(validatedData.defaultTenure, validatedData.tenureUnit),
        minTenure: convertTenureToDays(validatedData.minTenure, validatedData.tenureUnit),
        maxTenure: convertTenureToDays(validatedData.maxTenure, validatedData.tenureUnit),
        createdBy: session.user.id,
      },
      include: {
        _count: {
          select: {
            loans: true
          }
        }
      }
    })

    // Create audit log
    await prisma.auditLog.create({
      data: {
        action: 'CREATE',
        resource: 'LoanType',
        resourceId: loanType.id,
        userId: session.user.id,
        newValues: {
          name: loanType.name,
          minAmount: loanType.minAmount,
          maxAmount: loanType.maxAmount,
          defaultInterestRate: loanType.defaultInterestRate,
          collectionType: loanType.collectionType,
          tenureUnit: loanType.tenureUnit
        }
      }
    })

    return NextResponse.json(loanType, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      // Create user-friendly error message from Zod errors
      const errorMessages = error.errors.map(err => {
        const field = err.path.join('.')
        return `${field}: ${err.message}`
      }).join(', ')

      return NextResponse.json(
        { error: `Please fix the following errors: ${errorMessages}` },
        { status: 400 }
      )
    }

    console.error('Error creating loan type:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
