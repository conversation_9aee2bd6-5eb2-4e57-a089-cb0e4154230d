import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { hasPermission } from '@/lib/auth'

// GET /api/loans/[id]/activities - Get loan activity log
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || !hasPermission(session.user.role, 'loans:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Verify loan exists and user has access
    const loan = await prisma.loan.findUnique({
      where: { id }
    })

    if (!loan) {
      return NextResponse.json({ error: 'Loan not found' }, { status: 404 })
    }

    // Get all audit logs related to this loan
    const activities = await prisma.auditLog.findMany({
      where: {
        OR: [
          // Direct loan activities
          {
            resource: 'Loan',
            resourceId: id
          },
          // Payment activities for this loan
          {
            resource: 'Payment',
            resourceId: id
          },
          // Document activities for this loan
          {
            resource: 'Document',
            resourceId: id
          },
          // Guarantor activities for this loan
          {
            resource: 'Guarantor',
            resourceId: id
          },
          // Payment schedule activities for this loan
          {
            resource: 'PaymentSchedule',
            resourceId: id
          }
        ]
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            role: true,
            email: true
          }
        }
      },
      orderBy: {
        timestamp: 'desc'
      }
    })

    // Add some synthetic activities for better loan history
    const syntheticActivities = []

    // Add loan creation activity if not already in audit logs
    const hasCreationLog = activities.some(a =>
      a.action === 'CREATE' && a.resource === 'Loan'
    )

    if (!hasCreationLog) {
      syntheticActivities.push({
        id: `synthetic-create-${loan.id}`,
        action: 'CREATE',
        resource: 'Loan',
        resourceId: loan.id,
        details: `Loan application ${loan.loanNumber} created with principal amount ${loan.principalAmount}`,
        timestamp: loan.createdAt,
        user: {
          id: loan.createdBy || 'system',
          firstName: 'System',
          lastName: 'User',
          role: 'SYSTEM',
          email: '<EMAIL>'
        }
      })
    }

    // Add disbursement activity if loan is disbursed
    if (loan.status === 'DISBURSED' || loan.status === 'ACTIVE') {
      const hasDisbursementLog = activities.some(a =>
        a.newValues && JSON.stringify(a.newValues).toLowerCase().includes('disbursed')
      )

      if (!hasDisbursementLog && loan.disbursementDate) {
        syntheticActivities.push({
          id: `synthetic-disburse-${loan.id}`,
          action: 'UPDATE',
          resource: 'Loan',
          resourceId: loan.id,
          details: `Loan disbursed with amount ${loan.disbursedAmount || loan.principalAmount}`,
          timestamp: loan.disbursementDate,
          user: {
            id: 'system',
            firstName: 'System',
            lastName: 'User',
            role: 'SYSTEM',
            email: '<EMAIL>'
          }
        })
      }
    }

    // Combine real and synthetic activities
    const allActivities = [
      ...activities.map(activity => ({
        id: activity.id,
        action: activity.action,
        resource: activity.resource,
        resourceId: activity.resourceId,
        details: activity.newValues ? JSON.stringify(activity.newValues) : activity.action,
        timestamp: activity.timestamp.toISOString(),
        user: activity.user
      })),
      ...syntheticActivities
    ]

    // Sort by timestamp (newest first)
    allActivities.sort((a, b) =>
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    )

    return NextResponse.json(allActivities)

  } catch (error) {
    console.error('Error fetching loan activities:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
