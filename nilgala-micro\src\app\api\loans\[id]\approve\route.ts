import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, hasPermission } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { AuditLogger } from '@/lib/audit-logger'
import { z } from 'zod'

const approvalSchema = z.object({
  action: z.enum(['APPROVE', 'REJECT', 'REQUEST_MORE_INFO']),
  notes: z.string().optional(),
  requestedInfo: z.string().optional()
})

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !await hasPermission(session.user.role, 'loans:approve')) {
      return NextResponse.json({ error: 'Unauthorized - Loan approval permission required' }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()
    const validatedData = approvalSchema.parse(body)

    // Check if loan exists and is in pending approval status
    const loan = await prisma.loan.findUnique({
      where: { id },
      include: {
        customer: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
            phone: true
          }
        }
      }
    })

    if (!loan) {
      return NextResponse.json({ error: 'Loan not found' }, { status: 404 })
    }

    if (!['PENDING_APPROVAL', 'PENDING_MORE_INFO'].includes(loan.status)) {
      return NextResponse.json({
        error: `Loan cannot be ${validatedData.action.toLowerCase()}d. Current status: ${loan.status}`
      }, { status: 400 })
    }

    let newStatus: string
    let statusMessage: string

    switch (validatedData.action) {
      case 'APPROVE':
        newStatus = 'APPROVED'
        statusMessage = 'approved'
        break
      case 'REJECT':
        newStatus = 'REJECTED'
        statusMessage = 'rejected'
        break
      case 'REQUEST_MORE_INFO':
        newStatus = 'PENDING_MORE_INFO'
        statusMessage = 'marked for more information'
        break
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }

    // Update loan status and add approval record
    const updatedLoan = await prisma.$transaction(async (tx) => {
      // Update loan status
      const loan = await tx.loan.update({
        where: { id },
        data: {
          status: newStatus,
          ...(validatedData.action === 'APPROVE' && { approvedAt: new Date(), approvedBy: session.user.id }),
          ...(validatedData.action === 'REJECT' && { rejectedAt: new Date(), rejectedBy: session.user.id }),
          updatedAt: new Date()
        },
        include: {
          customer: {
            select: {
              firstName: true,
              lastName: true,
              phone: true,
              email: true
            }
          },
          loanType: {
            select: {
              name: true
            }
          }
        }
      })

      // Create approval history record
      await tx.loanApproval.create({
        data: {
          loanId: id,
          action: validatedData.action,
          notes: validatedData.notes || '',
          requestedInfo: validatedData.requestedInfo || null,
          approvedBy: session.user.id,
          createdAt: new Date()
        }
      })

      return loan
    })

    // Create audit log entry
    const auditContext = AuditLogger.getContextFromRequest(request, session.user.id)
    await AuditLogger.logLoanOperation(
      auditContext,
      validatedData.action,
      id,
      { status: loan.status },
      {
        status: newStatus,
        notes: validatedData.notes,
        requestedInfo: validatedData.requestedInfo,
        loanNumber: updatedLoan.loanNumber,
        customerName: `${updatedLoan.customer.firstName} ${updatedLoan.customer.lastName}`,
        loanType: updatedLoan.loanType.name
      }
    )

    // TODO: Send notification to customer (email/SMS)

    return NextResponse.json({
      message: `Loan ${statusMessage} successfully`,
      loan: updatedLoan,
      action: validatedData.action,
      approvedBy: {
        id: session.user.id,
        name: `${session.user.firstName} ${session.user.lastName}`,
        role: session.user.role
      }
    })

  } catch (error) {
    console.error('Loan approval error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to process loan approval' },
      { status: 500 }
    )
  }
}

// Get loan approval history
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !await hasPermission(session.user.role, 'loans:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    const approvalHistory = await prisma.loanApproval.findMany({
      where: { loanId: id },
      include: {
        approver: {
          select: {
            firstName: true,
            lastName: true,
            role: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json({ approvalHistory })

  } catch (error) {
    console.error('Error fetching approval history:', error)
    return NextResponse.json(
      { error: 'Failed to fetch approval history' },
      { status: 500 }
    )
  }
}
