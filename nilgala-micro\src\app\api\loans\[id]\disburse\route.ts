import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, hasPermission } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { updateCustomerStatus } from '@/lib/customer-status'
import { generatePaymentSchedule } from '@/lib/payment-schedule'
import { z } from 'zod'

const disbursementSchema = z.object({
  disbursedAmount: z.number().positive('Disbursed amount must be positive'),
  disbursementMethod: z.enum(['CASH', 'BANK_TRANSFER', 'CHEQUE', 'ONLINE', 'MOBILE_PAYMENT']),
  disbursementReference: z.string().optional(),
  disbursementNotes: z.string().optional(),
  disbursementDate: z.string().transform((str) => new Date(str)).optional(),
})

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !await hasPermission(session.user.role, 'loans:disburse')) {
      return NextResponse.json({ error: 'Unauthorized - Loan disbursement permission required' }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()
    const validatedData = disbursementSchema.parse(body)

    // Check if loan exists and is approved
    const loan = await prisma.loan.findUnique({
      where: { id },
      include: {
        customer: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
            phone: true
          }
        },
        loanType: {
          select: {
            interestCalculationMethod: true,
            tenureUnit: true,
            name: true
          }
        }
      }
    })

    if (!loan) {
      return NextResponse.json({ error: 'Loan not found' }, { status: 404 })
    }

    if (loan.status !== 'APPROVED') {
      return NextResponse.json({ 
        error: `Loan cannot be disbursed. Current status: ${loan.status}. Only approved loans can be disbursed.` 
      }, { status: 400 })
    }

    // Validate disbursed amount doesn't exceed principal amount
    if (validatedData.disbursedAmount > Number(loan.principalAmount)) {
      return NextResponse.json({ 
        error: `Disbursed amount (${validatedData.disbursedAmount}) cannot exceed principal amount (${loan.principalAmount})` 
      }, { status: 400 })
    }

    // Update loan with disbursement details
    const updatedLoan = await prisma.$transaction(async (tx) => {
      // Update loan status and disbursement details
      const loan = await tx.loan.update({
        where: { id },
        data: {
          status: 'ACTIVE',
          disbursedAmount: validatedData.disbursedAmount,
          disbursementMethod: validatedData.disbursementMethod,
          disbursementReference: validatedData.disbursementReference || null,
          disbursementNotes: validatedData.disbursementNotes || null,
          disbursementDate: validatedData.disbursementDate || new Date(),
          disbursedBy: session.user.id,
          disbursedAt: new Date(),
          updatedAt: new Date()
        },
        include: {
          customer: {
            select: {
              firstName: true,
              lastName: true,
              phone: true,
              email: true
            }
          },
          loanType: {
            select: {
              name: true
            }
          }
        }
      })

      // Delete any existing payment schedules first to avoid unique constraint violations
      await tx.paymentSchedule.deleteMany({
        where: { loanId: loan.id }
      })

      // Create payment schedule now that loan is disbursed
      const loanData = {
        id: loan.id,
        principalAmount: Number(loan.principalAmount),
        interestRate: Number(loan.interestRate),
        tenure: Number(loan.tenure),
        tenureUnit: loan.loanType.tenureUnit || 'MONTHS',
        repaymentFrequency: loan.repaymentFrequency,
        interestCalculationMethod: loan.loanType.interestCalculationMethod || 'MONTHLY_INTEREST',
        applicationDate: loan.applicationDate,
        disbursementDate: validatedData.disbursementDate || new Date()
      }

      console.log('Creating payment schedule for loan:', {
        loanId: loan.loanNumber,
        loanNumber: loan.loanNumber,
        loanData
      })

      const schedule = generatePaymentSchedule(loanData)

      console.log('Generated payment schedule:', {
        numberOfPayments: schedule.length,
        firstPayment: schedule[0],
        lastPayment: schedule[schedule.length - 1]
      })

      // Save the generated schedule to database
      console.log('Saving payment schedule data:', schedule.slice(0, 2)) // Log first 2 items for debugging

      await tx.paymentSchedule.createMany({
        data: schedule.map(item => ({
          loanId: loan.id,
          installmentNumber: item.installmentNumber,
          dueDate: item.dueDate,
          principalAmount: item.principalAmount,
          interestAmount: item.interestAmount,
          totalAmount: item.totalAmount,
          status: 'PENDING' as const
        }))
      })

      // Create audit log entry
      await tx.auditLog.create({
        data: {
          action: 'DISBURSE',
          resource: 'Loan',
          resourceId: id,
          userId: session.user.id,
          newValues: {
            status: 'ACTIVE',
            disbursedAmount: validatedData.disbursedAmount,
            disbursementMethod: validatedData.disbursementMethod,
            disbursementReference: validatedData.disbursementReference,
            disbursementDate: validatedData.disbursementDate || new Date()
          }
        }
      })

      return loan
    })

    // Update customer status to ACTIVE since they now have a disbursed loan
    updateCustomerStatus(loan.customerId, session.user.id).catch(error => {
      console.error('Error updating customer status after disbursement:', error)
    })

    // TODO: Send notification to customer (email/SMS)
    // TODO: Generate disbursement receipt/document

    return NextResponse.json({
      message: `Loan disbursed successfully`,
      loan: updatedLoan,
      disbursement: {
        amount: validatedData.disbursedAmount,
        method: validatedData.disbursementMethod,
        reference: validatedData.disbursementReference,
        date: validatedData.disbursementDate || new Date(),
        disbursedBy: {
          id: session.user.id,
          name: `${session.user.firstName} ${session.user.lastName}`,
          role: session.user.role
        }
      }
    })

  } catch (error) {
    console.error('Loan disbursement error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to disburse loan' },
      { status: 500 }
    )
  }
}

// GET /api/loans/[id]/disburse - Get disbursement details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !await hasPermission(session.user.role, 'loans:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    const loan = await prisma.loan.findUnique({
      where: { id },
      select: {
        id: true,
        loanNumber: true,
        status: true,
        principalAmount: true,
        disbursedAmount: true,
        disbursementMethod: true,
        disbursementReference: true,
        disbursementNotes: true,
        disbursementDate: true,
        disbursedAt: true,
        disburser: {
          select: {
            firstName: true,
            lastName: true,
            role: true
          }
        }
      }
    })

    if (!loan) {
      return NextResponse.json({ error: 'Loan not found' }, { status: 404 })
    }

    return NextResponse.json({ loan })

  } catch (error) {
    console.error('Error fetching disbursement details:', error)
    return NextResponse.json(
      { error: 'Failed to fetch disbursement details' },
      { status: 500 }
    )
  }
}
