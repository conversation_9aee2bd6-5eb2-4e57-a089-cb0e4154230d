import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { hasPermission } from '@/lib/auth'
import { generatePaymentSchedule } from '@/lib/payment-schedule'

// POST /api/loans/[id]/regenerate-schedule - Regenerate payment schedule for a loan
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermission(session.user.role, 'loans:update')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the loan with loan type information
    const loan = await prisma.loan.findUnique({
      where: { id: params.id },
      include: {
        loanType: true
      }
    })

    if (!loan) {
      return NextResponse.json({ error: 'Loan not found' }, { status: 404 })
    }

    // Only allow regeneration for disbursed/active loans
    if (!['DISBURSED', 'ACTIVE'].includes(loan.status)) {
      return NextResponse.json(
        { error: 'Can only regenerate schedule for disbursed or active loans' },
        { status: 400 }
      )
    }

    await prisma.$transaction(async (tx) => {
      // Delete existing payment schedules
      await tx.paymentSchedule.deleteMany({
        where: { loanId: params.id }
      })

      // Create loan data for schedule generation
      const loanData = {
        id: loan.id,
        principalAmount: Number(loan.principalAmount),
        interestRate: Number(loan.interestRate),
        tenure: Number(loan.tenure),
        tenureUnit: loan.loanType.tenureUnit || 'MONTHS',
        repaymentFrequency: loan.repaymentFrequency,
        interestCalculationMethod: loan.loanType.interestCalculationMethod || 'MONTHLY_INTEREST',
        applicationDate: loan.applicationDate,
        disbursementDate: loan.disbursementDate || new Date()
      }

      console.log('Regenerating schedule for loan:', {
        loanId: loan.id,
        loanNumber: loan.loanNumber,
        principalAmount: loanData.principalAmount,
        interestRate: loanData.interestRate,
        tenure: loanData.tenure,
        tenureUnit: loanData.tenureUnit,
        repaymentFrequency: loanData.repaymentFrequency,
        interestCalculationMethod: loanData.interestCalculationMethod
      })

      const schedule = generatePaymentSchedule(loanData)

      console.log('Generated schedule:', {
        numberOfPayments: schedule.length,
        firstPayment: schedule[0],
        lastPayment: schedule[schedule.length - 1]
      })

      // Save the new schedule to database
      await tx.paymentSchedule.createMany({
        data: schedule.map(item => ({
          loanId: loan.id,
          installmentNumber: item.installmentNumber,
          dueDate: item.dueDate,
          principalAmount: item.principalAmount,
          interestAmount: item.interestAmount,
          totalAmount: item.totalAmount,
          status: 'PENDING' as const
        }))
      })

      // Create audit log
      await tx.auditLog.create({
        data: {
          userId: session.user.id,
          action: 'UPDATE',
          entityType: 'Loan',
          entityId: params.id,
          details: `Regenerated payment schedule - ${schedule.length} installments created`,
          ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown',
        }
      })
    })

    return NextResponse.json({
      message: 'Payment schedule regenerated successfully',
      loanId: params.id
    })

  } catch (error) {
    console.error('Error regenerating payment schedule:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
