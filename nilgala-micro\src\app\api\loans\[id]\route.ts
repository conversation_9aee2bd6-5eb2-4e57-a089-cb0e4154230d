import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { hasPermission } from '@/lib/auth'
import { generatePaymentSchedule } from '@/lib/payment-schedule'
import { z } from 'zod'

// Validation schema for loan update (approval/disbursement)
const updateLoanSchema = z.object({
  status: z.enum(['PENDING', 'APPROVED', 'REJECTED', 'DISBURSED', 'ACTIVE', 'COMPLETED', 'DEFAULTED', 'WRITTEN_OFF']).optional(),
  approvedAmount: z.number().positive().optional(),
  disbursedAmount: z.number().positive().optional(),
  disbursementDate: z.string().transform((str) => new Date(str)).optional(),
  notes: z.string().optional(),
  rejectionReason: z.string().optional(),
})

// Validation schema for loan editing (when status is PENDING_MORE_INFO)
const editLoanSchema = z.object({
  loanTypeId: z.string().min(1).optional(),
  principalAmount: z.number().positive().optional(),
  interestRate: z.number().min(0).optional(),
  tenure: z.number().positive().optional(),
  repaymentFrequency: z.enum(['DAILY', 'WEEKLY', 'MONTHLY', 'QUARTERLY', 'YEARLY']).optional(),
  gracePeriod: z.number().min(0).optional(),
  processingFee: z.number().min(0).optional(),
  insuranceFee: z.number().min(0).optional(),
  otherCharges: z.number().min(0).optional(),
  purpose: z.string().min(1).optional(),
  collateralDescription: z.string().optional(),
  notes: z.string().optional(),
})

// GET /api/loans/[id] - Get loan by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || !hasPermission(session.user.role, 'loans:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const loan = await prisma.loan.findUnique({
      where: { id },
      include: {
        customer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            phone: true,
            email: true,
            nationalId: true,
            address: true,
            monthlyIncome: true,
            employmentType: true,
            employer: true,
          }
        },
        loanType: true,
        guarantors: {
          select: {
            id: true,
            guarantorType: true,
            liabilityAmount: true,
            guarantorStatus: true,
            customer: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                phone: true,
              }
            },
            guarantor: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                phone: true,
                relationship: true,
                documents: {
                  select: {
                    id: true,
                    documentName: true,
                    documentType: true,
                    fileName: true,
                    fileSize: true,
                    uploadedAt: true
                  },
                  orderBy: { uploadedAt: 'desc' }
                }
              }
            }
          }
        },
        payments: {
          orderBy: { paymentDate: 'desc' },
          take: 10, // Latest 10 payments
          include: {
            createdByUser: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                role: true,
              }
            }
          }
        },
        schedules: {
          orderBy: { dueDate: 'asc' }
        },
        documents: {
          orderBy: { createdAt: 'desc' }
        },
        _count: {
          select: {
            payments: true,
            schedules: true,
            documents: true,
          }
        }
      }
    })

    if (!loan) {
      return NextResponse.json({ error: 'Loan not found' }, { status: 404 })
    }

    // Calculate loan summary
    const totalPaid = await prisma.payment.aggregate({
      where: { loanId: loan.id },
      _sum: { amount: true }
    })

    const outstandingAmount = Number(loan.totalAmount) - (totalPaid._sum.amount || 0)

    const loanWithSummary = {
      ...loan,
      summary: {
        totalPaid: totalPaid._sum.amount || 0,
        outstandingAmount,
        paymentProgress: Number(loan.totalAmount) > 0 ?
          ((totalPaid._sum.amount || 0) / Number(loan.totalAmount)) * 100 : 0
      }
    }

    return NextResponse.json(loanWithSummary)
  } catch (error) {
    console.error('Error fetching loan:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/loans/[id] - Update loan (approval, disbursement, etc.)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermission(session.user.role, 'loans:update')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { id } = await params

    // Check if loan exists first
    const existingLoan = await prisma.loan.findUnique({
      where: { id },
      include: { loanType: true }
    })

    if (!existingLoan) {
      return NextResponse.json({ error: 'Loan not found' }, { status: 404 })
    }

    // Determine if this is a loan edit or status update
    const isLoanEdit = existingLoan.status === 'PENDING_MORE_INFO' &&
                      (body.loanTypeId || body.principalAmount || body.interestRate || body.tenure ||
                       body.repaymentFrequency || body.gracePeriod || body.processingFee ||
                       body.insuranceFee || body.otherCharges || body.purpose || body.collateralDescription)

    let validatedData: any

    if (isLoanEdit) {
      // Validate as loan edit
      validatedData = editLoanSchema.parse(body)
    } else {
      // Validate as status update
      validatedData = updateLoanSchema.parse(body)
    }

    // Handle loan editing (only when status is PENDING_MORE_INFO)
    if (isLoanEdit) {
      if (existingLoan.status !== 'PENDING_MORE_INFO') {
        return NextResponse.json(
          { error: 'Loan can only be edited when status is PENDING_MORE_INFO' },
          { status: 400 }
        )
      }

      // Import interest calculation functions
      const { calculateLoanInterest, convertTenureToDays } = await import('@/lib/interest-calculations')

      // Recalculate loan amounts if financial details changed
      let recalculatedData = {}
      if (validatedData.principalAmount || validatedData.interestRate || validatedData.tenure) {
        const principalAmount = validatedData.principalAmount || existingLoan.principalAmount
        const interestRate = validatedData.interestRate || existingLoan.interestRate
        const tenure = validatedData.tenure || existingLoan.tenure
        const repaymentFrequency = validatedData.repaymentFrequency || existingLoan.repaymentFrequency

        // Convert tenure to days
        const tenureInDays = convertTenureToDays(Number(tenure), existingLoan.loanType.tenureUnit || 'MONTHS')

        // Calculate new amounts
        const calculation = calculateLoanInterest({
          principalAmount: Number(principalAmount),
          interestRate: Number(interestRate),
          tenureInDays: tenureInDays,
          collectionType: repaymentFrequency as any,
          interestCalculationMethod: existingLoan.loanType.interestCalculationMethod || 'MONTHLY_INTEREST'
        })

        recalculatedData = {
          totalAmount: calculation.totalAmount,
          emiAmount: calculation.emiAmount,
          totalInterest: calculation.totalInterest
        }
      }

      // Update the loan with edited data
      const updatedLoan = await prisma.loan.update({
        where: { id },
        data: {
          ...validatedData,
          ...recalculatedData,
          status: 'PENDING_APPROVAL', // Reset to pending approval after edit
          updatedAt: new Date(),
        },
        include: {
          customer: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              phone: true,
            }
          },
          loanType: {
            select: {
              id: true,
              name: true,
            }
          }
        }
      })

      // Create audit log for loan edit
      await prisma.auditLog.create({
        data: {
          userId: session.user.id,
          action: 'UPDATE',
          entityType: 'Loan',
          entityId: id,
          details: `Edited loan details and reset status to PENDING_APPROVAL`,
          ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown',
        }
      })

      return NextResponse.json(updatedLoan)
    }

    // Business logic validation for status updates
    if (validatedData.status) {
      // Validate status transitions
      const validTransitions: Record<string, string[]> = {
        'PENDING': ['APPROVED', 'REJECTED'],
        'APPROVED': ['DISBURSED', 'REJECTED'],
        'DISBURSED': ['ACTIVE'],
        'ACTIVE': ['COMPLETED', 'DEFAULTED'],
        'DEFAULTED': ['WRITTEN_OFF', 'ACTIVE'], // Can reactivate if payment made
      }

      const allowedNextStatuses = validTransitions[existingLoan.status] || []
      if (!allowedNextStatuses.includes(validatedData.status)) {
        return NextResponse.json(
          { error: `Cannot change status from ${existingLoan.status} to ${validatedData.status}` },
          { status: 400 }
        )
      }
    }

    // Validate approved amount
    if (validatedData.approvedAmount) {
      if (validatedData.approvedAmount > existingLoan.requestedAmount) {
        return NextResponse.json(
          { error: 'Approved amount cannot exceed requested amount' },
          { status: 400 }
        )
      }
    }

    const loan = await prisma.$transaction(async (tx) => {
      // Update the loan
      const updatedLoan = await tx.loan.update({
        where: { id },
        data: {
          ...validatedData,
          updatedAt: new Date(),
        }
      })

      // If loan is being disbursed, create payment schedule using unified system
      if (validatedData.status === 'DISBURSED' && validatedData.disbursedAmount) {
        // Create loan data for schedule generation
        const loanData = {
          id: updatedLoan.id,
          principalAmount: Number(validatedData.disbursedAmount),
          interestRate: Number(updatedLoan.interestRate),
          tenure: Number(updatedLoan.tenure),
          tenureUnit: updatedLoan.loanType.tenureUnit || 'MONTHS',
          repaymentFrequency: updatedLoan.repaymentFrequency,
          interestCalculationMethod: updatedLoan.loanType.interestCalculationMethod || 'MONTHLY_INTEREST',
          applicationDate: updatedLoan.applicationDate,
          disbursementDate: validatedData.disbursementDate || new Date()
        }

        const schedule = generatePaymentSchedule(loanData)

        // Save the generated schedule to database
        await tx.paymentSchedule.createMany({
          data: schedule.map(item => ({
            loanId: updatedLoan.id,
            installmentNumber: item.installmentNumber,
            dueDate: item.dueDate,
            principalAmount: item.principalAmount,
            interestAmount: item.interestAmount,
            totalAmount: item.totalAmount,
            status: 'PENDING' as const
          }))
        })
      }

      return updatedLoan
    })

    // Create audit log
    const actionDetails = validatedData.status ? 
      `Updated loan status to ${validatedData.status}` :
      'Updated loan details'

    await prisma.auditLog.create({
      data: {
        action: 'UPDATE',
        resource: 'Loan',
        resourceId: loan.id,
        userId: session.user.id,
        newValues: {
          loanId: loan.loanId,
          status: loan.status,
          amount: loan.amount
        }
      }
    })

    // Fetch updated loan with relations
    const completeLoan = await prisma.loan.findUnique({
      where: { id: loan.id },
      include: {
        customer: {
          select: {
            id: true,
            customerId: true,
            firstName: true,
            lastName: true,
            phone: true,
          }
        },
        loanType: true,
        guarantors: {
          include: {
            guarantor: {
              select: {
                id: true,
                customerId: true,
                firstName: true,
                lastName: true,
              }
            }
          }
        }
      }
    })

    return NextResponse.json(completeLoan)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating loan:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Note: Payment schedule creation now uses the unified system from @/lib/payment-schedule
