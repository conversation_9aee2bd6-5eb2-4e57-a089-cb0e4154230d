import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, hasPermission } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { generatePaymentSchedule } from '@/lib/payment-schedule'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || !await hasPermission(session.user.role, 'loans:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    
    // Get loan details
    const loan = await prisma.loan.findUnique({
      where: { id },
      include: {
        loanType: {
          select: {
            interestCalculationMethod: true
          }
        }
      }
    })

    if (!loan) {
      return NextResponse.json({ error: 'Loan not found' }, { status: 404 })
    }

    // Check if schedules already exist
    const existingSchedules = await prisma.paymentSchedule.findMany({
      where: { loanId: id },
      orderBy: { dueDate: 'asc' }
    })

    if (existingSchedules.length > 0) {
      return NextResponse.json(existingSchedules)
    }

    // Only generate schedules for disbursed loans
    if (loan.status !== 'DISBURSED' && loan.status !== 'ACTIVE') {
      return NextResponse.json(
        { error: 'Payment schedule can only be generated after loan disbursement' },
        { status: 400 }
      )
    }

    // Generate new schedule using the unified function
    const loanData = {
      id: loan.id,
      principalAmount: Number(loan.principalAmount),
      interestRate: Number(loan.interestRate),
      tenure: Number(loan.tenure),
      tenureUnit: loan.loanType.tenureUnit || 'MONTHS', // Use the loan type's tenure unit
      repaymentFrequency: loan.repaymentFrequency,
      interestCalculationMethod: loan.loanType.interestCalculationMethod,
      applicationDate: loan.applicationDate,
      disbursementDate: loan.disbursementDate
    }

    const schedule = generatePaymentSchedule(loanData)

    return NextResponse.json(schedule)
  } catch (error) {
    console.error('Error fetching payment schedule:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || !await hasPermission(session.user.role, 'loans:update')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    
    // Get loan details
    const loan = await prisma.loan.findUnique({
      where: { id },
      include: {
        loanType: {
          select: {
            interestCalculationMethod: true
          }
        }
      }
    })

    if (!loan) {
      return NextResponse.json({ error: 'Loan not found' }, { status: 404 })
    }

    // Delete existing schedules
    await prisma.paymentSchedule.deleteMany({
      where: { loanId: id }
    })

    // Generate new schedule using the unified function
    const loanData = {
      id: loan.id,
      principalAmount: Number(loan.principalAmount),
      interestRate: Number(loan.interestRate),
      tenure: Number(loan.tenure),
      tenureUnit: loan.loanType.tenureUnit || 'MONTHS', // Use the loan type's tenure unit
      repaymentFrequency: loan.repaymentFrequency,
      interestCalculationMethod: loan.loanType.interestCalculationMethod,
      applicationDate: loan.applicationDate,
      disbursementDate: loan.disbursementDate
    }

    const schedule = generatePaymentSchedule(loanData)
    
    // Save to database
    const savedSchedules = await prisma.paymentSchedule.createMany({
      data: schedule.map(item => ({
        loanId: id,
        installmentNumber: item.installmentNumber,
        dueDate: item.dueDate,
        principalAmount: item.principalAmount,
        interestAmount: item.interestAmount,
        totalAmount: item.totalAmount,
        status: 'PENDING' as const
      }))
    })

    // Fetch the created schedules
    const createdSchedules = await prisma.paymentSchedule.findMany({
      where: { loanId: id },
      orderBy: { dueDate: 'asc' }
    })

    return NextResponse.json(createdSchedules)
  } catch (error) {
    console.error('Error creating payment schedule:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
