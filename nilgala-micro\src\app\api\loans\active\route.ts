import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { hasPermission } from '@/lib/auth'

// GET /api/loans/active - Get active loans for payment recording
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !await hasPermission(session.user.role, 'loans:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''

    // Build where clause for active loans that can receive payments
    const where: any = {
      status: {
        in: ['ACTIVE', 'DISBURSED']
      }
    }
    
    if (search) {
      where.OR = [
        { loanNumber: { contains: search, mode: 'insensitive' } },
        { customer: {
          OR: [
            { firstName: { contains: search, mode: 'insensitive' } },
            { lastName: { contains: search, mode: 'insensitive' } },
            { nationalId: { contains: search, mode: 'insensitive' } },
            { phone: { contains: search, mode: 'insensitive' } }
          ]
        }}
      ]
    }

    const loans = await prisma.loan.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      include: {
        customer: {
          select: {
            id: true,
            nationalId: true,
            firstName: true,
            lastName: true,
            phone: true,
          }
        },
        loanType: {
          select: {
            id: true,
            name: true,
            defaultInterestRate: true,
          }
        },
        payments: {
          select: {
            amount: true
          }
        }
      }
    })

    // Calculate outstanding amounts for each loan
    const loansWithOutstanding = loans.map(loan => {
      const totalPaid = loan.payments.reduce((sum, payment) => sum + Number(payment.amount), 0)
      const outstandingAmount = Number(loan.totalAmount) - totalPaid

      return {
        id: loan.id,
        loanNumber: loan.loanNumber,
        principalAmount: Number(loan.principalAmount),
        totalAmount: Number(loan.totalAmount),
        disbursedAmount: Number(loan.disbursedAmount || loan.principalAmount),
        outstandingAmount: Math.max(0, outstandingAmount),
        emiAmount: Number(loan.emiAmount),
        status: loan.status,
        interestRate: Number(loan.interestRate),
        tenure: loan.tenure,
        repaymentFrequency: loan.repaymentFrequency,
        customer: loan.customer,
        loanType: {
          name: loan.loanType.name,
          interestRate: Number(loan.loanType.defaultInterestRate)
        },
        totalPaid
      }
    })

    return NextResponse.json({
      loans: loansWithOutstanding
    })
  } catch (error) {
    console.error('Error fetching active loans:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
