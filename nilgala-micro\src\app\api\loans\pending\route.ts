import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, hasPermission } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { calculateLoanInterest, convertTenureToDays } from '@/lib/interest-calculations'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !await hasPermission(session.user.role, 'loans:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    
    const skip = (page - 1) * limit

    // Build where clause for search
    let where: any = {
      status: { in: ['PENDING_APPROVAL', 'PENDING_MORE_INFO'] }
    }

    if (search) {
      where.OR = [
        { loanId: { contains: search, mode: 'insensitive' } },
        { customer: { 
          OR: [
            { firstName: { contains: search, mode: 'insensitive' } },
            { lastName: { contains: search, mode: 'insensitive' } },
            { nationalId: { contains: search, mode: 'insensitive' } },
            { phone: { contains: search, mode: 'insensitive' } }
          ]
        }},
        { loanType: { name: { contains: search, mode: 'insensitive' } } }
      ]
    }

    const [loans, total] = await Promise.all([
      prisma.loan.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          customer: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              phone: true,
              nationalId: true,
              monthlyIncome: true
            }
          },
          loanType: {
            select: {
              id: true,
              name: true,
              defaultInterestRate: true,
              maxAmount: true,
              minAmount: true,
              interestCalculationMethod: true,
              tenureUnit: true
            }
          },
          creator: {
            select: {
              firstName: true,
              lastName: true,
              role: true
            }
          },
          _count: {
            select: {
              documents: true,
              guarantors: true
            }
          }
        }
      }),
      prisma.loan.count({ where })
    ])

    // Calculate additional loan metrics using proper interest calculation method
    const loansWithMetrics = loans.map(loan => {
      // Convert tenure to days
      const tenureInDays = convertTenureToDays(
        Number(loan.tenure),
        loan.loanType.tenureUnit || 'MONTHS'
      )

      // Calculate EMI using the loan type's interest calculation method
      const calculation = calculateLoanInterest({
        principalAmount: Number(loan.principalAmount),
        interestRate: Number(loan.interestRate),
        tenureInDays: tenureInDays,
        collectionType: loan.repaymentFrequency as any,
        interestCalculationMethod: loan.loanType.interestCalculationMethod || 'MONTHLY_INTEREST'
      })

      const debtToIncomeRatio = loan.customer.monthlyIncome
        ? (calculation.emiAmount / Number(loan.customer.monthlyIncome)) * 100
        : 0

      return {
        ...loan,
        emiAmount: calculation.emiAmount,
        totalAmount: calculation.totalAmount,
        totalInterest: calculation.totalInterest,
        debtToIncomeRatio: Math.round(debtToIncomeRatio * 100) / 100,
        riskLevel: getRiskLevel(debtToIncomeRatio, Number(loan.principalAmount), loan.customer.monthlyIncome)
      }
    })

    return NextResponse.json({
      loans: loansWithMetrics,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Error fetching pending loans:', error)
    return NextResponse.json(
      { error: 'Failed to fetch pending loans' },
      { status: 500 }
    )
  }
}

// Note: EMI calculation now uses the unified interest calculation methods from @/lib/interest-calculations

// Helper function to determine risk level
function getRiskLevel(debtToIncomeRatio: number, loanAmount: number, monthlyIncome: number | null): string {
  if (!monthlyIncome) return 'HIGH'
  
  if (debtToIncomeRatio > 50) return 'HIGH'
  if (debtToIncomeRatio > 30) return 'MEDIUM'
  if (loanAmount > Number(monthlyIncome) * 24) return 'MEDIUM' // More than 2 years of income
  
  return 'LOW'
}
