import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { hasPermission } from '@/lib/auth'
import { updateCustomerStatus } from '@/lib/customer-status'
import { AuditLogger } from '@/lib/audit-logger'
import { generatePaymentSchedule } from '@/lib/payment-schedule'
import { uploadFileToR2, generateGuarantorFileKey } from '@/lib/r2-client'
import { z } from 'zod'

// Validation schema for loan creation
const createLoanSchema = z.object({
  customerId: z.string().min(1, 'Customer is required'),
  loanTypeId: z.string().min(1, 'Loan type is required'),
  principalAmount: z.number().positive('Principal amount must be positive'),
  interestRate: z.number().min(0, 'Interest rate cannot be negative'),
  tenure: z.number().positive('Tenure must be positive'),
  repaymentFrequency: z.enum(['DAILY', 'WEEKLY', 'MONTHLY', 'QUARTERLY', 'YEARLY']),
  gracePeriod: z.number().min(0, 'Grace period cannot be negative').optional(),
  processingFee: z.number().min(0, 'Processing fee cannot be negative').optional(),
  insuranceFee: z.number().min(0, 'Insurance fee cannot be negative').optional(),
  otherCharges: z.number().min(0, 'Other charges cannot be negative').optional(),
  purpose: z.string().min(1, 'Loan purpose is required'),
  collateralDescription: z.string().optional(),
  guarantors: z.array(z.object({
    type: z.enum(['existing', 'new']),
    customerId: z.string().optional(),
    guarantorData: z.object({
      firstName: z.string(),
      lastName: z.string(),
      nationalId: z.string(),
      phone: z.string(),
      email: z.string().optional(),
      address: z.string(),
      city: z.string(),
      state: z.string(),
      dateOfBirth: z.string().optional(),
      occupation: z.string().optional(),
      monthlyIncome: z.number().optional(),
      relationship: z.string().optional(),
      documents: z.array(z.object({
        documentName: z.string(),
        file: z.any().optional(),
        fileName: z.string().optional(),
        fileSize: z.number().optional(),
      })).optional()
    }).optional()
  })).optional(),
  documents: z.array(z.object({
    id: z.string().optional(), // Document ID if already uploaded
    documentName: z.string(),
    fileName: z.string().optional(),
    fileSize: z.number().optional(),
    requiredDocumentId: z.string().optional(),
  })).optional(),
  notes: z.string().optional(),
})

// GET /api/loans - List loans with pagination and filters
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermission(session.user.role, 'loans:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const statusParams = searchParams.getAll('status')
    const customerId = searchParams.get('customerId') || ''
    const assignmentFilter = searchParams.get('assignmentFilter') || ''

    const skip = (page - 1) * limit

    // Build where clause for search and filters
    const where: any = {}
    
    if (search) {
      where.OR = [
        { loanNumber: { contains: search } },
        { customer: {
          OR: [
            { firstName: { contains: search, mode: 'insensitive' } },
            { lastName: { contains: search, mode: 'insensitive' } },
            { nationalId: { contains: search } },
          ]
        }}
      ]
    }

    if (statusParams.length > 0) {
      if (statusParams.length === 1) {
        where.status = statusParams[0]
      } else {
        where.status = { in: statusParams }
      }
    }

    if (customerId) {
      where.customerId = customerId
    }

    // Assignment filter for credit officers
    if (assignmentFilter === 'ASSIGNED_TO_ME' && session.user.role === 'CREDIT_OFFICER') {
      where.customer = {
        ...where.customer,
        assignedTo: session.user.id
      }
    }

    const [loans, total] = await Promise.all([
      prisma.loan.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          customer: {
            select: {
              id: true,
              nationalId: true,
              firstName: true,
              lastName: true,
              phone: true,
            }
          },
          loanType: {
            select: {
              id: true,
              name: true,
              defaultInterestRate: true,
            }
          },
          payments: {
            select: {
              amount: true
            }
          },
          _count: {
            select: {
              payments: true,
              schedules: true,
            }
          }
        }
      }),
      prisma.loan.count({ where })
    ])

    // Calculate outstanding amounts for each loan
    const loansWithOutstanding = loans.map(loan => {
      const totalPaid = loan.payments.reduce((sum, payment) => sum + Number(payment.amount), 0)
      const outstandingAmount = Number(loan.totalAmount) - totalPaid

      return {
        ...loan,
        outstandingAmount: Math.max(0, outstandingAmount),
        totalPaid,
        payments: undefined // Remove payments array from response for cleaner data
      }
    })

    return NextResponse.json({
      loans: loansWithOutstanding,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching loans:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/loans - Create new loan application
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermission(session.user.role, 'loans:create')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createLoanSchema.parse(body)

    // Verify customer exists and get assigned officer
    const customer = await prisma.customer.findUnique({
      where: { id: validatedData.customerId },
      include: {
        assignedOfficer: {
          select: {
            id: true,
            role: true,
            isActive: true
          }
        }
      }
    })

    if (!customer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      )
    }

    // Determine who should create the loan
    let loanCreator = session.user.id

    // If customer has an assigned Credit Officer, use that officer as creator
    // unless the current user is a higher authority (Manager, Higher Management, Super Admin)
    if (customer.assignedOfficer &&
        customer.assignedOfficer.isActive &&
        customer.assignedOfficer.role === 'CREDIT_OFFICER' &&
        !['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER'].includes(session.user.role)) {
      loanCreator = customer.assignedOfficer.id
    }

    // If current user is a Credit Officer and customer is not assigned, assign customer to current user
    if (!customer.assignedTo && session.user.role === 'CREDIT_OFFICER') {
      await prisma.customer.update({
        where: { id: customer.id },
        data: { assignedTo: session.user.id }
      })
      loanCreator = session.user.id
    }

    // Verify loan type exists
    const loanType = await prisma.loanType.findUnique({
      where: { id: validatedData.loanTypeId }
    })

    if (!loanType) {
      return NextResponse.json(
        { error: 'Loan type not found' },
        { status: 404 }
      )
    }

    // Validate against loan type constraints
    const validationErrors: string[] = []

    // Principal amount validation
    if (validatedData.principalAmount < Number(loanType.minAmount)) {
      validationErrors.push(`Principal amount must be at least LKR ${Number(loanType.minAmount).toLocaleString()}`)
    }
    if (validatedData.principalAmount > Number(loanType.maxAmount)) {
      validationErrors.push(`Principal amount cannot exceed LKR ${Number(loanType.maxAmount).toLocaleString()}`)
    }

    // Interest rate validation
    if (validatedData.interestRate < Number(loanType.minInterestRate)) {
      validationErrors.push(`Interest rate must be at least ${loanType.minInterestRate}%`)
    }
    if (validatedData.interestRate > Number(loanType.maxInterestRate)) {
      validationErrors.push(`Interest rate cannot exceed ${loanType.maxInterestRate}%`)
    }

    // Tenure validation (convert to days for comparison)
    const convertTenureToDays = (value: number, unit: string): number => {
      switch (unit) {
        case 'DAYS': return value
        case 'WEEKS': return value * 7
        case 'MONTHS': return value * 30
        case 'YEARS': return value * 365
        default: return value
      }
    }

    const tenureInDays = convertTenureToDays(validatedData.tenure, loanType.tenureUnit)
    if (tenureInDays < loanType.minTenure) {
      const minDisplay = Math.round(loanType.minTenure / (loanType.tenureUnit === 'DAYS' ? 1 : loanType.tenureUnit === 'WEEKS' ? 7 : loanType.tenureUnit === 'MONTHS' ? 30 : 365))
      validationErrors.push(`Tenure must be at least ${minDisplay} ${loanType.tenureUnit.toLowerCase()}`)
    }
    if (tenureInDays > loanType.maxTenure) {
      const maxDisplay = Math.round(loanType.maxTenure / (loanType.tenureUnit === 'DAYS' ? 1 : loanType.tenureUnit === 'WEEKS' ? 7 : loanType.tenureUnit === 'MONTHS' ? 30 : 365))
      validationErrors.push(`Tenure cannot exceed ${maxDisplay} ${loanType.tenureUnit.toLowerCase()}`)
    }

    // Repayment frequency validation
    if (validatedData.repaymentFrequency !== loanType.collectionType) {
      validationErrors.push(`Repayment frequency must be ${loanType.collectionType.toLowerCase()} for this loan type`)
    }

    if (validationErrors.length > 0) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationErrors },
        { status: 400 }
      )
    }

    // Calculate total amount and EMI using the loan type's interest calculation method
    const { calculateLoanInterest } = await import('@/lib/interest-calculations')

    const calculation = calculateLoanInterest({
      principalAmount: validatedData.principalAmount,
      interestRate: validatedData.interestRate,
      tenureInDays: tenureInDays,
      collectionType: validatedData.repaymentFrequency,
      interestCalculationMethod: loanType.interestCalculationMethod || 'MONTHLY_INTEREST'
    })

    const totalInterest = calculation.totalInterest
    const baseTotalAmount = calculation.totalAmount
    const emiAmount = calculation.emiAmount
    const totalAmount = baseTotalAmount + (validatedData.processingFee || 0) + (validatedData.insuranceFee || 0) + (validatedData.otherCharges || 0)

    // Generate unique loan number
    const loanNumber = `LN${Date.now()}`

    // Create loan
    const loan = await prisma.loan.create({
      data: {
        loanNumber,
        customerId: validatedData.customerId,
        loanTypeId: validatedData.loanTypeId,
        principalAmount: validatedData.principalAmount,
        interestRate: validatedData.interestRate,
        tenure: validatedData.tenure,
        repaymentFrequency: validatedData.repaymentFrequency,
        gracePeriod: validatedData.gracePeriod || 0,
        processingFee: validatedData.processingFee || 0,
        insuranceFee: validatedData.insuranceFee || 0,
        otherCharges: validatedData.otherCharges || 0,
        totalAmount,
        emiAmount,
        totalInterest,
        status: 'PENDING_APPROVAL',
        applicationDate: new Date(),
        createdBy: loanCreator,
      },
      include: {
        customer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
          }
        },
      }
    })

    // Process guarantors if provided
    if (validatedData.guarantors && validatedData.guarantors.length > 0) {
      for (const guarantorData of validatedData.guarantors) {
        if (guarantorData.type === 'existing' && guarantorData.customerId) {
          // Create loan guarantor record for existing customer
          await prisma.loanGuarantor.create({
            data: {
              loanId: loan.id,
              customerId: guarantorData.customerId,
              guarantorType: 'CUSTOMER',
              liabilityAmount: validatedData.principalAmount, // Default to full loan amount
            }
          })
        } else if (guarantorData.type === 'new' && guarantorData.guarantorData) {
          // Create new guarantor first
          const newGuarantor = await prisma.guarantor.create({
            data: {
              firstName: guarantorData.guarantorData.firstName,
              lastName: guarantorData.guarantorData.lastName,
              nationalId: guarantorData.guarantorData.nationalId,
              phone: guarantorData.guarantorData.phone,
              email: guarantorData.guarantorData.email || null,
              address: guarantorData.guarantorData.address,
              city: guarantorData.guarantorData.city,
              state: guarantorData.guarantorData.state,
              dateOfBirth: guarantorData.guarantorData.dateOfBirth ? new Date(guarantorData.guarantorData.dateOfBirth) : null,
              occupation: guarantorData.guarantorData.occupation || null,
              monthlyIncome: guarantorData.guarantorData.monthlyIncome || null,
              relationship: guarantorData.guarantorData.relationship || null,
            }
          })

          // Create loan guarantor record
          await prisma.loanGuarantor.create({
            data: {
              loanId: loan.id,
              guarantorId: newGuarantor.id,
              guarantorType: 'INDIVIDUAL',
              liabilityAmount: validatedData.principalAmount,
            }
          })

          // Process guarantor documents if any
          if (guarantorData.guarantorData.documents && guarantorData.guarantorData.documents.length > 0) {
            for (const doc of guarantorData.guarantorData.documents) {
              if (doc.documentName && doc.file) {
                // Upload file to R2 if it's a File object
                let fileKey = doc.fileKey
                if (doc.file instanceof File) {
                  fileKey = generateGuarantorFileKey(doc.file.name, guarantor.id)
                  const fileBuffer = Buffer.from(await doc.file.arrayBuffer())
                  await uploadFileToR2(fileBuffer, fileKey, doc.file.type)
                }

                await prisma.guarantorDocument.create({
                  data: {
                    guarantorId: newGuarantor.id,
                    documentName: doc.documentName,
                    documentType: doc.documentType || 'OTHER',
                    fileName: doc.fileName || doc.file?.name || 'unknown',
                    fileSize: doc.fileSize || doc.file?.size || 0,
                    fileKey: fileKey || `guarantor-documents/${newGuarantor.id}/${doc.fileName || 'unknown'}`,
                  }
                })
              }
            }
          }
        }
      }
    }

    // Associate any documents uploaded for this customer that don't have a loan ID yet
    // This handles documents uploaded during the loan creation process
    await prisma.document.updateMany({
      where: {
        customerId: validatedData.customerId,
        loanId: null, // Documents uploaded but not yet associated with a loan
      },
      data: {
        loanId: loan.id,
      }
    })

    // Process any additional documents provided in the request
    if (validatedData.documents && validatedData.documents.length > 0) {
      for (const doc of validatedData.documents) {
        if (doc.id) {
          // Document was already uploaded, just associate it with the loan
          await prisma.document.update({
            where: { id: doc.id },
            data: {
              loanId: loan.id,
            }
          })
        } else if (doc.documentName && doc.fileName) {
          // Create new document record (fallback for any documents not pre-uploaded)
          await prisma.document.create({
            data: {
              loanId: loan.id,
              customerId: validatedData.customerId,
              documentName: doc.documentName,
              documentType: 'OTHER',
              fileName: doc.fileName,
              fileSize: doc.fileSize || 0,
              uploadedAt: new Date(),
            }
          })
        }
      }
    }

    // Generate payment schedule using the unified function
    const loanData = {
      id: loan.id,
      principalAmount: validatedData.principalAmount,
      interestRate: validatedData.interestRate,
      tenure: validatedData.tenure,
      tenureUnit: loanType.tenureUnit,
      repaymentFrequency: validatedData.repaymentFrequency,
      interestCalculationMethod: loanType.interestCalculationMethod,
      applicationDate: loan.applicationDate,
      disbursementDate: loan.disbursementDate
    }

    // Payment schedule will be created after loan disbursement
    // This ensures schedules start from disbursement date, not application date

    // Create enhanced audit log
    const auditContext = AuditLogger.getContextFromRequest(request, session.user.id)
    await AuditLogger.logCRUD(
      auditContext,
      'CREATE',
      'Loan',
      loan.id,
      undefined,
      {
        loanNumber: loan.loanNumber,
        customerId: validatedData.customerId,
        customerName: `${customer.firstName} ${customer.lastName}`,
        customerAssignedTo: customer.assignedTo,
        loanTypeId: validatedData.loanTypeId,
        principalAmount: validatedData.principalAmount,
        interestRate: validatedData.interestRate,
        tenure: validatedData.tenure,
        repaymentFrequency: validatedData.repaymentFrequency,
        totalAmount: loan.totalAmount,
        status: loan.status,
        guarantorsCount: validatedData.guarantors?.length || 0,
        createdByUser: session.user.id,
        createdByUserName: `${session.user.firstName} ${session.user.lastName}`,
        createdByUserRole: session.user.role,
        assignedToOfficer: loanCreator,
        assignedToOfficerName: customer.assignedOfficer ? `${customer.assignedOfficer.firstName} ${customer.assignedOfficer.lastName}` : 'N/A'
      }
    )

    // Fetch the complete loan data to return
    const completeLoan = await prisma.loan.findUnique({
      where: { id: loan.id },
      include: {
        customer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            phone: true,
          }
        },
        loanType: {
          select: {
            id: true,
            name: true,
            defaultInterestRate: true,
          }
        },
        guarantors: {
          include: {
            customer: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                phone: true,
              }
            },
            guarantor: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                phone: true,
                nationalId: true,
                relationship: true,
              }
            }
          }
        }
      }
    })

    // Update customer status based on loan activity (async, don't wait)
    updateCustomerStatus(validatedData.customerId, session.user.id).catch(error => {
      console.error('Error updating customer status:', error)
    })

    return NextResponse.json(completeLoan, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating loan:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}


