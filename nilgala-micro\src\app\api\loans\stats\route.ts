import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, hasPermission } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !await hasPermission(session.user.role, 'loans:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get loan statistics
    const [
      totalLoans,
      pendingApproval,
      approved,
      disbursed,
      active,
      completed,
      defaulted,
      rejected
    ] = await Promise.all([
      prisma.loan.count(),
      prisma.loan.count({ where: { status: 'PENDING_APPROVAL' } }),
      prisma.loan.count({ where: { status: 'APPROVED' } }),
      prisma.loan.count({ where: { status: 'DISBURSED' } }),
      prisma.loan.count({ where: { status: 'ACTIVE' } }),
      prisma.loan.count({ where: { status: 'COMPLETED' } }),
      prisma.loan.count({ where: { status: 'DEFAULTED' } }),
      prisma.loan.count({ where: { status: 'REJECTED' } })
    ])

    // Get financial statistics
    const [
      totalAmountResult,
      disbursedAmountResult,
      outstandingAmountResult
    ] = await Promise.all([
      prisma.loan.aggregate({
        _sum: { principalAmount: true },
        where: { status: { not: 'REJECTED' } }
      }),
      prisma.loan.aggregate({
        _sum: { principalAmount: true },
        where: { status: { in: ['DISBURSED', 'ACTIVE', 'COMPLETED'] } }
      }),
      prisma.payment.aggregate({
        _sum: { amount: true }
      })
    ])

    const totalAmount = Number(totalAmountResult._sum.principalAmount || 0)
    const disbursedAmount = Number(disbursedAmountResult._sum.principalAmount || 0)
    const totalPaid = Number(outstandingAmountResult._sum.amount || 0)
    const outstandingAmount = disbursedAmount - totalPaid

    const stats = {
      counts: {
        total: totalLoans,
        pending: pendingApproval,
        approved,
        disbursed,
        active,
        completed,
        defaulted,
        rejected
      },
      amounts: {
        total: totalAmount,
        disbursed: disbursedAmount,
        outstanding: outstandingAmount,
        collected: totalPaid
      },
      ratios: {
        approvalRate: totalLoans > 0 ? ((totalLoans - rejected) / totalLoans) * 100 : 0,
        disbursementRate: approved > 0 ? (disbursed / approved) * 100 : 0,
        collectionRate: disbursedAmount > 0 ? (totalPaid / disbursedAmount) * 100 : 0
      }
    }

    return NextResponse.json(stats)

  } catch (error) {
    console.error('Error fetching loan statistics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch loan statistics' },
      { status: 500 }
    )
  }
}
