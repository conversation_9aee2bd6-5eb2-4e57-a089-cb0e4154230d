import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, hasPermissionSync } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/payment-schedules - List payment schedules with filtering
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermissionSync(session.user.role, 'payments:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const loanId = searchParams.get('loanId') || ''
    const status = searchParams.get('status') || ''
    const overdue = searchParams.get('overdue') === 'true'
    const upcoming = searchParams.get('upcoming') === 'true'

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}

    if (loanId) {
      where.loanId = loanId
    }

    if (status && status !== 'ALL') {
      where.status = status
    }

    if (overdue) {
      where.AND = [
        { status: { in: ['PENDING', 'PARTIAL'] } },
        { dueDate: { lt: new Date() } }
      ]
    }

    if (upcoming) {
      const today = new Date()
      const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)
      where.AND = [
        { status: { in: ['PENDING', 'PARTIAL'] } },
        { dueDate: { gte: today, lte: nextWeek } }
      ]
    }

    const [schedules, total] = await Promise.all([
      prisma.paymentSchedule.findMany({
        where,
        include: {
          loan: {
            include: {
              customer: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  phone: true,
                  nationalId: true,
                }
              },
              loanType: {
                select: {
                  name: true
                }
              }
            }
          }
        },
        orderBy: { dueDate: 'asc' },
        skip,
        take: limit,
      }),
      prisma.paymentSchedule.count({ where })
    ])

    // Calculate overdue days for each schedule
    const schedulesWithOverdue = schedules.map(schedule => {
      const today = new Date()
      const dueDate = new Date(schedule.dueDate)
      const overdueDays = ['PENDING', 'PARTIAL'].includes(schedule.status) && dueDate < today
        ? Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24))
        : 0

      return {
        ...schedule,
        overdueDays,
        isOverdue: overdueDays > 0
      }
    })

    const pages = Math.ceil(total / limit)

    return NextResponse.json({
      schedules: schedulesWithOverdue,
      pagination: {
        page,
        limit,
        total,
        pages
      }
    })
  } catch (error) {
    console.error('Error fetching payment schedules:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/payment-schedules/summary - Get payment schedule summary
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermissionSync(session.user.role, 'payments:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { action } = body

    if (action === 'summary') {
      const today = new Date()
      const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)
      const nextMonth = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000)

      const [
        totalPending,
        overdueCount,
        overdueAmount,
        upcomingWeek,
        upcomingMonth,
        totalCollected
      ] = await Promise.all([
        // Total pending schedules
        prisma.paymentSchedule.count({
          where: { status: { in: ['PENDING', 'PARTIAL'] } }
        }),
        
        // Overdue count
        prisma.paymentSchedule.count({
          where: {
            status: { in: ['PENDING', 'PARTIAL'] },
            dueDate: { lt: today }
          }
        }),
        
        // Overdue amount
        prisma.paymentSchedule.aggregate({
          where: {
            status: { in: ['PENDING', 'PARTIAL'] },
            dueDate: { lt: today }
          },
          _sum: {
            totalAmount: true
          }
        }),
        
        // Upcoming week
        prisma.paymentSchedule.aggregate({
          where: {
            status: { in: ['PENDING', 'PARTIAL'] },
            dueDate: { gte: today, lte: nextWeek }
          },
          _sum: {
            totalAmount: true
          },
          _count: true
        }),
        
        // Upcoming month
        prisma.paymentSchedule.aggregate({
          where: {
            status: { in: ['PENDING', 'PARTIAL'] },
            dueDate: { gte: today, lte: nextMonth }
          },
          _sum: {
            totalAmount: true
          },
          _count: true
        }),
        
        // Total collected this month
        prisma.payment.aggregate({
          where: {
            paymentDate: {
              gte: new Date(today.getFullYear(), today.getMonth(), 1),
              lte: today
            }
          },
          _sum: {
            amount: true
          }
        })
      ])

      return NextResponse.json({
        totalPending,
        overdue: {
          count: overdueCount,
          amount: overdueAmount._sum.totalAmount || 0
        },
        upcoming: {
          week: {
            count: upcomingWeek._count,
            amount: upcomingWeek._sum.totalAmount || 0
          },
          month: {
            count: upcomingMonth._count,
            amount: upcomingMonth._sum.totalAmount || 0
          }
        },
        collected: {
          thisMonth: totalCollected._sum.amount || 0
        }
      })
    }

    if (action === 'overdue-customers') {
      // Get customers with overdue payments
      const overdueCustomers = await prisma.paymentSchedule.findMany({
        where: {
          status: { in: ['PENDING', 'PARTIAL'] },
          dueDate: { lt: new Date() }
        },
        include: {
          loan: {
            include: {
              customer: {
                select: {
                  id: true,
                  customerId: true,
                  firstName: true,
                  lastName: true,
                  phone: true,
                  email: true,
                }
              }
            }
          }
        },
        orderBy: { dueDate: 'asc' }
      })

      // Group by customer and calculate total overdue
      const customerOverdues = overdueCustomers.reduce((acc: any, schedule) => {
        const customerId = schedule.loan.customer.id
        if (!acc[customerId]) {
          acc[customerId] = {
            customer: schedule.loan.customer,
            totalOverdue: 0,
            overdueCount: 0,
            oldestOverdue: schedule.dueDate,
            loans: []
          }
        }
        
        acc[customerId].totalOverdue += schedule.totalAmount - (schedule.paidAmount || 0)
        acc[customerId].overdueCount += 1
        acc[customerId].loans.push({
          loanId: schedule.loan.loanId,
          dueDate: schedule.dueDate,
          amount: schedule.totalAmount - (schedule.paidAmount || 0)
        })
        
        return acc
      }, {})

      return NextResponse.json({
        overdueCustomers: Object.values(customerOverdues)
      })
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
  } catch (error) {
    console.error('Error processing payment schedule request:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
