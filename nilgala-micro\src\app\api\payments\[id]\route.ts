import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { hasPermission } from '@/lib/auth'
import { z } from 'zod'

// Validation schema for payment update
const updatePaymentSchema = z.object({
  amount: z.number().positive().optional(),
  paymentMethod: z.enum(['CASH', 'BANK_TRANSFER', 'CHEQUE', 'ONLINE', 'MOBILE_PAYMENT']).optional(),
  referenceNumber: z.string().optional(),
  notes: z.string().optional(),
  paymentDate: z.string().transform((str) => new Date(str)).optional(),
})

// GET /api/payments/[id] - Get payment by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermission(session.user.role, 'payments:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const payment = await prisma.payment.findUnique({
      where: { id: params.id },
      include: {
        loan: {
          select: {
            id: true,
            loanNumber: true,
            customer: {
              select: {
                id: true,
                nationalId: true,
                firstName: true,
                lastName: true,
                phone: true,
                email: true,
              }
            },
            loanType: {
              select: {
                name: true,
                interestRate: true,
              }
            }
          }
        },
        createdByUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            role: true,
          }
        }
      }
    })

    if (!payment) {
      return NextResponse.json({ error: 'Payment not found' }, { status: 404 })
    }

    return NextResponse.json(payment)
  } catch (error) {
    console.error('Error fetching payment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/payments/[id] - Update payment
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermission(session.user.role, 'payments:update')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = updatePaymentSchema.parse(body)

    // Check if payment exists
    const existingPayment = await prisma.payment.findUnique({
      where: { id: params.id },
      include: {
        loan: true
      }
    })

    if (!existingPayment) {
      return NextResponse.json({ error: 'Payment not found' }, { status: 404 })
    }

    // Only allow updates within 24 hours of creation (business rule)
    const hoursSinceCreation = (Date.now() - existingPayment.createdAt.getTime()) / (1000 * 60 * 60)
    if (hoursSinceCreation > 24) {
      return NextResponse.json(
        { error: 'Payments can only be modified within 24 hours of creation' },
        { status: 400 }
      )
    }

    // If amount is being changed, validate against loan outstanding
    if (validatedData.amount && validatedData.amount !== existingPayment.amount) {
      const totalPaid = await prisma.payment.aggregate({
        where: { 
          loanId: existingPayment.loanId,
          id: { not: existingPayment.id } // Exclude current payment
        },
        _sum: { amount: true }
      })

      const outstandingAmount = existingPayment.loan.disbursedAmount - (totalPaid._sum.amount || 0)

      if (validatedData.amount > outstandingAmount) {
        return NextResponse.json(
          { error: `Payment amount (${validatedData.amount}) exceeds outstanding amount (${outstandingAmount})` },
          { status: 400 }
        )
      }
    }

    const payment = await prisma.payment.update({
      where: { id: params.id },
      data: {
        ...validatedData,
        updatedAt: new Date(),
      }
    })

    // Create audit log
    await prisma.auditLog.create({
      data: {
        action: 'UPDATE',
        resource: 'Payment',
        resourceId: payment.id,
        userId: session.user.id,
        newValues: {
          amount: payment.amount,
          method: payment.method,
          status: payment.status
        }
      }
    })

    // Fetch updated payment with relations
    const completePayment = await prisma.payment.findUnique({
      where: { id: payment.id },
      include: {
        loan: {
          include: {
            customer: {
              select: {
                id: true,
                nationalId: true,
                firstName: true,
                lastName: true,
              }
            }
          }
        }
      }
    })

    return NextResponse.json(completePayment)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating payment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/payments/[id] - Delete payment (soft delete)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermission(session.user.role, 'payments:delete')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if payment exists
    const existingPayment = await prisma.payment.findUnique({
      where: { id: params.id },
      include: {
        loan: true
      }
    })

    if (!existingPayment) {
      return NextResponse.json({ error: 'Payment not found' }, { status: 404 })
    }

    // Only allow deletion within 1 hour of creation (strict business rule)
    const hoursSinceCreation = (Date.now() - existingPayment.createdAt.getTime()) / (1000 * 60 * 60)
    if (hoursSinceCreation > 1) {
      return NextResponse.json(
        { error: 'Payments can only be deleted within 1 hour of creation' },
        { status: 400 }
      )
    }

    // Only Super Admin or Higher Management can delete payments
    if (!['SUPER_ADMIN', 'HIGHER_MANAGEMENT'].includes(session.user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to delete payments' },
        { status: 403 }
      )
    }

    await prisma.$transaction(async (tx) => {
      // Soft delete the payment
      await tx.payment.update({
        where: { id: params.id },
        data: {
          deletedAt: new Date(),
          deletedBy: session.user.id,
        }
      })

      // Reverse payment schedule updates
      await reversePaymentScheduleUpdates(tx, existingPayment.loanId, existingPayment.amount)

      // Update loan status if needed
      const totalPaid = await tx.payment.aggregate({
        where: { 
          loanId: existingPayment.loanId,
          deletedAt: null,
          id: { not: existingPayment.id }
        },
        _sum: { amount: true }
      })

      if ((totalPaid._sum.amount || 0) < existingPayment.loan.disbursedAmount) {
        await tx.loan.update({
          where: { id: existingPayment.loanId },
          data: { status: 'ACTIVE' }
        })
      }
    })

    // Create audit log
    await prisma.auditLog.create({
      data: {
        action: 'DELETE',
        resource: 'Payment',
        resourceId: params.id,
        userId: session.user.id,
        oldValues: {
          amount: existingPayment.amount,
          method: existingPayment.method,
          status: existingPayment.status
        }
      }
    })

    return NextResponse.json({ message: 'Payment deleted successfully' })
  } catch (error) {
    console.error('Error deleting payment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper function to reverse payment schedule updates
async function reversePaymentScheduleUpdates(tx: any, loanId: string, paymentAmount: number) {
  // Get payment schedules that were affected, ordered by due date desc
  const affectedSchedules = await tx.paymentSchedule.findMany({
    where: {
      loanId,
      OR: [
        { status: 'PAID' },
        { status: 'PARTIAL' }
      ]
    },
    orderBy: { dueDate: 'desc' }
  })

  let remainingAmount = paymentAmount

  for (const schedule of affectedSchedules) {
    if (remainingAmount <= 0) break

    if (schedule.status === 'PAID' && remainingAmount >= schedule.totalAmount) {
      // Reverse full payment
      await tx.paymentSchedule.update({
        where: { id: schedule.id },
        data: {
          status: 'PENDING',
          paidAmount: 0,
          paidDate: null
        }
      })
      remainingAmount -= schedule.totalAmount
    } else if (schedule.status === 'PARTIAL') {
      // Reverse partial payment
      const newPaidAmount = Math.max(0, (schedule.paidAmount || 0) - remainingAmount)
      await tx.paymentSchedule.update({
        where: { id: schedule.id },
        data: {
          paidAmount: newPaidAmount,
          status: newPaidAmount > 0 ? 'PARTIAL' : 'PENDING'
        }
      })
      remainingAmount = 0
    }
  }
}
