import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { hasPermissionSync } from '@/lib/auth'
import { AuditLogger } from '@/lib/audit-logger'
import { z } from 'zod'

// Validation schema for payment creation
const createPaymentSchema = z.object({
  loanId: z.string().min(1, 'Loan ID is required'),
  amount: z.number().positive('Amount must be positive'),
  paymentMethod: z.enum(['CASH', 'BANK_TRANSFER', 'CHEQUE', 'ONLINE', 'MOBILE_PAYMENT']),
  referenceNumber: z.string().optional(),
  notes: z.string().optional(),
  paymentDate: z.string().transform((str) => new Date(str)).optional(),
})

// GET /api/payments - List payments with filtering
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermissionSync(session.user.role, 'payments:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const loanId = searchParams.get('loanId') || ''
    const paymentMethod = searchParams.get('paymentMethod') || ''
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}

    if (search) {
      where.OR = [
        { referenceNumber: { contains: search, mode: 'insensitive' } },
        { loan: { loanId: { contains: search, mode: 'insensitive' } } },
        { loan: { customer: { 
          OR: [
            { firstName: { contains: search, mode: 'insensitive' } },
            { lastName: { contains: search, mode: 'insensitive' } },
            { nationalId: { contains: search, mode: 'insensitive' } }
          ]
        }}}
      ]
    }

    if (loanId) {
      where.loanId = loanId
    }

    if (paymentMethod) {
      where.paymentMethod = paymentMethod
    }

    if (startDate && endDate) {
      where.paymentDate = {
        gte: new Date(startDate),
        lte: new Date(endDate)
      }
    }

    const [payments, total] = await Promise.all([
      prisma.payment.findMany({
        where,
        include: {
          loan: {
            select: {
              id: true,
              loanNumber: true,
              customer: {
                select: {
                  id: true,
                  nationalId: true,
                  firstName: true,
                  lastName: true,
                  phone: true,
                }
              },
              loanType: {
                select: {
                  name: true
                }
              }
            }
          },
          createdByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              role: true,
            }
          }
        },
        orderBy: { paymentDate: 'desc' },
        skip,
        take: limit,
      }),
      prisma.payment.count({ where })
    ])

    const pages = Math.ceil(total / limit)

    return NextResponse.json({
      payments,
      pagination: {
        page,
        limit,
        total,
        pages
      }
    })
  } catch (error) {
    console.error('Error fetching payments:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/payments - Create new payment
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermissionSync(session.user.role, 'payments:create')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createPaymentSchema.parse(body)

    // Check if loan exists and is active
    const loan = await prisma.loan.findUnique({
      where: { id: validatedData.loanId },
      include: {
        customer: {
          select: {
            id: true,
            nationalId: true,
            firstName: true,
            lastName: true,
          }
        }
      }
    })

    if (!loan) {
      return NextResponse.json({ error: 'Loan not found' }, { status: 404 })
    }

    if (!['ACTIVE', 'DISBURSED', 'OVERDUE'].includes(loan.status)) {
      return NextResponse.json(
        { error: 'Payments can only be made for active, disbursed, or overdue loans' },
        { status: 400 }
      )
    }

    // Calculate outstanding amount
    const totalPaid = await prisma.payment.aggregate({
      where: { loanId: loan.id },
      _sum: { amount: true }
    })

    const outstandingAmount = loan.disbursedAmount - (totalPaid._sum.amount || 0)

    if (validatedData.amount > outstandingAmount) {
      return NextResponse.json(
        { error: `Payment amount (${validatedData.amount}) exceeds outstanding amount (${outstandingAmount})` },
        { status: 400 }
      )
    }

    const payment = await prisma.$transaction(async (tx) => {
      // Create the payment
      const newPayment = await tx.payment.create({
        data: {
          loanId: validatedData.loanId,
          amount: validatedData.amount,
          paymentMethod: validatedData.paymentMethod,
          referenceNumber: validatedData.referenceNumber,
          notes: validatedData.notes,
          paymentDate: validatedData.paymentDate || new Date(),
          principalAmount: validatedData.amount, // For now, treat full amount as principal
          interestAmount: 0, // TODO: Calculate based on payment schedule
          penaltyAmount: 0,
          feeAmount: 0,
          status: 'COMPLETED',
          processedBy: session.user.id,
          processedAt: new Date(),
          collectedBy: session.user.id,
          collectionDate: new Date(),
          createdBy: session.user.id,
        }
      })

      // Update payment schedules
      await updatePaymentSchedules(tx, validatedData.loanId, validatedData.amount)

      // Check if loan is fully paid by verifying all payment schedules are completed
      const pendingSchedules = await tx.paymentSchedule.count({
        where: {
          loanId: validatedData.loanId,
          status: { in: ['PENDING', 'PARTIAL'] }
        }
      })

      // Only mark loan as completed if all schedules are paid
      if (pendingSchedules === 0) {
        await tx.loan.update({
          where: { id: loan.id },
          data: { status: 'COMPLETED' }
        })
      } else {
        // If loan was previously completed but now has pending schedules, mark as active
        if (loan.status === 'COMPLETED') {
          await tx.loan.update({
            where: { id: loan.id },
            data: { status: 'ACTIVE' }
          })
        }
      }

      return newPayment
    })

    // Create enhanced audit log
    const auditContext = AuditLogger.getContextFromRequest(request, session.user.id)
    await AuditLogger.logPaymentOperation(
      auditContext,
      'RECORD_PAYMENT',
      payment.id,
      undefined,
      {
        amount: validatedData.amount,
        paymentMethod: validatedData.paymentMethod,
        loanId: validatedData.loanId,
        loanNumber: loan.loanNumber,
        customerName: `${loan.customer.firstName} ${loan.customer.lastName}`,
        referenceNumber: validatedData.referenceNumber,
        paymentDate: validatedData.paymentDate || new Date()
      }
    )

    // Fetch complete payment with relations
    const completePayment = await prisma.payment.findUnique({
      where: { id: payment.id },
      include: {
        loan: {
          include: {
            customer: {
              select: {
                id: true,
                nationalId: true,
                firstName: true,
                lastName: true,
              }
            }
          }
        }
      }
    })

    return NextResponse.json(completePayment, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating payment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper function to update payment schedules
async function updatePaymentSchedules(tx: any, loanId: string, paymentAmount: number) {
  // Get all payment schedules (including partial ones) ordered by due date
  const schedules = await tx.paymentSchedule.findMany({
    where: {
      loanId,
      status: { in: ['PENDING', 'PARTIAL'] }
    },
    orderBy: { dueDate: 'asc' }
  })

  let remainingAmount = paymentAmount

  for (const schedule of schedules) {
    if (remainingAmount <= 0) break

    const currentPaid = schedule.paidAmount || 0
    const amountNeeded = schedule.totalAmount - currentPaid

    if (remainingAmount >= amountNeeded) {
      // Full payment for this schedule (complete it)
      await tx.paymentSchedule.update({
        where: { id: schedule.id },
        data: {
          status: 'PAID',
          paidAmount: schedule.totalAmount,
          paidDate: new Date()
        }
      })
      remainingAmount -= amountNeeded
    } else {
      // Partial payment - add to existing paid amount
      await tx.paymentSchedule.update({
        where: { id: schedule.id },
        data: {
          paidAmount: currentPaid + remainingAmount,
          status: 'PARTIAL'
        }
      })
      remainingAmount = 0
    }
  }
}
