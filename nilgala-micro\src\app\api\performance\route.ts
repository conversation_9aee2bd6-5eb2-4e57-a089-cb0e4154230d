import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, hasPermission } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !await hasPermission(session.user.role, 'reports:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const year = parseInt(searchParams.get('year') || new Date().getFullYear().toString())
    const period = searchParams.get('period') || 'monthly'

    // Only Credit Officers can view their own performance
    if (session.user.role !== 'CREDIT_OFFICER') {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    const performanceData = await getCreditOfficerPerformance(session.user.id, year, period)
    
    return NextResponse.json(performanceData)

  } catch (error) {
    console.error('Error fetching performance data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch performance data' },
      { status: 500 }
    )
  }
}

async function getCreditOfficerPerformance(officerId: string, year: number, period: string) {
  const currentDate = new Date()
  const currentMonth = currentDate.getMonth() + 1
  const currentYear = currentDate.getFullYear()

  // Get current month target and achievement
  const currentMonthTarget = await prisma.creditOfficerTarget.findUnique({
    where: {
      creditOfficerId_month_year: {
        creditOfficerId: officerId,
        month: currentMonth,
        year: currentYear
      }
    }
  })

  // Calculate current month achievement (disbursed loans this month)
  const startOfMonth = new Date(currentYear, currentMonth - 1, 1)
  const endOfMonth = new Date(currentYear, currentMonth, 0, 23, 59, 59)

  // Get achievement from loans where customer is assigned to officer
  const currentMonthAchievement = await prisma.loan.aggregate({
    _sum: { principalAmount: true },
    where: {
      customer: {
        assignedTo: officerId
      },
      status: {
        in: ['DISBURSED', 'ACTIVE', 'COMPLETED']
      },
      OR: [
        {
          disbursementDate: {
            gte: startOfMonth,
            lte: endOfMonth
          }
        },
        {
          approvalDate: {
            gte: startOfMonth,
            lte: endOfMonth
          }
        }
      ]
    }
  })

  const target = Number(currentMonthTarget?.loanTarget || 500000)
  const achievement = Number(currentMonthAchievement._sum.principalAmount || 0)
  const percentage = target > 0 ? (achievement / target) * 100 : 0

  // Get team ranking
  const allOfficers = await prisma.user.findMany({
    where: {
      role: 'CREDIT_OFFICER',
      isActive: true
    },
    select: { id: true }
  })

  // Calculate achievements for all officers this month
  const officerAchievements = await Promise.all(
    allOfficers.map(async (officer) => {
      const officerAchievement = await prisma.loan.aggregate({
        _sum: { principalAmount: true },
        where: {
          customer: {
            assignedTo: officer.id
          },
          status: {
            in: ['DISBURSED', 'ACTIVE', 'COMPLETED']
          },
          OR: [
            {
              disbursementDate: {
                gte: startOfMonth,
                lte: endOfMonth
              }
            },
            {
              approvalDate: {
                gte: startOfMonth,
                lte: endOfMonth
              }
            }
          ]
        }
      })
      return {
        officerId: officer.id,
        achievement: Number(officerAchievement._sum.principalAmount || 0)
      }
    })
  )

  // Sort by achievement and find rank
  officerAchievements.sort((a, b) => b.achievement - a.achievement)
  const rank = officerAchievements.findIndex(o => o.officerId === officerId) + 1

  // Get monthly history for the year
  const monthlyHistory = []
  for (let month = 1; month <= 12; month++) {
    const monthTarget = await prisma.creditOfficerTarget.findUnique({
      where: {
        creditOfficerId_month_year: {
          creditOfficerId: officerId,
          month: month,
          year: year
        }
      }
    })

    const monthStart = new Date(year, month - 1, 1)
    const monthEnd = new Date(year, month, 0, 23, 59, 59)

    const monthAchievement = await prisma.loan.aggregate({
      _sum: { principalAmount: true },
      where: {
        customer: {
          assignedTo: officerId
        },
        status: {
          in: ['DISBURSED', 'ACTIVE', 'COMPLETED']
        },
        OR: [
          {
            disbursementDate: {
              gte: monthStart,
              lte: monthEnd
            }
          },
          {
            approvalDate: {
              gte: monthStart,
              lte: monthEnd
            }
          }
        ]
      }
    })

    const monthTargetAmount = Number(monthTarget?.loanTarget || 0)
    const monthAchievementAmount = Number(monthAchievement._sum.principalAmount || 0)
    const monthPercentage = monthTargetAmount > 0 ? (monthAchievementAmount / monthTargetAmount) * 100 : 0

    if (monthTargetAmount > 0 || monthAchievementAmount > 0) {
      monthlyHistory.push({
        month: new Date(year, month - 1).toLocaleDateString('en-US', { month: 'long' }),
        target: monthTargetAmount,
        achievement: monthAchievementAmount,
        percentage: monthPercentage
      })
    }
  }

  // Calculate yearly stats
  const yearlyTargets = await prisma.creditOfficerTarget.findMany({
    where: {
      creditOfficerId: officerId,
      year: year
    }
  })

  const totalTarget = yearlyTargets.reduce((sum, target) => sum + Number(target.loanTarget), 0)
  const totalAchievement = monthlyHistory.reduce((sum, month) => sum + month.achievement, 0)
  const averagePercentage = monthlyHistory.length > 0 
    ? monthlyHistory.reduce((sum, month) => sum + month.percentage, 0) / monthlyHistory.length 
    : 0

  const bestMonth = monthlyHistory.reduce((best, month) => 
    month.percentage > best.percentage ? month : best, 
    { month: 'N/A', percentage: 0 }
  )

  // Get loan metrics
  const loanMetrics = await prisma.loan.aggregate({
    _count: true,
    _sum: { principalAmount: true },
    _avg: { principalAmount: true },
    where: {
      customer: {
        assignedTo: officerId
      }
    }
  })

  const activeLoanCount = await prisma.loan.count({
    where: {
      customer: {
        assignedTo: officerId
      },
      status: {
        in: ['ACTIVE', 'DISBURSED']
      }
    }
  })

  const completedLoanCount = await prisma.loan.count({
    where: {
      customer: {
        assignedTo: officerId
      },
      status: 'COMPLETED'
    }
  })

  // Get customer metrics - customers assigned to this officer
  const customerMetrics = await prisma.customer.aggregate({
    _count: true,
    where: {
      assignedTo: officerId
    }
  })

  const newCustomersThisMonth = await prisma.customer.count({
    where: {
      assignedTo: officerId,
      createdAt: {
        gte: startOfMonth,
        lte: endOfMonth
      }
    }
  })

  // Calculate customers with loans (assigned to this officer)
  const customersWithLoansFromOfficer = await prisma.customer.count({
    where: {
      assignedTo: officerId,
      loans: {
        some: {
          status: {
            in: ['COMPLETED', 'ACTIVE', 'DISBURSED', 'APPROVED']
          }
        }
      }
    }
  })

  // Calculate retention rate (customers with multiple loans)
  const customersWithMultipleLoans = await prisma.customer.count({
    where: {
      assignedTo: officerId,
      loans: {
        some: {
          status: {
            in: ['COMPLETED', 'ACTIVE', 'DISBURSED']
          }
        }
      }
    }
  })

  const retentionRate = customerMetrics._count > 0
    ? (customersWithMultipleLoans / customerMetrics._count) * 100
    : 0

  return {
    currentMonth: {
      target,
      achievement,
      percentage,
      rank,
      totalOfficers: allOfficers.length
    },
    monthlyHistory,
    yearlyStats: {
      totalTarget,
      totalAchievement,
      averagePercentage,
      bestMonth: bestMonth.month,
      bestPercentage: bestMonth.percentage
    },
    loanMetrics: {
      totalLoans: loanMetrics._count || 0,
      activeLoans: activeLoanCount,
      completedLoans: completedLoanCount,
      averageLoanAmount: Number(loanMetrics._avg.principalAmount || 0),
      totalDisbursed: Number(loanMetrics._sum.principalAmount || 0)
    },
    customerMetrics: {
      assignedCustomers: customerMetrics._count || 0,
      customersWithLoans: customersWithLoansFromOfficer,
      newCustomers: newCustomersThisMonth,
      retentionRate,
      satisfactionScore: 85 // Placeholder - could be calculated from feedback
    }
  }
}
