import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, hasPermission } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// Helper function to convert BigInt values to numbers recursively
function convertBigIntToNumber(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj
  }

  if (typeof obj === 'bigint') {
    return Number(obj)
  }

  if (Array.isArray(obj)) {
    return obj.map(convertBigIntToNumber)
  }

  if (typeof obj === 'object') {
    const converted: any = {}
    for (const [key, value] of Object.entries(obj)) {
      converted[key] = convertBigIntToNumber(value)
    }
    return converted
  }

  return obj
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !await hasPermission(session.user.role, 'reports:read')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const reportType = searchParams.get('type') || 'overview'
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    const start = startDate ? new Date(startDate) : new Date(new Date().getFullYear(), new Date().getMonth(), 1)
    const end = endDate ? new Date(endDate) : new Date()

    switch (reportType) {
      case 'overview':
        return await getOverviewReport(start, end)
      case 'loans':
        return await getLoansReport(start, end)
      case 'payments':
        return await getPaymentsReport(start, end)
      case 'customers':
        return await getCustomersReport(start, end)
      case 'portfolio':
        return await getPortfolioReport(start, end)
      default:
        return NextResponse.json({ error: 'Invalid report type' }, { status: 400 })
    }
  } catch (error) {
    console.error('Error generating report:', error)
    return NextResponse.json(
      { error: 'Failed to generate report' },
      { status: 500 }
    )
  }
}

async function getOverviewReport(startDate: Date, endDate: Date) {
  try {
    console.log('Getting overview report for period:', startDate, 'to', endDate)

    const [
      totalLoans,
      totalCustomers,
      totalDisbursed,
      totalCollected,
      activeLoans,
      overdueLoans,
      recentActivities
    ] = await Promise.all([
    // Total loans
    prisma.loan.count(),
    
    // Total customers
    prisma.customer.count(),
    
    // Total disbursed amount (all time for overview, but within date range for specific period analysis)
    prisma.loan.aggregate({
      where: {
        status: { in: ['DISBURSED', 'ACTIVE', 'COMPLETED'] },
        disbursementDate: { not: null }
      },
      _sum: { disbursedAmount: true }
    }),

    // Total collected amount (within date range)
    prisma.payment.aggregate({
      where: {
        paymentDate: { gte: startDate, lte: endDate }
      },
      _sum: { amount: true }
    }),
    
    // Active loans
    prisma.loan.count({
      where: { status: { in: ['ACTIVE', 'DISBURSED'] } }
    }),
    
    // Overdue loans (simplified - would need proper overdue logic)
    prisma.loan.count({
      where: { 
        status: 'ACTIVE',
        // Add overdue logic based on payment schedules
      }
    }),
    
    // Recent activities from audit logs
    prisma.auditLog.findMany({
      take: 10,
      orderBy: { timestamp: 'desc' },
      where: {
        timestamp: { gte: startDate, lte: endDate }
      },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true
          }
        }
      }
    })
    ])

    console.log('Overview report data:', {
      totalLoans,
      totalCustomers,
      totalDisbursed: totalDisbursed._sum.disbursedAmount,
      totalCollected: totalCollected._sum.amount,
      activeLoans,
      overdueLoans,
      recentActivitiesCount: recentActivities.length
    })

    const result = {
      overview: {
        totalLoans,
        totalCustomers,
        totalDisbursed: totalDisbursed._sum.disbursedAmount || 0,
        totalCollected: totalCollected._sum.amount || 0,
        activeLoans,
        overdueLoans
      },
      recentActivities,
      period: { startDate, endDate }
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error in getOverviewReport:', error)
    return NextResponse.json({
      overview: {
        totalLoans: 0,
        totalCustomers: 0,
        totalDisbursed: 0,
        totalCollected: 0,
        activeLoans: 0,
        overdueLoans: 0
      },
      recentActivities: [],
      period: { startDate, endDate },
      error: 'Failed to generate overview report'
    })
  }
}

async function getLoansReport(startDate: Date, endDate: Date) {
  try {
    console.log('Getting loans report for period:', startDate, 'to', endDate)

    const [
      loansByStatus,
      loansByType,
      disbursementTrend,
      averageLoanAmount
    ] = await Promise.all([
    // Loans by status
    prisma.loan.groupBy({
      by: ['status'],
      _count: { id: true },
      _sum: { principalAmount: true }
    }),
    
    // Loans by type
    prisma.loan.groupBy({
      by: ['loanTypeId'],
      _count: { id: true },
      _sum: { principalAmount: true }
    }),
    
    // Disbursement trend (monthly) - show all disbursements, not just within date range
    prisma.$queryRaw`
      SELECT
        DATE_TRUNC('month', "disbursementDate") as month,
        COUNT(*) as count,
        SUM("disbursedAmount") as amount
      FROM loans
      WHERE "disbursementDate" IS NOT NULL
        AND "disbursedAmount" IS NOT NULL
      GROUP BY DATE_TRUNC('month', "disbursementDate")
      ORDER BY month DESC
      LIMIT 12
    `,

    // Average loan amount (all loans)
    prisma.loan.aggregate({
      where: {
        principalAmount: { gt: 0 }
      },
      _avg: { principalAmount: true }
    })
    ])

    console.log('Loans report data:', {
      loansByStatusCount: loansByStatus.length,
      loansByTypeCount: loansByType.length,
      disbursementTrendCount: disbursementTrend.length,
      averageLoanAmount: averageLoanAmount._avg.principalAmount
    })

    // Convert all BigInt values to numbers for JSON serialization
    const result = convertBigIntToNumber({
      loansByStatus,
      loansByType,
      disbursementTrend,
      averageLoanAmount: averageLoanAmount._avg.principalAmount || 0,
      period: { startDate, endDate }
    })

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error in getLoansReport:', error)
    return NextResponse.json({
      loansByStatus: [],
      loansByType: [],
      disbursementTrend: [],
      averageLoanAmount: 0,
      period: { startDate, endDate },
      error: 'Failed to generate loans report'
    })
  }
}

async function getPaymentsReport(startDate: Date, endDate: Date) {
  try {
    console.log('Getting payments report for period:', startDate, 'to', endDate)

    const [
      paymentsByMethod,
      paymentTrend,
      totalCollections,
      averagePayment
    ] = await Promise.all([
    // Payments by method (all payments)
    prisma.payment.groupBy({
      by: ['paymentMethod'],
      _count: { id: true },
      _sum: { amount: true }
    }),

    // Payment trend (daily) - last 30 days or within date range
    prisma.$queryRaw`
      SELECT
        DATE_TRUNC('day', "paymentDate") as day,
        COUNT(*) as count,
        SUM("amount") as amount
      FROM payments
      WHERE "paymentDate" >= ${startDate}
        AND "paymentDate" <= ${endDate}
      GROUP BY DATE_TRUNC('day', "paymentDate")
      ORDER BY day DESC
    `,

    // Total collections (within date range)
    prisma.payment.aggregate({
      where: {
        paymentDate: { gte: startDate, lte: endDate }
      },
      _sum: { amount: true },
      _count: { id: true }
    }),

    // Average payment (all payments)
    prisma.payment.aggregate({
      _avg: { amount: true }
    })
    ])

    console.log('Payments report data:', {
      paymentsByMethodCount: paymentsByMethod.length,
      paymentTrendCount: paymentTrend.length,
      totalCollections: totalCollections._sum.amount,
      averagePayment: averagePayment._avg.amount
    })

    // Convert all BigInt values to numbers for JSON serialization
    const result = convertBigIntToNumber({
      paymentsByMethod,
      paymentTrend,
      totalCollections: {
        amount: totalCollections._sum.amount || 0,
        count: totalCollections._count.id || 0
      },
      averagePayment: averagePayment._avg.amount || 0,
      period: { startDate, endDate }
    })

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error in getPaymentsReport:', error)
    return NextResponse.json({
      paymentsByMethod: [],
      paymentTrend: [],
      totalCollections: { amount: 0, count: 0 },
      averagePayment: 0,
      period: { startDate, endDate },
      error: 'Failed to generate payments report'
    })
  }
}

async function getCustomersReport(startDate: Date, endDate: Date) {
  try {
    console.log('Getting customers report for period:', startDate, 'to', endDate)

    const [
      newCustomers,
      customersByEmployment,
      customersByCity,
      averageIncome
    ] = await Promise.all([
    // New customers
    prisma.customer.count({
      where: {
        createdAt: { gte: startDate, lte: endDate }
      }
    }),
    
    // Customers by employment type
    prisma.customer.groupBy({
      by: ['employmentType'],
      _count: { id: true }
    }),
    
    // Customers by city
    prisma.customer.groupBy({
      by: ['city'],
      _count: { id: true }
    }),
    
    // Average monthly income
    prisma.customer.aggregate({
      _avg: { monthlyIncome: true }
    })
    ])

    console.log('Customers report data:', {
      newCustomers,
      customersByEmploymentCount: customersByEmployment.length,
      customersByCityCount: customersByCity.length,
      averageIncome: averageIncome._avg.monthlyIncome
    })

    const result = {
      newCustomers,
      customersByEmployment,
      customersByCity,
      averageIncome: averageIncome._avg.monthlyIncome || 0,
      period: { startDate, endDate }
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error in getCustomersReport:', error)
    return NextResponse.json({
      newCustomers: 0,
      customersByEmployment: [],
      customersByCity: [],
      averageIncome: 0,
      period: { startDate, endDate },
      error: 'Failed to generate customers report'
    })
  }
}

async function getPortfolioReport(startDate: Date, endDate: Date) {
  try {
    console.log('Getting portfolio report for period:', startDate, 'to', endDate)

    const [
      portfolioValue,
      outstandingAmount,
      collectionRate,
      riskAnalysis
    ] = await Promise.all([
    // Total portfolio value
    prisma.loan.aggregate({
      where: {
        status: { in: ['ACTIVE', 'DISBURSED'] }
      },
      _sum: { totalAmount: true }
    }),
    
    // Outstanding amount
    prisma.loan.aggregate({
      where: {
        status: { in: ['ACTIVE', 'DISBURSED'] }
      },
      _sum: { disbursedAmount: true }
    }),
    
    // Collection rate (simplified)
    prisma.payment.aggregate({
      where: {
        paymentDate: { gte: startDate, lte: endDate }
      },
      _sum: { amount: true }
    }),
    
    // Risk analysis (loans by status)
    prisma.loan.groupBy({
      by: ['status'],
      _count: { id: true },
      _sum: { disbursedAmount: true }
    })
    ])

    console.log('Portfolio report data:', {
      portfolioValue: portfolioValue._sum.totalAmount,
      outstandingAmount: outstandingAmount._sum.disbursedAmount,
      collectionRate: collectionRate._sum.amount,
      riskAnalysisCount: riskAnalysis.length
    })

    const result = {
      portfolioValue: portfolioValue._sum.totalAmount || 0,
      outstandingAmount: outstandingAmount._sum.disbursedAmount || 0,
      collectionRate: collectionRate._sum.amount || 0,
      riskAnalysis,
      period: { startDate, endDate }
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error in getPortfolioReport:', error)
    return NextResponse.json({
      portfolioValue: 0,
      outstandingAmount: 0,
      collectionRate: 0,
      riskAnalysis: [],
      period: { startDate, endDate },
      error: 'Failed to generate portfolio report'
    })
  }
}
