import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { hasPermission } from '@/lib/auth'
import { z } from 'zod'
import { handleApiError } from '@/lib/validation-utils'

// Validation schema for required document creation
const createRequiredDocumentSchema = z.object({
  name: z.string().min(1, 'Document name is required'),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
})

// GET /api/required-documents - List all required documents
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    const hasSettingsRead = await hasPermission(session.user.role, 'settings:read')
    const hasLoanTypesRead = await hasPermission(session.user.role, 'loan-types:read')

    if (!session || !(hasSettingsRead || hasLoanTypesRead)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const active = searchParams.get('active')

    const where: any = {}
    if (active === 'true') {
      where.isActive = true
    }

    const documents = await prisma.requiredDocument.findMany({
      where,
      orderBy: { name: 'asc' }
    })

    return NextResponse.json(documents)
  } catch (error) {
    console.error('Error fetching required documents:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/required-documents - Create new required document
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !(await hasPermission(session.user.role, 'settings:update'))) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createRequiredDocumentSchema.parse(body)

    // Check if document with same name already exists
    const existingDocument = await prisma.requiredDocument.findFirst({
      where: { name: validatedData.name }
    })

    if (existingDocument) {
      return NextResponse.json(
        { error: 'Required document with this name already exists' },
        { status: 400 }
      )
    }

    const document = await prisma.requiredDocument.create({
      data: validatedData
    })

    // Create audit log
    await prisma.auditLog.create({
      data: {
        action: 'CREATE',
        resource: 'RequiredDocument',
        resourceId: document.id,
        userId: session.user.id,
        newValues: {
          name: document.name,
          description: document.description
        }
      }
    })

    return NextResponse.json(document, { status: 201 })
  } catch (error) {
    return handleApiError(error, 'creating required document')
  }
}

// PUT /api/required-documents - Update required document
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !(await hasPermission(session.user.role, 'settings:update'))) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { id, ...updateData } = body
    const validatedData = createRequiredDocumentSchema.partial().parse(updateData)

    if (!id) {
      return NextResponse.json({ error: 'Document ID is required' }, { status: 400 })
    }

    // Check if document exists
    const existingDocument = await prisma.requiredDocument.findUnique({
      where: { id }
    })

    if (!existingDocument) {
      return NextResponse.json({ error: 'Required document not found' }, { status: 404 })
    }

    // Check if name is unique (if being updated)
    if (validatedData.name && validatedData.name !== existingDocument.name) {
      const existingWithName = await prisma.requiredDocument.findFirst({
        where: { 
          name: validatedData.name,
          id: { not: id }
        }
      })

      if (existingWithName) {
        return NextResponse.json(
          { error: 'Required document with this name already exists' },
          { status: 400 }
        )
      }
    }

    const document = await prisma.requiredDocument.update({
      where: { id },
      data: validatedData
    })

    // Create audit log
    await prisma.auditLog.create({
      data: {
        action: 'UPDATE',
        resource: 'RequiredDocument',
        resourceId: document.id,
        userId: session.user.id,
        oldValues: {
          name: existingDocument.name,
          description: existingDocument.description
        },
        newValues: {
          name: document.name,
          description: document.description
        }
      }
    })

    return NextResponse.json(document)
  } catch (error) {
    return handleApiError(error, 'updating required document')
  }
}

// DELETE /api/required-documents - Delete required document
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !(await hasPermission(session.user.role, 'settings:update'))) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({ error: 'Document ID is required' }, { status: 400 })
    }

    // Check if document exists
    const existingDocument = await prisma.requiredDocument.findUnique({
      where: { id }
    })

    if (!existingDocument) {
      return NextResponse.json({ error: 'Required document not found' }, { status: 404 })
    }

    // Soft delete by setting isActive to false
    const document = await prisma.requiredDocument.update({
      where: { id },
      data: { isActive: false }
    })

    // Create audit log
    await prisma.auditLog.create({
      data: {
        action: 'DELETE',
        resource: 'RequiredDocument',
        resourceId: document.id,
        userId: session.user.id,
        oldValues: {
          name: existingDocument.name,
          isActive: existingDocument.isActive
        },
        newValues: {
          name: document.name,
          isActive: document.isActive
        }
      }
    })

    return NextResponse.json({ message: 'Required document deactivated successfully' })
  } catch (error) {
    console.error('Error deleting required document:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
