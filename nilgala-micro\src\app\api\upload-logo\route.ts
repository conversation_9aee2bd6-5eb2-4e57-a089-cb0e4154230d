import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { hasPermission } from '@/lib/auth'
import { uploadFileToR2, deleteFileFromR2 } from '@/lib/r2-client'
import { writeFile, unlink } from 'fs/promises'
import { join } from 'path'

// POST /api/upload-logo - Upload company logo
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !hasPermission(session.user.role, 'settings:update')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const formData = await request.formData()
    const file = formData.get('logo') as File

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // Validate file size (max 5MB for logos)
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json({ error: 'File size must be less than 5MB' }, { status: 400 })
    }

    // Check file type (only images)
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/svg+xml']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ 
        error: 'Only JPEG, PNG, and SVG files are allowed' 
      }, { status: 400 })
    }

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer())

    // Save to public directory as mainlogo.png (or maintain original extension for SVG)
    const extension = file.type === 'image/svg+xml' ? 'svg' : 'png'
    const fileName = `mainlogo.${extension}`
    const publicPath = join(process.cwd(), 'public', fileName)

    try {
      // Remove existing logo files
      const possibleExtensions = ['png', 'jpg', 'jpeg', 'svg']
      for (const ext of possibleExtensions) {
        try {
          await unlink(join(process.cwd(), 'public', `mainlogo.${ext}`))
        } catch (error) {
          // File doesn't exist, ignore
        }
      }

      // Write new logo file
      await writeFile(publicPath, buffer)

      // Update database with the new logo path
      const logoUrl = `/${fileName}`
      await prisma.systemConfig.upsert({
        where: { key: 'COMPANY_LOGO' },
        update: { 
          value: logoUrl,
          category: 'COMPANY_SETTINGS'
        },
        create: { 
          key: 'COMPANY_LOGO',
          value: logoUrl,
          category: 'COMPANY_SETTINGS',
          description: 'Company logo URL'
        }
      })

      // Create audit log
      try {
        const userExists = await prisma.user.findUnique({
          where: { id: session.user.id },
          select: { id: true }
        })

        if (userExists) {
          await prisma.auditLog.create({
            data: {
              action: 'UPDATE',
              resource: 'CompanyLogo',
              resourceId: 'company-logo',
              userId: session.user.id,
              newValues: { companyLogo: logoUrl }
            }
          })
        }
      } catch (auditError) {
        console.error('Failed to create audit log:', auditError)
      }

      return NextResponse.json({
        message: 'Logo uploaded successfully',
        logoUrl: logoUrl
      }, { status: 200 })

    } catch (fileError) {
      console.error('Error saving logo file:', fileError)
      return NextResponse.json(
        { error: 'Failed to save logo file' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Error uploading logo:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
