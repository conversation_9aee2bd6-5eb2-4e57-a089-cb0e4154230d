import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, getRolePermissions } from '@/lib/auth'
import { UserRole } from '@prisma/client'

// Get current user's permissions
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userRole = session.user.role as UserRole
    
    // Get all role permissions from database
    const allRolePermissions = await getRolePermissions()
    
    // Get user's specific permissions
    const userPermissions = allRolePermissions[userRole] || []

    // Define available permissions by category for reference
    const availablePermissions = {
      users: [
        'users:create',
        'users:read', 
        'users:update',
        'users:delete'
      ],
      customers: [
        'customers:create',
        'customers:read',
        'customers:update', 
        'customers:delete'
      ],
      loans: [
        'loans:create',
        'loans:read',
        'loans:update',
        'loans:delete',
        'loans:approve',
        'loans:disburse'
      ],
      payments: [
        'payments:create',
        'payments:read',
        'payments:update',
        'payments:delete'
      ],
      reports: [
        'reports:read',
        'reports:export',
        'reports:create'
      ],
      settings: [
        'settings:read',
        'settings:update'
      ],
      audit: [
        'audit:read'
      ],
      documents: [
        'documents:create',
        'documents:read',
        'documents:update',
        'documents:delete'
      ],
      loanTypes: [
        'loan-types:create',
        'loan-types:read',
        'loan-types:update',
        'loan-types:delete'
      ]
    }

    return NextResponse.json({
      role: userRole,
      permissions: userPermissions,
      availablePermissions,
      hasPermission: (permission: string) => userPermissions.includes(permission)
    })

  } catch (error) {
    console.error('User permissions fetch error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch user permissions' },
      { status: 500 }
    )
  }
}
