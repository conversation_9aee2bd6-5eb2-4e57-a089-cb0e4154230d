import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions, hasPermissionSync } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { UserRole } from '@prisma/client'

// GET /api/users/credit-officers - Get list of active Credit Officers
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to read users or create customers
    const canReadUsers = hasPermissionSync(session.user.role as UserRole, 'users:read')
    const canCreateCustomers = hasPermissionSync(session.user.role as UserRole, 'customers:create')
    
    if (!canReadUsers && !canCreateCustomers) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get all active Credit Officers
    const creditOfficers = await prisma.user.findMany({
      where: {
        role: 'CREDIT_OFFICER',
        isActive: true
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
        _count: {
          select: {
            assignedCustomers: true,
            createdLoans: true
          }
        }
      },
      orderBy: [
        { firstName: 'asc' },
        { lastName: 'asc' }
      ]
    })

    // Format the response with additional info
    const formattedOfficers = creditOfficers.map(officer => ({
      id: officer.id,
      name: `${officer.firstName} ${officer.lastName}`,
      email: officer.email,
      phone: officer.phone,
      assignedCustomers: officer._count.assignedCustomers,
      totalLoans: officer._count.createdLoans,
      workload: officer._count.assignedCustomers // Can be used for load balancing
    }))

    return NextResponse.json({
      creditOfficers: formattedOfficers,
      total: formattedOfficers.length
    })

  } catch (error) {
    console.error('Error fetching credit officers:', error)
    return NextResponse.json(
      { error: 'Failed to fetch credit officers' },
      { status: 500 }
    )
  }
}
