'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import PageHeader from '@/components/layout/PageHeader'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Calendar, Clock, Users, DollarSign, AlertTriangle, CheckCircle } from 'lucide-react'
import CalendarView from '@/components/calendar/CalendarView'
import ScheduleList from '@/components/calendar/ScheduleList'
import PaymentReminders from '@/components/calendar/PaymentReminders'

interface CalendarEvent {
  id: string
  title: string
  date: string
  time: string
  type: 'payment' | 'meeting' | 'collection' | 'reminder'
  status: 'pending' | 'completed' | 'overdue'
  amount?: number
  customer?: {
    name: string
    id: string
  }
  loan?: {
    id: string
    amount: number
  }
}

interface CalendarStats {
  todayPayments: number
  overduePayments: number
  upcomingMeetings: number
  totalScheduled: number
}

export default function CalendarPage() {
  const { data: session } = useSession()
  const [events, setEvents] = useState<CalendarEvent[]>([])
  const [stats, setStats] = useState<CalendarStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('calendar')

  useEffect(() => {
    fetchCalendarData()
  }, [])

  const fetchCalendarData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/calendar')
      if (!response.ok) throw new Error('Failed to fetch calendar data')
      
      const data = await response.json()
      setEvents(data.events || [])
      setStats(data.stats || {
        todayPayments: 0,
        overduePayments: 0,
        upcomingMeetings: 0,
        totalScheduled: 0
      })
    } catch (error) {
      console.error('Error fetching calendar data:', error)
      // Set mock data for development
      setEvents([
        {
          id: '1',
          title: 'Payment Due - John Doe',
          date: '2025-01-15',
          time: '10:00',
          type: 'payment',
          status: 'pending',
          amount: 5000,
          customer: { name: 'John Doe', id: 'cust1' },
          loan: { id: 'loan1', amount: 50000 }
        },
        {
          id: '2',
          title: 'Collection Visit - Jane Smith',
          date: '2025-01-15',
          time: '14:00',
          type: 'collection',
          status: 'pending',
          customer: { name: 'Jane Smith', id: 'cust2' }
        }
      ])
      setStats({
        todayPayments: 5,
        overduePayments: 2,
        upcomingMeetings: 3,
        totalScheduled: 15
      })
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <PageHeader title="Calendar & Schedule Management" description="Manage payment schedules and appointments">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </PageHeader>
    )
  }

  return (
    <PageHeader
      title="Calendar & Schedule Management"
      description="Manage payment schedules, appointments, and reminders"
    >
      <div className="space-y-6">
        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Today's Payments</p>
                  <p className="text-2xl font-bold text-blue-600">{stats?.todayPayments || 0}</p>
                </div>
                <DollarSign className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Overdue</p>
                  <p className="text-2xl font-bold text-red-600">{stats?.overduePayments || 0}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Meetings</p>
                  <p className="text-2xl font-bold text-green-600">{stats?.upcomingMeetings || 0}</p>
                </div>
                <Users className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Scheduled</p>
                  <p className="text-2xl font-bold text-purple-600">{stats?.totalScheduled || 0}</p>
                </div>
                <Calendar className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Calendar Interface */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="calendar">Calendar View</TabsTrigger>
            <TabsTrigger value="schedule">Schedule List</TabsTrigger>
            <TabsTrigger value="reminders">Reminders</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
          </TabsList>

          <TabsContent value="calendar" className="space-y-4">
            <CalendarView events={events} onEventUpdate={fetchCalendarData} />
          </TabsContent>

          <TabsContent value="schedule" className="space-y-4">
            <ScheduleList events={events} onEventUpdate={fetchCalendarData} />
          </TabsContent>

          <TabsContent value="reminders" className="space-y-4">
            <PaymentReminders events={events} onEventUpdate={fetchCalendarData} />
          </TabsContent>

          <TabsContent value="reports" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Calendar Reports</CardTitle>
                <CardDescription>Analytics and reports for scheduled activities</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">Calendar reports and analytics will be implemented here.</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </PageHeader>
  )
}
