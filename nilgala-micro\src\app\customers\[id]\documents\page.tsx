'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useToast } from '@/hooks/use-toast'
import { ArrowLeft, FileText, Upload, Download, Trash2, Eye, Plus } from 'lucide-react'
import Link from 'next/link'
import PageHeader from '@/components/layout/PageHeader'

interface Document {
  id: string
  fileName: string
  originalName: string
  mimeType: string
  fileSize: number
  documentType: string
  uploadedAt: string
  uploadedBy?: string
}

interface Customer {
  id: string
  firstName: string
  lastName: string
  nationalId: string
}

interface CustomerDocumentsResponse {
  customer: Customer
  documents: Document[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

const DOCUMENT_TYPES = [
  'NATIONAL_ID',
  'PASSPORT',
  'DRIVING_LICENSE',
  'UTILITY_BILL',
  'BANK_STATEMENT',
  'INCOME_CERTIFICATE',
  'PROPERTY_DEED',
  'BUSINESS_REGISTRATION',
  'LOAN_APPLICATION',
  'GUARANTOR_ID',
  'OTHER'
]

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getDocumentTypeLabel = (type: string) => {
  return type.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
}

export default function CustomerDocumentsPage() {
  const params = useParams()
  const router = useRouter()
  const { data: session } = useSession()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [uploading, setUploading] = useState(false)
  const [data, setData] = useState<CustomerDocumentsResponse | null>(null)
  const [page, setPage] = useState(1)
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [documentType, setDocumentType] = useState('')
  const limit = 10

  const customerId = params.id as string

  useEffect(() => {
    if (session) {
      fetchCustomerDocuments()
    }
  }, [session, page])

  const fetchCustomerDocuments = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/customers/${customerId}/documents?page=${page}&limit=${limit}`)
      
      if (response.ok) {
        const result = await response.json()
        setData(result)
      } else {
        toast({
          title: 'Error',
          description: 'Failed to fetch customer documents',
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error fetching customer documents:', error)
      toast({
        title: 'Error',
        description: 'Failed to fetch customer documents',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleFileUpload = async () => {
    if (!selectedFile || !documentType) {
      toast({
        title: 'Error',
        description: 'Please select a file and document type',
        variant: 'destructive'
      })
      return
    }

    try {
      setUploading(true)
      const formData = new FormData()
      formData.append('file', selectedFile)
      formData.append('documentType', documentType)

      const response = await fetch(`/api/customers/${customerId}/documents`, {
        method: 'POST',
        body: formData
      })

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'Document uploaded successfully'
        })
        setUploadDialogOpen(false)
        setSelectedFile(null)
        setDocumentType('')
        fetchCustomerDocuments()
      } else {
        const error = await response.json()
        toast({
          title: 'Error',
          description: error.error || 'Failed to upload document',
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error uploading document:', error)
      toast({
        title: 'Error',
        description: 'Failed to upload document',
        variant: 'destructive'
      })
    } finally {
      setUploading(false)
    }
  }

  const handleDownload = async (documentId: string, fileName: string) => {
    try {
      const response = await fetch(`/api/customers/${customerId}/documents/${documentId}/download`)
      
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = fileName
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        toast({
          title: 'Error',
          description: 'Failed to download document',
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error downloading document:', error)
      toast({
        title: 'Error',
        description: 'Failed to download document',
        variant: 'destructive'
      })
    }
  }

  const handlePreview = (documentId: string, fileName: string) => {
    // Open document in new tab for preview
    window.open(`/api/customers/${customerId}/documents/${documentId}/view`, '_blank')
  }

  const handleDelete = async (documentId: string) => {
    if (!confirm('Are you sure you want to delete this document?')) {
      return
    }

    try {
      const response = await fetch(`/api/customers/${customerId}/documents/${documentId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'Document deleted successfully'
        })
        fetchCustomerDocuments()
      } else {
        toast({
          title: 'Error',
          description: 'Failed to delete document',
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error deleting document:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete document',
        variant: 'destructive'
      })
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-2"></div>
          <p className="text-gray-600">Loading customer documents...</p>
        </div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">Failed to load customer documents</p>
      </div>
    )
  }

  return (
    <>
      <PageHeader
        title={`${data.customer.firstName} ${data.customer.lastName} - Documents`}
        description={`Customer ID: ${data.customer.nationalId}`}
        actions={
          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <Link href={`/customers/${customerId}`}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Customer
              </Link>
            </Button>
            <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Upload Document
                </Button>
              </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Upload Document</DialogTitle>
              <DialogDescription>
                Upload a new document for {data.customer.firstName} {data.customer.lastName}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="file">Select File</Label>
                <Input
                  id="file"
                  type="file"
                  onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                />
              </div>
              <div>
                <Label htmlFor="documentType">Document Type</Label>
                <Select value={documentType} onValueChange={setDocumentType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select document type" />
                  </SelectTrigger>
                  <SelectContent>
                    {DOCUMENT_TYPES.map((type) => (
                      <SelectItem key={type} value={type}>
                        {getDocumentTypeLabel(type)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setUploadDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleFileUpload} disabled={uploading}>
                  {uploading ? 'Uploading...' : 'Upload'}
                </Button>
              </div>
            </div>
          </DialogContent>
            </Dialog>
          </div>
        }
      >

      {/* Summary Card */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex items-center">
            <FileText className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Documents</p>
              <p className="text-2xl font-bold">{data.pagination.total}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Documents Table */}
      <Card>
        <CardHeader>
          <CardTitle>Documents</CardTitle>
          <CardDescription>
            All documents for {data.customer.firstName} {data.customer.lastName}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {data.documents.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No documents found for this customer</p>
              <Button className="mt-4" onClick={() => setUploadDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Upload First Document
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>File Name</TableHead>
                    <TableHead>Document Type</TableHead>
                    <TableHead>File Size</TableHead>
                    <TableHead>Upload Date</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.documents.map((document) => (
                    <TableRow key={document.id}>
                      <TableCell className="font-medium">{document.originalName}</TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {getDocumentTypeLabel(document.documentType)}
                        </Badge>
                      </TableCell>
                      <TableCell>{formatFileSize(document.fileSize)}</TableCell>
                      <TableCell>
                        {new Date(document.uploadedAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePreview(document.id, document.originalName)}
                            title="Preview Document"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDownload(document.id, document.originalName)}
                            title="Download Document"
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(document.id)}
                            title="Delete Document"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {data.pagination.pages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-700">
            Showing {((page - 1) * limit) + 1} to {Math.min(page * limit, data.pagination.total)} of{' '}
            {data.pagination.total} documents
          </p>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={page === data.pagination.pages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
      </PageHeader>
    </>
  )
}
