'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useToast } from '@/hooks/use-toast'
import { ArrowLeft, CreditCard, Eye, Plus, Calendar, DollarSign } from 'lucide-react'
import Link from 'next/link'
import { formatCurrency } from '@/lib/utils'
import PageHeader from '@/components/layout/PageHeader'

interface Loan {
  id: string
  loanNumber: string
  principalAmount: number
  totalAmount: number
  disbursedAmount?: number
  interestRate: number
  tenure: number
  repaymentFrequency: string
  status: string
  applicationDate: string
  disbursementDate?: string
  maturityDate?: string
  loanType: {
    name: string
  }
  payments: {
    amount: number
  }[]
  _count: {
    payments: number
  }
}

interface Customer {
  id: string
  firstName: string
  lastName: string
  nationalId: string
}

interface CustomerLoansResponse {
  customer: Customer
  loans: Loan[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'ACTIVE':
      return 'bg-green-100 text-green-800'
    case 'DISBURSED':
      return 'bg-blue-100 text-blue-800'
    case 'COMPLETED':
      return 'bg-gray-100 text-gray-800'
    case 'PENDING_APPROVAL':
      return 'bg-yellow-100 text-yellow-800'
    case 'REJECTED':
      return 'bg-red-100 text-red-800'
    case 'DRAFT':
      return 'bg-gray-100 text-gray-600'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

export default function CustomerLoansPage() {
  const params = useParams()
  const router = useRouter()
  const { data: session } = useSession()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [data, setData] = useState<CustomerLoansResponse | null>(null)
  const [page, setPage] = useState(1)
  const limit = 10

  const customerId = params.id as string

  useEffect(() => {
    if (session) {
      fetchCustomerLoans()
    }
  }, [session, page])

  const fetchCustomerLoans = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/customers/${customerId}/loans?page=${page}&limit=${limit}`)
      
      if (response.ok) {
        const result = await response.json()
        setData(result)
      } else {
        toast({
          title: 'Error',
          description: 'Failed to fetch customer loans',
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error fetching customer loans:', error)
      toast({
        title: 'Error',
        description: 'Failed to fetch customer loans',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const calculatePaidAmount = (payments: { amount: number }[]) => {
    return payments.reduce((total, payment) => total + Number(payment.amount), 0)
  }

  const calculateOutstanding = (totalAmount: number, payments: { amount: number }[]) => {
    const paidAmount = calculatePaidAmount(payments)
    return Math.max(0, totalAmount - paidAmount)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-2"></div>
          <p className="text-gray-600">Loading customer loans...</p>
        </div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">Failed to load customer loans</p>
      </div>
    )
  }

  return (
    <PageHeader
      title={`${data.customer.firstName} ${data.customer.lastName} - Loans`}
      description={`Customer ID: ${data.customer.nationalId}`}
      actions={
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href={`/customers/${customerId}`}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Customer
            </Link>
          </Button>
          <Button asChild>
            <Link href={`/loans/new?customerId=${customerId}`}>
              <Plus className="h-4 w-4 mr-2" />
              New Loan
            </Link>
          </Button>
        </div>
      }
    >

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CreditCard className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Loans</p>
                <p className="text-2xl font-bold">{data.pagination.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Disbursed</p>
                <p className="text-2xl font-bold">
                  {formatCurrency(
                    data.loans.reduce((sum, loan) => sum + Number(loan.disbursedAmount || 0), 0)
                  )}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Outstanding</p>
                <p className="text-2xl font-bold">
                  {formatCurrency(
                    data.loans.reduce((sum, loan) => 
                      sum + calculateOutstanding(Number(loan.totalAmount), loan.payments), 0
                    )
                  )}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Loans</p>
                <p className="text-2xl font-bold">
                  {data.loans.filter(loan => ['ACTIVE', 'DISBURSED'].includes(loan.status)).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Loans Table */}
      <Card>
        <CardHeader>
          <CardTitle>Loan History</CardTitle>
          <CardDescription>
            All loans for {data.customer.firstName} {data.customer.lastName}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {data.loans.length === 0 ? (
            <div className="text-center py-8">
              <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No loans found for this customer</p>
              <Button className="mt-4" asChild>
                <Link href={`/loans/new?customerId=${customerId}`}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Loan
                </Link>
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Loan Number</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Principal</TableHead>
                    <TableHead>Total Amount</TableHead>
                    <TableHead>Outstanding</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Application Date</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.loans.map((loan) => (
                    <TableRow key={loan.id}>
                      <TableCell className="font-medium">{loan.loanNumber}</TableCell>
                      <TableCell>{loan.loanType.name}</TableCell>
                      <TableCell>{formatCurrency(loan.principalAmount)}</TableCell>
                      <TableCell>{formatCurrency(loan.totalAmount)}</TableCell>
                      <TableCell>
                        {formatCurrency(calculateOutstanding(Number(loan.totalAmount), loan.payments))}
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(loan.status)}>
                          {loan.status.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {new Date(loan.applicationDate).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/loans/${loan.id}`}>
                            <Eye className="h-4 w-4 mr-2" />
                            View
                          </Link>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {data.pagination.pages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-700">
            Showing {((page - 1) * limit) + 1} to {Math.min(page * limit, data.pagination.total)} of{' '}
            {data.pagination.total} loans
          </p>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={page === data.pagination.pages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </PageHeader>
  )
}
