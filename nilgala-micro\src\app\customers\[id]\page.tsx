'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { useToast } from '@/hooks/use-toast'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { ArrowLeft, Edit, FileText, CreditCard, Settings, Shield } from 'lucide-react'
import Link from 'next/link'

interface Customer {
  id: string
  firstName: string
  lastName: string
  email?: string
  phone: string
  additionalPhones?: string[]
  nationalId: string
  dateOfBirth: string
  gender: string
  maritalStatus: string
  address: string
  city: string
  state: string
  postalCode?: string
  employmentType: string
  employer?: string
  monthlyIncome: number
  employmentDuration?: number
  bankAccount?: string
  bankName?: string
  status: string
  createdAt: string
  loans: any[]
  documents: any[]
  _count: {
    loans: number
    documents: number
  }
}

export default function CustomerDetailPage() {
  const params = useParams()
  const { data: session } = useSession()
  const { toast } = useToast()
  const [customer, setCustomer] = useState<Customer | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  // Status management state
  const [statusDialog, setStatusDialog] = useState(false)
  const [newStatus, setNewStatus] = useState('')
  const [statusNotes, setStatusNotes] = useState('')
  const [updatingStatus, setUpdatingStatus] = useState(false)

  useEffect(() => {
    const fetchCustomer = async () => {
      try {
        const response = await fetch(`/api/customers/${params.id}`)
        if (response.ok) {
          const data = await response.json()
          setCustomer(data)
        } else {
          setError('Customer not found')
        }
      } catch (error) {
        setError('Failed to load customer')
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchCustomer()
    }
  }, [params.id])

  const getStatusBadge = (status: string) => {
    const variants = {
      ACTIVE: 'default',
      INACTIVE: 'secondary',
      SUSPENDED: 'destructive',
      BLACKLISTED: 'destructive'
    } as const

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {status}
      </Badge>
    )
  }

  const canManageStatus = () => {
    return session?.user && ['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER'].includes(session.user.role)
  }

  const handleStatusChange = () => {
    if (!customer) return
    setNewStatus(customer.status)
    setStatusNotes('')
    setStatusDialog(true)
  }

  const updateCustomerStatus = async () => {
    if (!customer || !newStatus) return

    try {
      setUpdatingStatus(true)
      const response = await fetch(`/api/customers/${customer.id}/status`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          status: newStatus,
          notes: statusNotes
        })
      })

      if (response.ok) {
        const data = await response.json()
        setCustomer(prev => prev ? { ...prev, status: newStatus } : null)
        setStatusDialog(false)
        toast({
          title: 'Success',
          description: 'Customer status updated successfully'
        })
      } else {
        const error = await response.json()
        toast({
          title: 'Error',
          description: error.error || 'Failed to update customer status',
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error updating status:', error)
      toast({
        title: 'Error',
        description: 'Failed to update customer status',
        variant: 'destructive'
      })
    } finally {
      setUpdatingStatus(false)
    }
  }



  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">Loading customer details...</div>
      </div>
    )
  }

  if (error || !customer) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <p className="text-red-600">{error || 'Customer not found'}</p>
          <Link href="/customers">
            <Button className="mt-4">Back to Customers</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-4 sm:py-6 px-4 sm:px-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4 sm:mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
          <Link href="/customers">
            <Button variant="outline" size="sm" className="w-fit">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Customers
            </Button>
          </Link>
          <div>
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold">
              {customer.firstName} {customer.lastName}
            </h1>
            <p className="text-sm sm:text-base text-gray-600">Customer ID: {customer.id}</p>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
          <Link href={`/customers/${customer.id}/edit`}>
            <Button variant="outline" className="w-full sm:w-auto h-11 sm:h-10">
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          </Link>
          <Link href={`/loans/new?customerId=${customer.id}`}>
            <Button className="w-full sm:w-auto h-11 sm:h-10">
              <CreditCard className="h-4 w-4 mr-2" />
              New Loan
            </Button>
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
        {/* Personal Information */}
        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="pb-3 sm:pb-4">
            <CardTitle className="text-base sm:text-lg">Personal Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 sm:space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Full Name</label>
              <p>{customer.firstName} {customer.lastName}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">National ID</label>
              <p>{customer.nationalId}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Date of Birth</label>
              <p>{new Date(customer.dateOfBirth).toLocaleDateString()}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Gender</label>
              <p>{customer.gender}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Marital Status</label>
              <p>{customer.maritalStatus}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Status</label>
              <div className="flex items-center justify-between">
                {getStatusBadge(customer.status)}
                {canManageStatus() && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleStatusChange}
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Member Since</label>
              <p>{new Date(customer.createdAt).toLocaleDateString()}</p>
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <label className="text-sm font-medium text-gray-500">Phone Numbers</label>
              <div className="space-y-1">
                <p className="font-medium">{customer.phone} <span className="text-xs text-gray-500">(Primary)</span></p>
                {customer.additionalPhones && customer.additionalPhones.length > 0 && (
                  customer.additionalPhones.map((phone, index) => (
                    <p key={index} className="text-gray-700">{phone}</p>
                  ))
                )}
              </div>
            </div>
            {customer.email && (
              <div>
                <label className="text-sm font-medium text-gray-500">Email</label>
                <p>{customer.email}</p>
              </div>
            )}
            <div>
              <label className="text-sm font-medium text-gray-500">Address</label>
              <p>{customer.address}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">City</label>
              <p>{customer.city}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">State</label>
              <p>{customer.state}</p>
            </div>
            {customer.postalCode && (
              <div>
                <label className="text-sm font-medium text-gray-500">Postal Code</label>
                <p>{customer.postalCode}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Employment Information */}
        <Card>
          <CardHeader>
            <CardTitle>Employment Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <label className="text-sm font-medium text-gray-500">Employment Type</label>
              <p>{customer.employmentType}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Monthly Income</label>
              <p>LKR {customer.monthlyIncome.toLocaleString()}</p>
            </div>
            {customer.employer && (
              <div>
                <label className="text-sm font-medium text-gray-500">Employer</label>
                <p>{customer.employer}</p>
              </div>
            )}
            {customer.employmentDuration && (
              <div>
                <label className="text-sm font-medium text-gray-500">Employment Duration</label>
                <p>{customer.employmentDuration} months</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Banking Information */}
        <Card>
          <CardHeader>
            <CardTitle>Banking Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {customer.bankAccount && (
              <div>
                <label className="text-sm font-medium text-gray-500">Bank Account</label>
                <p>{customer.bankAccount}</p>
              </div>
            )}
            {customer.bankName && (
              <div>
                <label className="text-sm font-medium text-gray-500">Bank Name</label>
                <p>{customer.bankName}</p>
              </div>
            )}
            {!customer.bankAccount && !customer.bankName && (
              <p className="text-gray-500 text-sm">No banking information available</p>
            )}
          </CardContent>
        </Card>

        {/* Loans Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Loans Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <div className="text-2xl font-bold">{customer._count.loans}</div>
              <p className="text-sm text-gray-500">Total Loans</p>
            </div>
            {customer._count.loans > 0 && (
              <Button variant="outline" className="w-full mt-4" asChild>
                <Link href={`/customers/${customer.id}/loans`}>
                  <CreditCard className="h-4 w-4 mr-2" />
                  View All Loans
                </Link>
              </Button>
            )}
          </CardContent>
        </Card>

        {/* Documents Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Documents</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <div className="text-2xl font-bold">{customer._count.documents}</div>
              <p className="text-sm text-gray-500">Documents</p>
            </div>
            <Button variant="outline" className="w-full mt-4" asChild>
              <Link href={`/customers/${customer.id}/documents`}>
                <FileText className="h-4 w-4 mr-2" />
                Manage Documents
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Status Management Dialog */}
      <Dialog open={statusDialog} onOpenChange={setStatusDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Update Customer Status
            </DialogTitle>
            <DialogDescription>
              Change the status of {customer?.firstName} {customer?.lastName}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="status">New Status</Label>
              <Select value={newStatus} onValueChange={setNewStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="INACTIVE">Inactive</SelectItem>
                  <SelectItem value="ACTIVE" disabled={!customer?.loans?.some(loan => ['ACTIVE', 'DISBURSED'].includes(loan.status))}>
                    Active
                  </SelectItem>
                  <SelectItem value="SUSPENDED">Suspended</SelectItem>
                  <SelectItem value="BLACKLISTED">Blacklisted</SelectItem>
                </SelectContent>
              </Select>
              {newStatus === 'ACTIVE' && !customer?.loans?.some(loan => ['ACTIVE', 'DISBURSED'].includes(loan.status)) && (
                <p className="text-sm text-red-600 mt-1">
                  Customer must have active loans to be set to Active status
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="notes">Notes (Optional)</Label>
              <Textarea
                id="notes"
                value={statusNotes}
                onChange={(e) => setStatusNotes(e.target.value)}
                placeholder="Add notes about this status change..."
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setStatusDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={updateCustomerStatus}
              disabled={updatingStatus || !newStatus || newStatus === customer?.status}
            >
              {updatingStatus ? 'Updating...' : 'Update Status'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
