'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ArrowLeft, Save, Loader2, Users } from 'lucide-react'
import Link from 'next/link'

interface CustomerFormData {
  firstName: string
  lastName: string
  email: string
  phones: string[] // Changed from single phone to array of phones
  nationalId: string
  dateOfBirth: string
  gender: string
  maritalStatus: string
  address: string
  city: string
  state: string
  postalCode: string
  employmentType: string
  employer: string
  monthlyIncome: string
  employmentDuration: string
  bankAccount: string
  bankName: string
  assignedTo: string
}

interface CreditOfficer {
  id: string
  name: string
  email: string
  assignedCustomers: number
  workload: number
}

interface ValidationError {
  path: string[]
  message: string
  code: string
}

// Sri Lankan Provinces
const SRI_LANKAN_PROVINCES = [
  'Western',
  'Central',
  'Southern',
  'Northern',
  'Eastern',
  'North Western',
  'North Central',
  'Uva',
  'Sabaragamuwa'
]

export default function NewCustomerPage() {
  const router = useRouter()
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([])
  const [creditOfficers, setCreditOfficers] = useState<CreditOfficer[]>([])
  const [loadingOfficers, setLoadingOfficers] = useState(false)
  const [formData, setFormData] = useState<CustomerFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phones: ['', ''], // Initialize with 2 empty phone numbers (both required)
    nationalId: '',
    dateOfBirth: '',
    gender: '',
    maritalStatus: '',
    address: '',
    city: '',
    state: '',
    postalCode: '',
    employmentType: '',
    employer: '',
    monthlyIncome: '',
    employmentDuration: '',
    bankAccount: '',
    bankName: '',
    assignedTo: '',
  })

  // Fetch Credit Officers on component mount
  useEffect(() => {
    fetchCreditOfficers()

    // Auto-assign to current user if they are a Credit Officer
    if (session?.user?.role === 'CREDIT_OFFICER') {
      setFormData(prev => ({ ...prev, assignedTo: session.user.id }))
    }
  }, [session])

  const fetchCreditOfficers = async () => {
    try {
      setLoadingOfficers(true)
      const response = await fetch('/api/users/credit-officers')
      if (response.ok) {
        const data = await response.json()
        setCreditOfficers(data.creditOfficers)
      } else {
        console.error('Failed to fetch credit officers')
      }
    } catch (error) {
      console.error('Error fetching credit officers:', error)
    } finally {
      setLoadingOfficers(false)
    }
  }

  const handleInputChange = (field: keyof CustomerFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear validation errors for this field when user starts typing
    setValidationErrors(prev => prev.filter(error => error.path[0] !== field))
  }

  const handlePhoneChange = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      phones: prev.phones.map((phone, i) => i === index ? value : phone)
    }))
    // Clear validation errors for phones
    setValidationErrors(prev => prev.filter(error => !error.path[0].startsWith('phone')))
  }

  const addPhoneNumber = () => {
    if (formData.phones.length < 5) {
      setFormData(prev => ({
        ...prev,
        phones: [...prev.phones, '']
      }))
    }
  }

  const removePhoneNumber = (index: number) => {
    if (formData.phones.length > 2) { // Keep minimum 2 phone numbers
      setFormData(prev => ({
        ...prev,
        phones: prev.phones.filter((_, i) => i !== index)
      }))
    }
  }

  const getFieldError = (fieldName: string): string | undefined => {
    const error = validationErrors.find(error => error.path[0] === fieldName)
    return error?.message
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')
    setValidationErrors([])

    try {
      const submitData = {
        ...formData,
        phone: formData.phones[0], // Use first phone as primary phone
        additionalPhones: formData.phones.slice(1).filter(phone => phone.trim() !== ''), // Additional phones
        monthlyIncome: parseFloat(formData.monthlyIncome) || 0,
        employmentDuration: parseInt(formData.employmentDuration) || undefined,
        assignedTo: formData.assignedTo || undefined,
      }
      // Remove phones array from submitData since we're using phone and additionalPhones
      delete (submitData as any).phones

      const response = await fetch('/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (response.ok) {
        const customer = await response.json()
        router.push(`/customers/${customer.id}`)
      } else {
        const errorData = await response.json()

        if (errorData.details && Array.isArray(errorData.details)) {
          // Handle validation errors
          setValidationErrors(errorData.details)
          setError('Please fix the validation errors below.')
        } else {
          // Handle general errors
          setError(errorData.error || 'Failed to create customer')
        }
      }
    } catch (error) {
      setError('An error occurred. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-4 sm:py-6 px-4 sm:px-6">
      <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4 mb-4 sm:mb-6">
        <Link href="/customers">
          <Button variant="outline" size="sm" className="w-fit">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Customers
          </Button>
        </Link>
        <div>
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold">Add New Customer</h1>
          <p className="text-sm sm:text-base text-gray-600">Create a new customer profile</p>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
          {/* Personal Information */}
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3 sm:pb-4">
              <CardTitle className="text-base sm:text-lg">Personal Information</CardTitle>
              <CardDescription className="text-sm sm:text-base">Basic customer details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 sm:space-y-5">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="firstName" className="text-sm sm:text-base">First Name *</Label>
                  <Input
                    id="firstName"
                    value={formData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    required
                    className={`h-11 sm:h-10 ${getFieldError('firstName') ? 'border-red-500' : ''}`}
                  />
                  {getFieldError('firstName') && (
                    <p className="text-sm text-red-500 mt-1">{getFieldError('firstName')}</p>
                  )}
                </div>
                <div>
                  <Label htmlFor="lastName" className="text-sm sm:text-base">Last Name *</Label>
                  <Input
                    id="lastName"
                    value={formData.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    required
                    className={`h-11 sm:h-10 ${getFieldError('lastName') ? 'border-red-500' : ''}`}
                  />
                  {getFieldError('lastName') && (
                    <p className="text-sm text-red-500 mt-1">{getFieldError('lastName')}</p>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="nationalId">National ID Number *</Label>
                <Input
                  id="nationalId"
                  value={formData.nationalId}
                  onChange={(e) => handleInputChange('nationalId', e.target.value)}
                  placeholder="e.g., 199012345678"
                  required
                  className={getFieldError('nationalId') ? 'border-red-500' : ''}
                />
                {getFieldError('nationalId') && (
                  <p className="text-sm text-red-500 mt-1">{getFieldError('nationalId')}</p>
                )}
              </div>

              <div>
                <Label htmlFor="dateOfBirth">Date of Birth *</Label>
                <Input
                  id="dateOfBirth"
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                  required
                />
              </div>

              <div>
                <Label htmlFor="gender">Gender *</Label>
                <Select value={formData.gender} onValueChange={(value) => handleInputChange('gender', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="MALE">Male</SelectItem>
                    <SelectItem value="FEMALE">Female</SelectItem>
                    <SelectItem value="OTHER">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="maritalStatus">Marital Status *</Label>
                <Select value={formData.maritalStatus} onValueChange={(value) => handleInputChange('maritalStatus', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select marital status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="SINGLE">Single</SelectItem>
                    <SelectItem value="MARRIED">Married</SelectItem>
                    <SelectItem value="DIVORCED">Divorced</SelectItem>
                    <SelectItem value="WIDOWED">Widowed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle>
              <CardDescription>Phone, email, and address details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Phone Numbers *</Label>
                <p className="text-sm text-gray-600 mb-2">At least 2 phone numbers are required</p>
                {formData.phones.map((phone, index) => (
                  <div key={index} className="flex gap-2 mb-2">
                    <Input
                      value={phone}
                      onChange={(e) => handlePhoneChange(index, e.target.value)}
                      placeholder={`Phone ${index + 1} (e.g., 0771234567)`}
                      required={index < 2} // First 2 phones are required
                      className={getFieldError(`phone${index}`) ? 'border-red-500' : ''}
                    />
                    {formData.phones.length > 2 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removePhoneNumber(index)}
                        className="px-3"
                      >
                        ×
                      </Button>
                    )}
                  </div>
                ))}
                {formData.phones.length < 5 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addPhoneNumber}
                    className="mt-2"
                  >
                    + Add Phone Number
                  </Button>
                )}
                {(getFieldError('phone0') || getFieldError('phone1')) && (
                  <p className="text-sm text-red-500 mt-1">First 2 phone numbers are required</p>
                )}
              </div>

              <div>
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                  className={getFieldError('email') ? 'border-red-500' : ''}
                />
                {getFieldError('email') && (
                  <p className="text-sm text-red-500 mt-1">{getFieldError('email')}</p>
                )}
              </div>

              <div>
                <Label htmlFor="address">Address *</Label>
                <Input
                  id="address"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  placeholder="Street address"
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="city">City *</Label>
                  <Input
                    id="city"
                    value={formData.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="postalCode">Postal Code</Label>
                  <Input
                    id="postalCode"
                    value={formData.postalCode}
                    onChange={(e) => handleInputChange('postalCode', e.target.value)}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="state">Province *</Label>
                <Select value={formData.state} onValueChange={(value) => handleInputChange('state', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select province" />
                  </SelectTrigger>
                  <SelectContent>
                    {SRI_LANKAN_PROVINCES.map((province) => (
                      <SelectItem key={province} value={province}>
                        {province}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Employment Information */}
          <Card>
            <CardHeader>
              <CardTitle>Employment Information</CardTitle>
              <CardDescription>Employment and income details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="employmentType">Employment Type *</Label>
                <Select value={formData.employmentType} onValueChange={(value) => handleInputChange('employmentType', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select employment type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="EMPLOYED">Employed</SelectItem>
                    <SelectItem value="SELF_EMPLOYED">Self Employed</SelectItem>
                    <SelectItem value="UNEMPLOYED">Unemployed</SelectItem>
                    <SelectItem value="RETIRED">Retired</SelectItem>
                    <SelectItem value="STUDENT">Student</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="employer">Employer Name</Label>
                <Input
                  id="employer"
                  value={formData.employer}
                  onChange={(e) => handleInputChange('employer', e.target.value)}
                  placeholder="Company or organization name"
                />
              </div>

              <div>
                <Label htmlFor="monthlyIncome">Monthly Income (LKR) *</Label>
                <Input
                  id="monthlyIncome"
                  type="number"
                  value={formData.monthlyIncome}
                  onChange={(e) => handleInputChange('monthlyIncome', e.target.value)}
                  placeholder="50000"
                  required
                  className={getFieldError('monthlyIncome') ? 'border-red-500' : ''}
                />
                {getFieldError('monthlyIncome') && (
                  <p className="text-sm text-red-500 mt-1">{getFieldError('monthlyIncome')}</p>
                )}
              </div>

              <div>
                <Label htmlFor="employmentDuration">Employment Duration (months)</Label>
                <Input
                  id="employmentDuration"
                  type="number"
                  value={formData.employmentDuration}
                  onChange={(e) => handleInputChange('employmentDuration', e.target.value)}
                  placeholder="24"
                />
              </div>
            </CardContent>
          </Card>

          {/* Banking Information */}
          <Card>
            <CardHeader>
              <CardTitle>Banking Information</CardTitle>
              <CardDescription>Bank account details (optional)</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="bankName">Bank Name</Label>
                <Input
                  id="bankName"
                  value={formData.bankName}
                  onChange={(e) => handleInputChange('bankName', e.target.value)}
                  placeholder="e.g., Bank of Ceylon, Commercial Bank"
                />
              </div>

              <div>
                <Label htmlFor="bankAccount">Bank Account Number</Label>
                <Input
                  id="bankAccount"
                  value={formData.bankAccount}
                  onChange={(e) => handleInputChange('bankAccount', e.target.value)}
                  placeholder="**********"
                />
              </div>
            </CardContent>
          </Card>

          {/* Assignment Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Credit Officer Assignment
              </CardTitle>
              <CardDescription>Assign this customer to a Credit Officer</CardDescription>
            </CardHeader>
            <CardContent>
              <div>
                <Label htmlFor="assignedTo">Assigned Credit Officer</Label>
                <Select
                  value={formData.assignedTo}
                  onValueChange={(value) => handleInputChange('assignedTo', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={loadingOfficers ? "Loading officers..." : "Select Credit Officer"} />
                  </SelectTrigger>
                  <SelectContent>
                    {creditOfficers.map((officer) => (
                      <SelectItem key={officer.id} value={officer.id}>
                        <div className="flex flex-col">
                          <span>{officer.name}</span>
                          <span className="text-xs text-gray-500">
                            {officer.assignedCustomers} customers • {officer.email}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500 mt-1">
                  {session?.user?.role === 'CREDIT_OFFICER'
                    ? 'Defaults to you. You can assign to another Credit Officer if needed.'
                    : 'Select a Credit Officer to handle this customer\'s loans and services.'
                  }
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {error && (
          <Alert variant="destructive" className="mt-6">
            <AlertDescription>
              {error}
              {validationErrors.length > 0 && (
                <div className="mt-2">
                  <p className="font-medium">Please fix the following issues:</p>
                  <ul className="list-disc list-inside mt-1 space-y-1">
                    {validationErrors.map((error, index) => (
                      <li key={index} className="text-sm">
                        <span className="font-medium">{error.path[0]}:</span> {error.message}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </AlertDescription>
          </Alert>
        )}

        <div className="flex justify-end gap-4 mt-6">
          <Link href="/customers">
            <Button variant="outline" type="button">
              Cancel
            </Button>
          </Link>
          <Button type="submit" disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Create Customer
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
