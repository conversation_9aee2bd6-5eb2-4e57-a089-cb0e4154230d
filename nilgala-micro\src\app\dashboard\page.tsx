'use client'

import { useSession, signOut } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Loader2, LogOut, User } from 'lucide-react'
import { useState, useEffect } from 'react'
import Link from 'next/link'
import SuperAdminDashboard from '@/components/dashboards/SuperAdminDashboard'
import HigherManagementDashboard from '@/components/dashboards/HigherManagementDashboard'
import ManagerDashboard from '@/components/dashboards/ManagerDashboard'
import CreditOfficerDashboard from '@/components/dashboards/CreditOfficerDashboard'
import CustomerServiceDashboard from '@/components/dashboards/CustomerServiceDashboard'

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const [companySettings, setCompanySettings] = useState({
    systemTitle: 'Nilgala Micro',
    companyLogo: ''
  })

  useEffect(() => {
    fetchCompanySettings()
  }, [])

  const fetchCompanySettings = async () => {
    try {
      const response = await fetch('/api/company-settings')
      if (response.ok) {
        const data = await response.json()
        setCompanySettings({
          systemTitle: data.systemTitle || 'Nilgala Micro',
          companyLogo: data.companyLogo || ''
        })
      }
    } catch (error) {
      console.error('Error fetching company settings:', error)
    }
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p>Access denied. Please sign in.</p>
      </div>
    )
  }

  // Render role-specific dashboard
  const renderDashboard = () => {
    switch (session.user.role) {
      case 'SUPER_ADMIN':
        return <SuperAdminDashboard />
      case 'HIGHER_MANAGEMENT':
        return <HigherManagementDashboard />
      case 'MANAGER':
        return <ManagerDashboard />
      case 'CREDIT_OFFICER':
        return <CreditOfficerDashboard />
      case 'CUSTOMER_SERVICE_OFFICER':
        return <CustomerServiceDashboard />
      default:
        return <SuperAdminDashboard />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/dashboard" className="flex items-center space-x-2">
                {companySettings.companyLogo ? (
                  <img
                    src={companySettings.companyLogo}
                    alt="Company Logo"
                    className="h-8 object-contain"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none'
                    }}
                  />
                ) : null}
                {!companySettings.companyLogo && (
                  <h1 className="text-3xl font-bold text-gray-900">
                    {companySettings.systemTitle}
                  </h1>
                )}
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <User className="h-5 w-5 text-gray-500" />
                <span className="text-sm text-gray-700">
                  {session.user.firstName} {session.user.lastName}
                </span>
                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                  {session.user.role.replace('_', ' ')}
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => signOut({ callbackUrl: '/auth/signin' })}
              >
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {renderDashboard()}
        </div>
      </main>
    </div>
  )
}
