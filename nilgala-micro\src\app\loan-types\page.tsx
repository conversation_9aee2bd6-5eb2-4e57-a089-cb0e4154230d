'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Checkbox } from '@/components/ui/checkbox'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Plus, Edit, Trash2, X } from 'lucide-react'
import Link from 'next/link'
import PageHeader from '@/components/layout/PageHeader'

interface LoanType {
  id: string
  name: string
  description: string | null
  category: string
  minAmount: number
  maxAmount: number
  defaultInterestRate: number
  minInterestRate: number
  maxInterestRate: number
  defaultTenure: number
  minTenure: number
  maxTenure: number
  tenureUnit: string
  collectionType: string
  processingFeeRate: number
  gracePeriod: number
  isActive: boolean
  requiresGuarantor: boolean
  maxGuarantors: number
  requiredDocuments: string[]
  createdAt: string
  _count: {
    loans: number
  }
}

interface RequiredDocument {
  id: string
  name: string
  description: string | null
  isActive: boolean
}

export default function LoanTypesPage() {
  const [loanTypes, setLoanTypes] = useState<LoanType[]>([])
  const [requiredDocuments, setRequiredDocuments] = useState<RequiredDocument[]>([])
  const [loading, setLoading] = useState(true)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingLoanType, setEditingLoanType] = useState<LoanType | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    minAmount: '',
    maxAmount: '',
    defaultInterestRate: '',
    minInterestRate: '',
    maxInterestRate: '',
    defaultTenure: '',
    minTenure: '',
    maxTenure: '',
    tenureUnit: 'MONTHS',
    collectionType: 'MONTHLY',
    interestCalculationMethod: 'MONTHLY_INTEREST',
    processingFeeRate: '',
    insuranceFeeRate: '',
    gracePeriod: '',
    requiresGuarantor: false,
    maxGuarantors: '',
    eligibilityCriteria: '',
    requiredDocuments: [] as string[],
    isActive: true,
  })

  const fetchLoanTypes = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/loan-types', {
        headers: {
          'Cache-Control': 'no-cache',
        }
      })
      if (response.ok) {
        const data = await response.json()
        setLoanTypes(data)
      } else {
        console.error('Failed to fetch loan types:', response.status)
      }
    } catch (error) {
      console.error('Error fetching loan types:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchRequiredDocuments = async () => {
    try {
      const response = await fetch('/api/required-documents?active=true')
      if (response.ok) {
        const data = await response.json()
        setRequiredDocuments(data)
      } else {
        console.error('Failed to fetch required documents:', response.status)
      }
    } catch (error) {
      console.error('Error fetching required documents:', error)
    }
  }

  useEffect(() => {
    fetchLoanTypes()
    fetchRequiredDocuments()
  }, [])

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      minAmount: '',
      maxAmount: '',
      defaultInterestRate: '',
      minInterestRate: '',
      maxInterestRate: '',
      defaultTenure: '',
      minTenure: '',
      maxTenure: '',
      tenureUnit: 'MONTHS',
      collectionType: 'MONTHLY',
      interestCalculationMethod: 'MONTHLY_INTEREST',
      processingFeeRate: '',
      insuranceFeeRate: '',
      gracePeriod: '',
      requiresGuarantor: false,
      maxGuarantors: '',
      eligibilityCriteria: '',
      requiredDocuments: [],
      isActive: true,
    })
    setEditingLoanType(null)
  }

  // Helper function to convert tenure from days to display unit
  const convertTenureFromDays = (days: number, unit: string): number => {
    switch (unit) {
      case 'DAYS': return days
      case 'WEEKS': return Math.round(days / 7)
      case 'MONTHS': return Math.round(days / 30)
      case 'YEARS': return Math.round(days / 365)
      default: return days
    }
  }

  const openEditDialog = (loanType: LoanType) => {
    setEditingLoanType(loanType)
    setFormData({
      name: loanType.name,
      description: loanType.description || '',
      minAmount: loanType.minAmount.toString(),
      maxAmount: loanType.maxAmount.toString(),
      defaultInterestRate: loanType.defaultInterestRate.toString(),
      minInterestRate: loanType.minInterestRate.toString(),
      maxInterestRate: loanType.maxInterestRate.toString(),
      defaultTenure: convertTenureFromDays(loanType.defaultTenure, loanType.tenureUnit).toString(),
      minTenure: convertTenureFromDays(loanType.minTenure, loanType.tenureUnit).toString(),
      maxTenure: convertTenureFromDays(loanType.maxTenure, loanType.tenureUnit).toString(),
      tenureUnit: loanType.tenureUnit,
      collectionType: loanType.collectionType,
      interestCalculationMethod: loanType.interestCalculationMethod || 'MONTHLY_INTEREST',
      processingFeeRate: loanType.processingFeeRate.toString(),
      insuranceFeeRate: '0', // Default if not available
      gracePeriod: loanType.gracePeriod.toString(),
      requiresGuarantor: loanType.requiresGuarantor,
      maxGuarantors: loanType.maxGuarantors.toString(),
      eligibilityCriteria: '',
      requiredDocuments: loanType.requiredDocuments || [],
      isActive: loanType.isActive,
    })
    setDialogOpen(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      const submitData = {
        ...formData,
        minAmount: parseFloat(formData.minAmount),
        maxAmount: parseFloat(formData.maxAmount),
        defaultInterestRate: parseFloat(formData.defaultInterestRate),
        minInterestRate: parseFloat(formData.minInterestRate),
        maxInterestRate: parseFloat(formData.maxInterestRate),
        defaultTenure: parseInt(formData.defaultTenure),
        minTenure: parseInt(formData.minTenure),
        maxTenure: parseInt(formData.maxTenure),
        processingFeeRate: parseFloat(formData.processingFeeRate),
        insuranceFeeRate: parseFloat(formData.insuranceFeeRate || '0'),
        gracePeriod: parseInt(formData.gracePeriod || '0'),
        maxGuarantors: parseInt(formData.maxGuarantors || '0'),
      }

      const url = editingLoanType ? `/api/loan-types/${editingLoanType.id}` : '/api/loan-types'
      const method = editingLoanType ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (response.ok) {
        setDialogOpen(false)
        resetForm()
        fetchLoanTypes()
      } else {
        const error = await response.json()
        alert(error.error || `Failed to ${editingLoanType ? 'update' : 'create'} loan type`)
      }
    } catch (error) {
      alert('An error occurred. Please try again.')
    }
  }

  return (
    <PageHeader
      title="Loan Types"
      description="Manage available loan products"
      actions={
        <Dialog open={dialogOpen} onOpenChange={(open) => {
          setDialogOpen(open)
          if (!open) resetForm()
        }}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Loan Type
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>{editingLoanType ? 'Edit Loan Type' : 'Create New Loan Type'}</DialogTitle>
              <DialogDescription>
                {editingLoanType ? 'Update the loan product details.' : 'Define a new loan product with its terms and conditions.'}
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Loan Type Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="collectionType">Collection Schedule *</Label>
                  <Select value={formData.collectionType} onValueChange={(value) => setFormData(prev => ({ ...prev, collectionType: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select collection schedule" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="DAILY">Daily</SelectItem>
                      <SelectItem value="WEEKLY">Weekly</SelectItem>
                      <SelectItem value="MONTHLY">Monthly</SelectItem>
                      <SelectItem value="QUARTERLY">Quarterly</SelectItem>
                      <SelectItem value="YEARLY">Yearly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="interestCalculationMethod">Interest Calculation Method *</Label>
                  <Select value={formData.interestCalculationMethod} onValueChange={(value) => setFormData(prev => ({ ...prev, interestCalculationMethod: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select calculation method" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="MONTHLY_INTEREST">Monthly Interest (Simple)</SelectItem>
                      <SelectItem value="COMPOUND_INTEREST">Compound Interest (EMI)</SelectItem>
                    </SelectContent>
                  </Select>
                  <div className="text-xs text-gray-500 mt-1 space-y-1">
                    {formData.interestCalculationMethod === 'MONTHLY_INTEREST' ? (
                      <div>
                        <p><strong>Monthly Interest:</strong> Simple interest calculation</p>
                        <p>Formula: Principal × Rate × Months</p>
                        <p>Example: 100,000 × 5% × 2 months = 10,000 interest</p>
                      </div>
                    ) : (
                      <div>
                        <p><strong>Compound Interest:</strong> Standard EMI calculation</p>
                        <p>Formula: EMI = P × r × (1+r)^n / ((1+r)^n - 1)</p>
                        <p>Example: Standard loan EMI with reducing balance</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="minAmount">Minimum Amount (LKR) *</Label>
                  <Input
                    id="minAmount"
                    type="number"
                    value={formData.minAmount}
                    onChange={(e) => setFormData(prev => ({ ...prev, minAmount: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="maxAmount">Maximum Amount (LKR) *</Label>
                  <Input
                    id="maxAmount"
                    type="number"
                    value={formData.maxAmount}
                    onChange={(e) => setFormData(prev => ({ ...prev, maxAmount: e.target.value }))}
                    required
                  />
                </div>
              </div>

              {/* Interest Rate Range */}
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="minInterestRate">
                    Min Interest Rate (%) *
                    <span className="text-xs text-gray-500 block">
                      {formData.interestCalculationMethod === 'MONTHLY_INTEREST' ? 'Monthly rate' : 'Annual rate'}
                    </span>
                  </Label>
                  <Input
                    id="minInterestRate"
                    type="number"
                    step="0.1"
                    value={formData.minInterestRate}
                    onChange={(e) => setFormData(prev => ({ ...prev, minInterestRate: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="defaultInterestRate">
                    Default Interest Rate (%) *
                    <span className="text-xs text-gray-500 block">
                      {formData.interestCalculationMethod === 'MONTHLY_INTEREST' ? 'Monthly rate' : 'Annual rate'}
                    </span>
                  </Label>
                  <Input
                    id="defaultInterestRate"
                    type="number"
                    step="0.1"
                    value={formData.defaultInterestRate}
                    onChange={(e) => setFormData(prev => ({ ...prev, defaultInterestRate: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="maxInterestRate">
                    Max Interest Rate (%) *
                    <span className="text-xs text-gray-500 block">
                      {formData.interestCalculationMethod === 'MONTHLY_INTEREST' ? 'Monthly rate' : 'Annual rate'}
                    </span>
                  </Label>
                  <Input
                    id="maxInterestRate"
                    type="number"
                    step="0.1"
                    value={formData.maxInterestRate}
                    onChange={(e) => setFormData(prev => ({ ...prev, maxInterestRate: e.target.value }))}
                    required
                  />
                </div>
              </div>

              {/* Tenure Range */}
              <div className="grid grid-cols-4 gap-4">
                <div>
                  <Label htmlFor="minTenure">Min Tenure *</Label>
                  <Input
                    id="minTenure"
                    type="number"
                    value={formData.minTenure}
                    onChange={(e) => setFormData(prev => ({ ...prev, minTenure: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="defaultTenure">Default Tenure *</Label>
                  <Input
                    id="defaultTenure"
                    type="number"
                    value={formData.defaultTenure}
                    onChange={(e) => setFormData(prev => ({ ...prev, defaultTenure: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="maxTenure">Max Tenure *</Label>
                  <Input
                    id="maxTenure"
                    type="number"
                    value={formData.maxTenure}
                    onChange={(e) => setFormData(prev => ({ ...prev, maxTenure: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="tenureUnit">Tenure Unit *</Label>
                  <Select value={formData.tenureUnit} onValueChange={(value) => setFormData(prev => ({ ...prev, tenureUnit: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select unit" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="DAYS">Days</SelectItem>
                      <SelectItem value="WEEKS">Weeks</SelectItem>
                      <SelectItem value="MONTHS">Months</SelectItem>
                      <SelectItem value="YEARS">Years</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Fees */}
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="processingFeeRate">Processing Fee Rate (%) *</Label>
                  <Input
                    id="processingFeeRate"
                    type="number"
                    step="0.1"
                    value={formData.processingFeeRate}
                    onChange={(e) => setFormData(prev => ({ ...prev, processingFeeRate: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="insuranceFeeRate">Insurance Fee Rate (%)</Label>
                  <Input
                    id="insuranceFeeRate"
                    type="number"
                    step="0.1"
                    value={formData.insuranceFeeRate}
                    onChange={(e) => setFormData(prev => ({ ...prev, insuranceFeeRate: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="gracePeriod">Grace Period (Days)</Label>
                  <Input
                    id="gracePeriod"
                    type="number"
                    value={formData.gracePeriod}
                    onChange={(e) => setFormData(prev => ({ ...prev, gracePeriod: e.target.value }))}
                  />
                </div>
              </div>

              {/* Guarantor Settings */}
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="requiresGuarantor"
                    checked={formData.requiresGuarantor}
                    onCheckedChange={(checked) =>
                      setFormData(prev => ({ ...prev, requiresGuarantor: !!checked }))
                    }
                  />
                  <Label htmlFor="requiresGuarantor">Requires Guarantor</Label>
                </div>
                {formData.requiresGuarantor && (
                  <div>
                    <Label htmlFor="maxGuarantors">Max Guarantors</Label>
                    <Input
                      id="maxGuarantors"
                      type="number"
                      min="1"
                      value={formData.maxGuarantors}
                      onChange={(e) => setFormData(prev => ({ ...prev, maxGuarantors: e.target.value }))}
                    />
                  </div>
                )}
              </div>

              <div>
                <Label htmlFor="eligibilityCriteria">Eligibility Criteria</Label>
                <Textarea
                  id="eligibilityCriteria"
                  value={formData.eligibilityCriteria}
                  onChange={(e) => setFormData(prev => ({ ...prev, eligibilityCriteria: e.target.value }))}
                  placeholder="e.g., Minimum monthly income of LKR 25,000"
                />
              </div>

              {/* Required Documents */}
              <div>
                <Label>Required Documents</Label>
                <div className="grid grid-cols-2 gap-2 mt-2 max-h-32 overflow-y-auto border rounded p-2">
                  {requiredDocuments.map((doc) => (
                    <div key={doc.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`doc-${doc.id}`}
                        checked={formData.requiredDocuments.includes(doc.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setFormData(prev => ({
                              ...prev,
                              requiredDocuments: [...prev.requiredDocuments, doc.id]
                            }))
                          } else {
                            setFormData(prev => ({
                              ...prev,
                              requiredDocuments: prev.requiredDocuments.filter(id => id !== doc.id)
                            }))
                          }
                        }}
                      />
                      <Label htmlFor={`doc-${doc.id}`} className="text-sm">{doc.name}</Label>
                    </div>
                  ))}
                </div>
                {requiredDocuments.length === 0 && (
                  <p className="text-sm text-gray-500 mt-2">No required documents configured. Contact admin to add documents.</p>
                )}
              </div>

              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => {
                  setDialogOpen(false)
                  resetForm()
                }}>
                  Cancel
                </Button>
                <Button type="submit">
                  {editingLoanType ? 'Update Loan Type' : 'Create Loan Type'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      }
    >
      <Card>
        <CardHeader>
          <CardTitle>Available Loan Types</CardTitle>
          <CardDescription>
            {loanTypes.length} loan types configured
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">Loading loan types...</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Amount Range</TableHead>
                  <TableHead>Interest Rate</TableHead>
                  <TableHead>Collection</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Loans</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loanTypes.map((loanType) => (
                  <TableRow key={loanType.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{loanType.name}</div>
                        {loanType.description && (
                          <div className="text-sm text-gray-500">{loanType.description}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{loanType.category}</Badge>
                    </TableCell>
                    <TableCell>
                      LKR {loanType.minAmount.toLocaleString()} - {loanType.maxAmount.toLocaleString()}
                    </TableCell>
                    <TableCell>{loanType.minInterestRate}% - {loanType.maxInterestRate}%</TableCell>
                    <TableCell>
                      <Badge variant="outline">{loanType.collectionType}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={loanType.isActive ? 'default' : 'secondary'}>
                        {loanType.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {loanType._count.loans} loans
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openEditDialog(loanType)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </PageHeader>
  )
}
