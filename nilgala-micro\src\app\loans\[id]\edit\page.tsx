'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { ArrowLeft, Save, Loader2, AlertTriangle } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { formatCurrency } from '@/lib/utils'
import Link from 'next/link'

interface LoanData {
  id: string
  loanNumber: string
  principalAmount: number
  interestRate: number
  tenure: number
  repaymentFrequency: string
  gracePeriod: number
  processingFee: number
  insuranceFee: number
  otherCharges: number
  purpose: string
  collateralDescription?: string
  notes?: string
  status: string
  customer: {
    id: string
    firstName: string
    lastName: string
    phone: string
  }
  loanType: {
    id: string
    name: string
    interestCalculationMethod: string
    tenureUnit: string
  }
}

export default function EditLoanPage() {
  const router = useRouter()
  const params = useParams()
  const { data: session } = useSession()
  const { toast } = useToast()
  const [loan, setLoan] = useState<LoanData | null>(null)
  const [loanTypes, setLoanTypes] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [formData, setFormData] = useState({
    loanTypeId: '',
    principalAmount: '',
    interestRate: '',
    tenure: '',
    repaymentFrequency: '',
    gracePeriod: '',
    processingFee: '',
    insuranceFee: '',
    otherCharges: '',
    purpose: '',
    collateralDescription: '',
    notes: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    fetchLoan()
    fetchLoanTypes()
  }, [params.id])

  const fetchLoan = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/loans/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setLoan(data)
        
        // Check if loan can be edited
        if (data.status !== 'PENDING_MORE_INFO') {
          toast({
            title: "Cannot Edit Loan",
            description: "This loan can only be edited when status is 'Pending More Info'",
            variant: "destructive"
          })
          router.push(`/loans/${params.id}`)
          return
        }

        // Populate form data
        setFormData({
          loanTypeId: data.loanType.id,
          principalAmount: data.principalAmount.toString(),
          interestRate: data.interestRate.toString(),
          tenure: data.tenure.toString(),
          repaymentFrequency: data.repaymentFrequency,
          gracePeriod: data.gracePeriod.toString(),
          processingFee: data.processingFee.toString(),
          insuranceFee: data.insuranceFee.toString(),
          otherCharges: data.otherCharges.toString(),
          purpose: data.purpose || '',
          collateralDescription: data.collateralDescription || '',
          notes: data.notes || ''
        })
      } else {
        toast({
          title: "Error",
          description: "Failed to fetch loan details",
          variant: "destructive"
        })
        router.push('/loans')
      }
    } catch (error) {
      console.error('Error fetching loan:', error)
      toast({
        title: "Error",
        description: "Failed to fetch loan details",
        variant: "destructive"
      })
      router.push('/loans')
    } finally {
      setLoading(false)
    }
  }

  const fetchLoanTypes = async () => {
    try {
      const response = await fetch('/api/loan-types')
      if (response.ok) {
        const data = await response.json()
        setLoanTypes(data)
      }
    } catch (error) {
      console.error('Error fetching loan types:', error)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.loanTypeId) {
      newErrors.loanTypeId = 'Loan type is required'
    }

    if (!formData.principalAmount || Number(formData.principalAmount) <= 0) {
      newErrors.principalAmount = 'Principal amount is required and must be positive'
    }

    if (!formData.interestRate || Number(formData.interestRate) < 0) {
      newErrors.interestRate = 'Interest rate is required and cannot be negative'
    }

    if (!formData.tenure || Number(formData.tenure) <= 0) {
      newErrors.tenure = 'Tenure is required and must be positive'
    }

    if (!formData.repaymentFrequency) {
      newErrors.repaymentFrequency = 'Repayment frequency is required'
    }

    if (Number(formData.gracePeriod) < 0) {
      newErrors.gracePeriod = 'Grace period cannot be negative'
    }

    if (Number(formData.processingFee) < 0) {
      newErrors.processingFee = 'Processing fee cannot be negative'
    }

    if (Number(formData.insuranceFee) < 0) {
      newErrors.insuranceFee = 'Insurance fee cannot be negative'
    }

    if (Number(formData.otherCharges) < 0) {
      newErrors.otherCharges = 'Other charges cannot be negative'
    }

    if (!formData.purpose.trim()) {
      newErrors.purpose = 'Loan purpose is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    try {
      setSaving(true)
      
      const response = await fetch(`/api/loans/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          loanTypeId: formData.loanTypeId,
          principalAmount: Number(formData.principalAmount),
          interestRate: Number(formData.interestRate),
          tenure: Number(formData.tenure),
          repaymentFrequency: formData.repaymentFrequency,
          gracePeriod: Number(formData.gracePeriod),
          processingFee: Number(formData.processingFee),
          insuranceFee: Number(formData.insuranceFee),
          otherCharges: Number(formData.otherCharges),
          purpose: formData.purpose,
          collateralDescription: formData.collateralDescription,
          notes: formData.notes
        }),
      })

      if (response.ok) {
        toast({
          title: "Success",
          description: "Loan updated successfully",
        })
        router.push(`/loans/${params.id}`)
      } else {
        const errorData = await response.json()
        toast({
          title: "Error",
          description: errorData.error || "Failed to update loan",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('Error updating loan:', error)
      toast({
        title: "Error",
        description: "Failed to update loan",
        variant: "destructive"
      })
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!loan) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Loan Not Found</h2>
          <p className="text-gray-600 mb-4">The requested loan could not be found.</p>
          <Link href="/loans">
            <Button>Back to Loans</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center gap-4 mb-6">
        <Link href={`/loans/${loan.id}`}>
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Loan
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold">Edit Loan {loan.loanNumber}</h1>
          <p className="text-gray-600">
            Customer: {loan.customer.firstName} {loan.customer.lastName} - {loan.customer.phone}
          </p>
        </div>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <div className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-yellow-600" />
          <div>
            <h3 className="font-medium text-yellow-800">Editing Loan with Pending More Info Status</h3>
            <p className="text-sm text-yellow-700">
              This loan is in "Pending More Info" status. Make the necessary changes and resubmit for approval.
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Loan Details */}
        <Card>
          <CardHeader>
            <CardTitle>Loan Details</CardTitle>
            <CardDescription>Update the loan information as needed</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Loan Type Selection */}
            <div>
              <Label htmlFor="loanTypeId">Loan Type *</Label>
              <Select value={formData.loanTypeId} onValueChange={(value) => handleInputChange('loanTypeId', value)}>
                <SelectTrigger className={errors.loanTypeId ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select loan type" />
                </SelectTrigger>
                <SelectContent>
                  {loanTypes.map((type) => (
                    <SelectItem key={type.id} value={type.id}>
                      {type.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.loanTypeId && <p className="text-red-500 text-sm mt-1">{errors.loanTypeId}</p>}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="principalAmount">Principal Amount (LKR) *</Label>
                <Input
                  id="principalAmount"
                  type="number"
                  step="0.01"
                  value={formData.principalAmount}
                  onChange={(e) => handleInputChange('principalAmount', e.target.value)}
                  className={errors.principalAmount ? 'border-red-500' : ''}
                />
                {errors.principalAmount && <p className="text-red-500 text-sm mt-1">{errors.principalAmount}</p>}
              </div>
              
              <div>
                <Label htmlFor="interestRate">Interest Rate (%) *</Label>
                <Input
                  id="interestRate"
                  type="number"
                  step="0.01"
                  value={formData.interestRate}
                  onChange={(e) => handleInputChange('interestRate', e.target.value)}
                  className={errors.interestRate ? 'border-red-500' : ''}
                />
                {errors.interestRate && <p className="text-red-500 text-sm mt-1">{errors.interestRate}</p>}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="tenure">Tenure ({loan.loanType.tenureUnit.toLowerCase()}) *</Label>
                <Input
                  id="tenure"
                  type="number"
                  value={formData.tenure}
                  onChange={(e) => handleInputChange('tenure', e.target.value)}
                  className={errors.tenure ? 'border-red-500' : ''}
                />
                {errors.tenure && <p className="text-red-500 text-sm mt-1">{errors.tenure}</p>}
              </div>
              
              <div>
                <Label htmlFor="repaymentFrequency">Repayment Frequency *</Label>
                <Select value={formData.repaymentFrequency} onValueChange={(value) => handleInputChange('repaymentFrequency', value)}>
                  <SelectTrigger className={errors.repaymentFrequency ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="DAILY">Daily</SelectItem>
                    <SelectItem value="WEEKLY">Weekly</SelectItem>
                    <SelectItem value="MONTHLY">Monthly</SelectItem>
                    <SelectItem value="QUARTERLY">Quarterly</SelectItem>
                    <SelectItem value="YEARLY">Yearly</SelectItem>
                  </SelectContent>
                </Select>
                {errors.repaymentFrequency && <p className="text-red-500 text-sm mt-1">{errors.repaymentFrequency}</p>}
              </div>
            </div>

            {/* Fees and Charges */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="gracePeriod">Grace Period (days)</Label>
                <Input
                  id="gracePeriod"
                  type="number"
                  min="0"
                  value={formData.gracePeriod}
                  onChange={(e) => handleInputChange('gracePeriod', e.target.value)}
                  className={errors.gracePeriod ? 'border-red-500' : ''}
                />
                {errors.gracePeriod && <p className="text-red-500 text-sm mt-1">{errors.gracePeriod}</p>}
              </div>

              <div>
                <Label htmlFor="processingFee">Processing Fee (LKR)</Label>
                <Input
                  id="processingFee"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.processingFee}
                  onChange={(e) => handleInputChange('processingFee', e.target.value)}
                  className={errors.processingFee ? 'border-red-500' : ''}
                />
                {errors.processingFee && <p className="text-red-500 text-sm mt-1">{errors.processingFee}</p>}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="insuranceFee">Insurance Fee (LKR)</Label>
                <Input
                  id="insuranceFee"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.insuranceFee}
                  onChange={(e) => handleInputChange('insuranceFee', e.target.value)}
                  className={errors.insuranceFee ? 'border-red-500' : ''}
                />
                {errors.insuranceFee && <p className="text-red-500 text-sm mt-1">{errors.insuranceFee}</p>}
              </div>

              <div>
                <Label htmlFor="otherCharges">Other Charges (LKR)</Label>
                <Input
                  id="otherCharges"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.otherCharges}
                  onChange={(e) => handleInputChange('otherCharges', e.target.value)}
                  className={errors.otherCharges ? 'border-red-500' : ''}
                />
                {errors.otherCharges && <p className="text-red-500 text-sm mt-1">{errors.otherCharges}</p>}
              </div>
            </div>

            <div>
              <Label htmlFor="purpose">Loan Purpose *</Label>
              <Textarea
                id="purpose"
                value={formData.purpose}
                onChange={(e) => handleInputChange('purpose', e.target.value)}
                className={errors.purpose ? 'border-red-500' : ''}
                rows={3}
              />
              {errors.purpose && <p className="text-red-500 text-sm mt-1">{errors.purpose}</p>}
            </div>

            <div>
              <Label htmlFor="collateralDescription">Collateral Description</Label>
              <Textarea
                id="collateralDescription"
                value={formData.collateralDescription}
                onChange={(e) => handleInputChange('collateralDescription', e.target.value)}
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="notes">Additional Notes</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex gap-4">
          <Button type="submit" disabled={saving}>
            {saving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
          <Link href={`/loans/${loan.id}`}>
            <Button type="button" variant="outline">
              Cancel
            </Button>
          </Link>
        </div>
      </form>
    </div>
  )
}
