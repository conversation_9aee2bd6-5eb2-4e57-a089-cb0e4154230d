'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useToast } from '@/hooks/use-toast'
import { usePermissions } from '@/hooks/usePermissions'
import LoanComments from '@/components/loans/LoanComments'
import LoanActivityLog from '@/components/loan-activity-log'
import {
  ArrowLeft,
  User,
  DollarSign,
  Calendar,
  FileText,
  Shield,
  CreditCard,
  Edit,
  Download,
  Eye,
  Plus,
  CheckCircle,
  XCircle,
  AlertCircle,
  Banknote
} from 'lucide-react'
import Link from 'next/link'
import PageHeader from '@/components/layout/PageHeader'
import { formatCurrency } from '@/lib/utils'

interface LoanDetails {
  id: string
  loanNumber: string
  principalAmount: number | string
  interestRate: number | string
  tenure: number | string
  repaymentFrequency: string
  status: string
  createdAt: string
  disbursementDate: string
  summary: {
    totalPaid: number
    outstandingAmount: number
    paymentProgress: number
  }
  customer: {
    id: string
    firstName: string
    lastName: string
    phone: string
    email: string
    nationalId: string
    address: string
    monthlyIncome: number | string
    employmentType: string
    employer: string
  }
  loanType: {
    name: string
    interestRate: number | string
    maxAmount: number | string
    minAmount: number | string
    tenureUnit: string
  }
  payments: Array<{
    id: string
    amount: number | string
    paymentDate: string
    paymentMethod: string
    referenceNumber: string
    status: string
    createdByUser?: {
      id: string
      firstName: string
      lastName: string
      role: string
    }
  }>
  schedules: Array<{
    id: string
    installmentNumber: number | string
    dueDate: string
    principalAmount: number | string
    interestAmount: number | string
    totalAmount: number | string
    paidAmount: number | string
    status: string
  }>
  guarantors: Array<{
    id: string
    guarantorType: string
    liabilityAmount: number | string
    guarantorStatus: string
    customer?: {
      id: string
      firstName: string
      lastName: string
      phone: string
    }
    guarantor?: {
      id: string
      firstName: string
      lastName: string
      phone: string
      relationship: string
    }
  }>
  documents: Array<{
    id: string
    fileName: string
    originalName: string
    documentType: string
    fileSize: number
    mimeType: string
    storageKey: string
    storageUrl?: string
    uploadedAt: string
    createdAt: string
    isVerified: boolean
  }>
}

export default function LoanDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const { data: session } = useSession()
  const { toast } = useToast()
  const { hasPermission } = usePermissions()
  const [loan, setLoan] = useState<LoanDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [scheduleLoading, setScheduleLoading] = useState(false)

  // Approval dialog state
  const [approvalDialog, setApprovalDialog] = useState<{
    isOpen: boolean
    action: 'APPROVE' | 'REJECT' | 'REQUEST_MORE_INFO' | null
  }>({
    isOpen: false,
    action: null
  })
  const [notes, setNotes] = useState('')
  const [requestedInfo, setRequestedInfo] = useState('')
  const [processing, setProcessing] = useState(false)

  // Disbursement dialog state
  const [disbursementDialog, setDisbursementDialog] = useState(false)
  const [disbursementData, setDisbursementData] = useState({
    disbursedAmount: 0,
    disbursementMethod: '',
    disbursementReference: '',
    disbursementNotes: '',
    disbursementDate: new Date().toISOString().split('T')[0]
  })
  const [disbursing, setDisbursing] = useState(false)

  useEffect(() => {
    if (params.id) {
      fetchLoanDetails(params.id as string)
    }
  }, [params.id])

  const fetchLoanDetails = async (loanId: string) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/loans/${loanId}`)
      if (response.ok) {
        const data = await response.json()

        // Fetch payment schedule separately
        const scheduleResponse = await fetch(`/api/loans/${loanId}/schedule`)
        if (scheduleResponse.ok) {
          const scheduleData = await scheduleResponse.json()
          data.schedules = scheduleData
        }

        setLoan(data)
      } else {
        toast({
          title: 'Error',
          description: 'Failed to fetch loan details',
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error fetching loan details:', error)
      toast({
        title: 'Error',
        description: 'Failed to fetch loan details',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }



  const handleApprovalAction = (action: 'APPROVE' | 'REJECT' | 'REQUEST_MORE_INFO') => {
    setApprovalDialog({
      isOpen: true,
      action
    })
    setNotes('')
    setRequestedInfo('')
  }

  const processApproval = async () => {
    if (!loan || !approvalDialog.action) return

    try {
      setProcessing(true)
      const response = await fetch(`/api/loans/${loan.id}/approve`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: approvalDialog.action,
          notes,
          ...(approvalDialog.action === 'REQUEST_MORE_INFO' && { requestedInfo })
        })
      })

      if (response.ok) {
        const data = await response.json()
        toast({
          title: 'Success',
          description: data.message
        })

        // Refresh loan details to show updated status
        await fetchLoanDetails(loan.id)
        setApprovalDialog({ isOpen: false, action: null })
      } else {
        const error = await response.json()
        toast({
          title: 'Error',
          description: error.error || 'Failed to process approval',
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error processing approval:', error)
      toast({
        title: 'Error',
        description: 'Failed to process approval',
        variant: 'destructive'
      })
    } finally {
      setProcessing(false)
    }
  }

  const getActionButtonProps = (action: string) => {
    switch (action) {
      case 'APPROVE':
        return { variant: 'default' as const, text: 'Approve' }
      case 'REJECT':
        return { variant: 'destructive' as const, text: 'Reject' }
      case 'REQUEST_MORE_INFO':
        return { variant: 'outline' as const, text: 'Request Info' }
      default:
        return { variant: 'default' as const, text: 'Confirm' }
    }
  }

  // Generate disbursement reference number based on method
  const generateDisbursementReference = (method: string) => {
    const date = new Date()
    const year = date.getFullYear().toString().slice(-2)
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const time = date.getTime().toString().slice(-6)

    const prefixes = {
      'CASH': 'CSH',
      'BANK_TRANSFER': 'BT',
      'CHEQUE': 'CHQ',
      'ONLINE': 'ONL',
      'MOBILE_PAYMENT': 'MP'
    }

    const prefix = prefixes[method as keyof typeof prefixes] || 'DSB'
    return `${prefix}${year}${month}${day}${time}`
  }

  const handleDisbursement = () => {
    if (loan) {
      setDisbursementData(prev => ({
        ...prev,
        disbursedAmount: Number(loan.principalAmount)
      }))
      setDisbursementDialog(true)
    }
  }

  // Handle disbursement method change and auto-generate reference
  const handleDisbursementMethodChange = (method: string) => {
    const newReference = generateDisbursementReference(method)
    setDisbursementData(prev => ({
      ...prev,
      disbursementMethod: method,
      disbursementReference: newReference
    }))
  }

  const processDisbursement = async () => {
    if (!loan) return

    try {
      setDisbursing(true)
      const response = await fetch(`/api/loans/${loan.id}/disburse`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(disbursementData)
      })

      if (response.ok) {
        const data = await response.json()
        toast({
          title: 'Success',
          description: data.message
        })

        // Refresh loan details to show updated status
        await fetchLoanDetails(loan.id)
        setDisbursementDialog(false)
      } else {
        const error = await response.json()
        toast({
          title: 'Error',
          description: error.error || 'Failed to disburse loan',
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error disbursing loan:', error)
      toast({
        title: 'Error',
        description: 'Failed to disburse loan',
        variant: 'destructive'
      })
    } finally {
      setDisbursing(false)
    }
  }

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'DRAFT': return 'bg-gray-100 text-gray-800'
      case 'PENDING': return 'bg-yellow-100 text-yellow-800'
      case 'APPROVED': return 'bg-blue-100 text-blue-800'
      case 'DISBURSED': return 'bg-green-100 text-green-800'
      case 'ACTIVE': return 'bg-green-100 text-green-800'
      case 'COMPLETED': return 'bg-blue-100 text-blue-800'
      case 'OVERDUE': return 'bg-red-100 text-red-800'
      case 'DEFAULTED': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'bg-green-100 text-green-800'
      case 'PENDING': return 'bg-yellow-100 text-yellow-800'
      case 'FAILED': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getScheduleStatusColor = (status: string) => {
    switch (status) {
      case 'PAID': return 'bg-green-100 text-green-800'
      case 'PARTIAL': return 'bg-yellow-100 text-yellow-800'
      case 'PENDING': return 'bg-gray-100 text-gray-800'
      case 'OVERDUE': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const calculateTotalPaid = () => {
    if (!loan?.summary) return 0
    return loan.summary.totalPaid
  }

  const calculateOutstanding = () => {
    if (!loan?.summary) return 0
    return loan.summary.outstandingAmount
  }

  // Helper function to convert tenure from days to display unit
  const convertTenureFromDays = (days: number, unit: string): number => {
    switch (unit) {
      case 'DAYS': return days
      case 'WEEKS': return Math.round(days / 7)
      case 'MONTHS': return Math.round(days / 30)
      case 'YEARS': return Math.round(days / 365)
      default: return days
    }
  }

  // Helper function to get tenure display text
  const getTenureDisplayText = () => {
    if (!loan?.loanType?.tenureUnit) return `${loan?.tenure} months`

    // The loan.tenure is stored in the original unit (not converted to days)
    // So we can display it directly with the unit
    const unit = loan.loanType.tenureUnit.toLowerCase()
    return `${loan.tenure} ${unit}`
  }

  const handleViewDocument = (document: any) => {
    // For now, just open in a new tab using the storage URL or create a preview
    if (document.storageUrl) {
      window.open(document.storageUrl, '_blank')
    } else {
      // Check if it's a guarantor document or regular document
      const endpoint = document.isGuarantorDocument
        ? `/api/guarantor-documents/${document.id}/view`
        : `/api/documents/${document.id}/view`
      window.open(endpoint, '_blank')
    }
  }

  const handleDownloadDocument = async (document: any) => {
    try {
      const endpoint = document.isGuarantorDocument
        ? `/api/guarantor-documents/${document.id}/download`
        : `/api/documents/${document.id}/download`
      const response = await fetch(endpoint)
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = url
        a.download = document.originalName || document.fileName
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        toast({
          title: "Error",
          description: "Failed to download document",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Error downloading document:', error)
      toast({
        title: "Error",
        description: "Failed to download document",
        variant: "destructive",
      })
    }
  }

  if (loading) {
    return (
      <PageHeader title="Loading..." description="Please wait while we fetch loan details">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
        </div>
      </PageHeader>
    )
  }

  if (!loan) {
    return (
      <PageHeader title="Loan Not Found" description="The requested loan could not be found">
        <div className="text-center py-8">
          <p className="text-gray-500 mb-4">The loan you're looking for doesn't exist or you don't have permission to view it.</p>
          <Link href="/loans">
            <Button>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Loans
            </Button>
          </Link>
        </div>
      </PageHeader>
    )
  }

  return (
    <PageHeader
      title={`Loan ${loan.loanNumber}`}
      description={`${loan.customer.firstName} ${loan.customer.lastName} - ${loan.loanType.name}`}
      actions={
        <div className="flex gap-2">
          {loan.status === 'PENDING_APPROVAL' && hasPermission('loans:approve') && (
            <>
              <Button
                variant="default"
                onClick={() => handleApprovalAction('APPROVE')}
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Approve
              </Button>
              <Button
                variant="destructive"
                onClick={() => handleApprovalAction('REJECT')}
              >
                <XCircle className="h-4 w-4 mr-2" />
                Reject
              </Button>
              <Button
                variant="outline"
                onClick={() => handleApprovalAction('REQUEST_MORE_INFO')}
              >
                <AlertCircle className="h-4 w-4 mr-2" />
                Request Info
              </Button>
            </>
          )}
          {loan.status === 'PENDING_MORE_INFO' && hasPermission('loans:approve') && (
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-2 px-3 py-2 bg-yellow-50 border border-yellow-200 rounded-md">
                <AlertCircle className="h-4 w-4 text-yellow-600" />
                <span className="text-sm text-yellow-800 font-medium">
                  More information requested
                </span>
              </div>
              <Button
                variant="default"
                onClick={() => handleApprovalAction('APPROVE')}
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Approve
              </Button>
              <Button
                variant="destructive"
                onClick={() => handleApprovalAction('REJECT')}
              >
                <XCircle className="h-4 w-4 mr-2" />
                Reject
              </Button>
            </div>
          )}
          {loan.status === 'APPROVED' && (
            <Button
              variant="default"
              onClick={handleDisbursement}
            >
              <Banknote className="h-4 w-4 mr-2" />
              Disburse Loan
            </Button>
          )}
          {loan.status === 'PENDING_MORE_INFO' && (
            <Link href={`/loans/${loan.id}/edit`}>
              <Button variant="outline">
                <Edit className="h-4 w-4 mr-2" />
                Edit Loan
              </Button>
            </Link>
          )}
          {loan.status !== 'PENDING_APPROVAL' && loan.status !== 'APPROVED' && loan.status !== 'PENDING_MORE_INFO' && (
            <>
              {(loan.status === 'ACTIVE' || loan.status === 'DISBURSED') && (
                <Link href={`/payments/new?loanId=${loan.id}`}>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Record Payment
                  </Button>
                </Link>
              )}
            </>
          )}
          <Link href="/loans">
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Loans
            </Button>
          </Link>
        </div>
      }
    >
      <div className="space-y-6">
        {/* Loan Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Principal Amount</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">LKR {Number(loan.principalAmount || 0).toLocaleString()}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Paid</CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{formatCurrency(calculateTotalPaid())}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Remaining Amount</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{formatCurrency(calculateOutstanding())}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Status</CardTitle>
              <Badge className={getStatusBadgeColor(loan.status)}>
                {loan.status}
              </Badge>
            </CardHeader>
            <CardContent>
              <div className="text-sm text-gray-600">
                Interest: {loan.interestRate}% | {getTenureDisplayText()}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Information Tabs */}
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="payments">Payments ({loan.payments?.length || 0})</TabsTrigger>
            <TabsTrigger value="schedule">Payment Schedule ({loan.schedules?.length || 0})</TabsTrigger>
            <TabsTrigger value="guarantors">Guarantors ({loan.guarantors?.length || 0})</TabsTrigger>
            <TabsTrigger value="documents">Documents ({loan.documents?.length || 0})</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Customer Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Customer Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <span className="font-medium text-gray-600">Name:</span>
                    <p className="font-semibold">{loan.customer.firstName} {loan.customer.lastName}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Phone:</span>
                    <p className="font-semibold">{loan.customer.phone}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Email:</span>
                    <p className="font-semibold">{loan.customer.email || 'N/A'}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">National ID:</span>
                    <p className="font-semibold">{loan.customer.nationalId}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Monthly Income:</span>
                    <p className="font-semibold">LKR {Number(loan.customer.monthlyIncome || 0).toLocaleString()}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Employment:</span>
                    <p className="font-semibold">{loan.customer.employmentType || 'N/A'}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Employer:</span>
                    <p className="font-semibold">{loan.customer.employer || 'N/A'}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Address:</span>
                    <p className="font-semibold">{loan.customer.address || 'N/A'}</p>
                  </div>
                </CardContent>
              </Card>

              {/* Loan Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Loan Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <span className="font-medium text-gray-600">Loan Type:</span>
                    <p className="font-semibold">{loan.loanType.name}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Purpose:</span>
                    <p className="font-semibold">{loan.purpose || 'N/A'}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Repayment Frequency:</span>
                    <p className="font-semibold">{loan.repaymentFrequency}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Created:</span>
                    <p className="font-semibold">{new Date(loan.createdAt).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Disbursed:</span>
                    <p className="font-semibold">{loan.disbursedAt ? new Date(loan.disbursedAt).toLocaleDateString() : 'Not disbursed'}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Notes:</span>
                    <p className="font-semibold">{loan.notes || 'N/A'}</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Loan Communication System */}
            <LoanComments loanId={loan.id} />

            {/* Loan Activity Log */}
            <LoanActivityLog loanId={loan.id} />
          </TabsContent>

          <TabsContent value="payments" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Payment History</CardTitle>
                <CardDescription>All payments made for this loan</CardDescription>
              </CardHeader>
              <CardContent>
                {loan.payments && loan.payments.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Method</TableHead>
                        <TableHead>Reference</TableHead>
                        <TableHead>Recorded By</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {loan.payments.map((payment) => (
                        <TableRow key={payment.id}>
                          <TableCell>
                            <div>
                              <div>{new Date(payment.paymentDate).toLocaleDateString()}</div>
                              <div className="text-sm text-gray-500">
                                {new Date(payment.paymentDate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="font-semibold">LKR {Number(payment.amount || 0).toLocaleString()}</TableCell>
                          <TableCell>{payment.paymentMethod}</TableCell>
                          <TableCell>{payment.referenceNumber || 'N/A'}</TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {payment.createdByUser ?
                                `${payment.createdByUser.firstName} ${payment.createdByUser.lastName}` :
                                'System'
                              }
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={getPaymentStatusColor(payment.status)}>
                              {payment.status}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-gray-500 mb-4">No payments recorded yet</p>
                    {(loan.status === 'ACTIVE' || loan.status === 'DISBURSED') ? (
                      <Link href={`/payments/new?loanId=${loan.id}`}>
                        <Button>
                          <Plus className="h-4 w-4 mr-2" />
                          Record First Payment
                        </Button>
                      </Link>
                    ) : (
                      <p className="text-sm text-gray-400">
                        Payment recording is only available after loan disbursement
                      </p>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="schedule" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Payment Schedule</CardTitle>
                <CardDescription>
                  Installment schedule for this loan - {loan.repaymentFrequency.toLowerCase()} payments
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loan.schedules && loan.schedules.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Installment</TableHead>
                        <TableHead>Due Date</TableHead>
                        <TableHead>Principal</TableHead>
                        <TableHead>Interest</TableHead>
                        <TableHead>Total</TableHead>
                        <TableHead>Paid</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {loan.schedules.map((schedule, index) => (
                        <TableRow key={schedule.id || index}>
                          <TableCell>#{Number(schedule.installmentNumber || 0)}</TableCell>
                          <TableCell>{new Date(schedule.dueDate).toLocaleDateString()}</TableCell>
                          <TableCell>LKR {Number(schedule.principalAmount || 0).toLocaleString()}</TableCell>
                          <TableCell>LKR {Number(schedule.interestAmount || 0).toLocaleString()}</TableCell>
                          <TableCell className="font-semibold">LKR {Number(schedule.totalAmount || 0).toLocaleString()}</TableCell>
                          <TableCell className="font-semibold">LKR {Number(schedule.paidAmount || 0).toLocaleString()}</TableCell>
                          <TableCell>
                            <Badge className={getScheduleStatusColor(schedule.status)}>
                              {schedule.status}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-gray-500">Payment schedule will be generated automatically when the loan is disbursed</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="guarantors" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Guarantors
                </CardTitle>
                <CardDescription>People who have guaranteed this loan</CardDescription>
              </CardHeader>
              <CardContent>
                {loan.guarantors && loan.guarantors.length > 0 ? (
                  <div className="space-y-4">
                    {loan.guarantors.map((guarantor, index) => (
                      <Card key={guarantor.id} className="bg-gray-50">
                        <CardContent className="pt-4">
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div>
                              <span className="font-medium text-gray-600">Type:</span>
                              <p className="font-semibold">{guarantor.guarantorType}</p>
                            </div>
                            <div>
                              <span className="font-medium text-gray-600">Name:</span>
                              <p className="font-semibold">
                                {guarantor.customer ?
                                  `${guarantor.customer.firstName} ${guarantor.customer.lastName}` :
                                  guarantor.guarantor ?
                                  `${guarantor.guarantor.firstName} ${guarantor.guarantor.lastName}` :
                                  'N/A'
                                }
                              </p>
                            </div>
                            <div>
                              <span className="font-medium text-gray-600">Phone:</span>
                              <p className="font-semibold">
                                {guarantor.customer?.phone || guarantor.guarantor?.phone || 'N/A'}
                              </p>
                            </div>
                            <div>
                              <span className="font-medium text-gray-600">Liability:</span>
                              <p className="font-semibold">LKR {Number(guarantor.liabilityAmount || 0).toLocaleString()}</p>
                            </div>
                          </div>
                          <div className="mt-2">
                            <Badge className={guarantor.guarantorStatus === 'ACTIVE' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                              {guarantor.guarantorStatus}
                            </Badge>
                          </div>

                          {/* Show documents for individual guarantors */}
                          {guarantor.guarantorType === 'INDIVIDUAL' && guarantor.guarantor?.documents && guarantor.guarantor.documents.length > 0 && (
                            <div className="mt-4">
                              <h5 className="font-medium text-gray-700 mb-2">Documents:</h5>
                              <div className="space-y-2">
                                {guarantor.guarantor.documents.map((document) => (
                                  <div key={document.id} className="flex items-center justify-between p-2 bg-white rounded border">
                                    <div>
                                      <p className="font-medium text-sm">{document.documentName}</p>
                                      <p className="text-xs text-gray-500">{document.documentType} • {new Date(document.uploadedAt).toLocaleDateString()}</p>
                                    </div>
                                    <div className="flex gap-1">
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handleViewDocument({...document, isGuarantorDocument: true})}
                                      >
                                        <Eye className="h-3 w-3" />
                                      </Button>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handleDownloadDocument({...document, isGuarantorDocument: true})}
                                      >
                                        <Download className="h-3 w-3" />
                                      </Button>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-gray-500">No guarantors for this loan</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Documents
                </CardTitle>
                <CardDescription>Documents uploaded for this loan</CardDescription>
              </CardHeader>
              <CardContent>
                {loan.documents && loan.documents.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Document Name</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Uploaded</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {loan.documents.map((document) => (
                        <TableRow key={document.id}>
                          <TableCell className="font-semibold">{document.originalName}</TableCell>
                          <TableCell>{document.documentType}</TableCell>
                          <TableCell>{new Date(document.uploadedAt).toLocaleDateString()}</TableCell>
                          <TableCell>
                            <div className="flex gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleViewDocument(document)}
                              >
                                <Eye className="h-4 w-4 mr-1" />
                                View
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDownloadDocument(document)}
                              >
                                <Download className="h-4 w-4 mr-1" />
                                Download
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-gray-500">No documents uploaded yet</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Approval Dialog */}
      <Dialog open={approvalDialog.isOpen} onOpenChange={(open) =>
        setApprovalDialog(prev => ({ ...prev, isOpen: open }))
      }>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {approvalDialog.action === 'APPROVE' && 'Approve Loan Application'}
              {approvalDialog.action === 'REJECT' && 'Reject Loan Application'}
              {approvalDialog.action === 'REQUEST_MORE_INFO' && 'Request More Information'}
            </DialogTitle>
            <DialogDescription>
              {loan && (
                <>
                  Loan ID: {loan.loanNumber} - {loan.customer.firstName} {loan.customer.lastName}
                  <br />
                  Amount: LKR {Number(loan.principalAmount).toLocaleString()}
                </>
              )}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Add any notes or comments..."
                rows={3}
              />
            </div>

            {approvalDialog.action === 'REQUEST_MORE_INFO' && (
              <div>
                <Label htmlFor="requestedInfo">Requested Information *</Label>
                <Textarea
                  id="requestedInfo"
                  value={requestedInfo}
                  onChange={(e) => setRequestedInfo(e.target.value)}
                  placeholder="Specify what additional information is needed..."
                  rows={3}
                  required
                />
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setApprovalDialog({ isOpen: false, action: null })}
            >
              Cancel
            </Button>
            <Button
              variant={getActionButtonProps(approvalDialog.action || '').variant}
              onClick={processApproval}
              disabled={processing}
            >
              {processing ? 'Processing...' : `Confirm ${approvalDialog.action?.replace('_', ' ')}`}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Disbursement Dialog */}
      <Dialog open={disbursementDialog} onOpenChange={setDisbursementDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Disburse Loan</DialogTitle>
            <DialogDescription>
              {loan && (
                <>
                  Loan ID: {loan.loanNumber} - {loan.customer.firstName} {loan.customer.lastName}
                  <br />
                  Principal Amount: LKR {Number(loan.principalAmount).toLocaleString()}
                </>
              )}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="disbursedAmount">Disbursed Amount *</Label>
              <Input
                id="disbursedAmount"
                type="number"
                value={disbursementData.disbursedAmount}
                onChange={(e) => setDisbursementData(prev => ({
                  ...prev,
                  disbursedAmount: parseFloat(e.target.value) || 0
                }))}
                placeholder="Enter disbursed amount"
                required
              />
            </div>

            <div>
              <Label htmlFor="disbursementMethod">Disbursement Method *</Label>
              <Select
                value={disbursementData.disbursementMethod}
                onValueChange={handleDisbursementMethodChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select disbursement method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="CASH">Cash</SelectItem>
                  <SelectItem value="BANK_TRANSFER">Bank Transfer</SelectItem>
                  <SelectItem value="CHEQUE">Cheque</SelectItem>
                  <SelectItem value="ONLINE">Online Transfer</SelectItem>
                  <SelectItem value="MOBILE_PAYMENT">Mobile Payment</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="disbursementReference">Reference Number</Label>
              <Input
                id="disbursementReference"
                value={disbursementData.disbursementReference}
                onChange={(e) => setDisbursementData(prev => ({
                  ...prev,
                  disbursementReference: e.target.value
                }))}
                placeholder="Transaction/Reference number"
              />
            </div>

            <div>
              <Label htmlFor="disbursementDate">Disbursement Date *</Label>
              <Input
                id="disbursementDate"
                type="date"
                value={disbursementData.disbursementDate}
                onChange={(e) => setDisbursementData(prev => ({
                  ...prev,
                  disbursementDate: e.target.value
                }))}
                required
              />
            </div>

            <div>
              <Label htmlFor="disbursementNotes">Notes</Label>
              <Textarea
                id="disbursementNotes"
                value={disbursementData.disbursementNotes}
                onChange={(e) => setDisbursementData(prev => ({
                  ...prev,
                  disbursementNotes: e.target.value
                }))}
                placeholder="Add any notes about the disbursement..."
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDisbursementDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={processDisbursement}
              disabled={disbursing || !disbursementData.disbursedAmount || !disbursementData.disbursementMethod}
            >
              {disbursing ? 'Disbursing...' : 'Disburse Loan'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </PageHeader>
  )
}
