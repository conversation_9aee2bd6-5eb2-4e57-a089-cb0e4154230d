'use client'

import { useState, useEffect, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { ArrowLeft, Save, Loader2, Calculator, Lock, Settings, Info, Search } from 'lucide-react'
import { calculateLoanInterest, convertTenureToDays, getEMIFrequencyLabel } from '@/lib/interest-calculations'
import { useToast } from '@/hooks/use-toast'
import DocumentUpload from '@/components/ui/document-upload'
import GuarantorManagement from '@/components/ui/guarantor-management'
import { formatCurrency } from '@/lib/utils'

interface Customer {
  id: string
  firstName: string
  lastName: string
  email?: string
  phone: string
  monthlyIncome: number
}

interface DetailedCustomer {
  customer: {
    id: string
    firstName: string
    lastName: string
    email?: string
    phone: string
    nationalId: string
    monthlyIncome: number
    assignedOfficer?: {
      id: string
      firstName: string
      lastName: string
      role: string
    }
  }
  loanSummary: {
    totalLoans: number
    activeLoans: number
    totalLoanAmount: number
    totalPaidAmount: number
    outstandingAmount: number
    recentLoans: Array<{
      id: string
      loanNumber: string
      loanType: string
      category: string
      principalAmount: number
      totalAmount: number
      status: string
      applicationDate: string
      disbursedDate?: string
      totalPaid: number
    }>
  }
  guarantorSummary: {
    totalGuarantorRoles: number
    activeGuarantorRoles: number
    totalGuarantorLiability: number
    recentGuarantorRoles: Array<{
      id: string
      loanNumber: string
      loanType: string
      borrowerName: string
      borrowerPhone: string
      principalAmount: number
      liabilityAmount: number
      status: string
      applicationDate: string
      guarantorType: string
    }>
  }
  riskAssessment: {
    riskFactors: string[]
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
  }
}

interface NewGuarantor {
  firstName: string
  lastName: string
  nationalId: string
  phone: string
  email?: string
  address: string
  city: string
  state: string
  dateOfBirth?: string
  occupation?: string
  monthlyIncome?: number
  relationship?: string
  documents: DocumentUpload[]
}

interface SelectedGuarantor {
  type: 'existing' | 'new'
  customerId?: string
  guarantorData?: NewGuarantor
}

interface FormData {
  customerId: string
  loanTypeId: string
  principalAmount: number
  interestRate: number
  tenure: number
  repaymentFrequency: string
  gracePeriod: number
  processingFee: number
  insuranceFee: number
  otherCharges: number
  purpose: string
  collateralDescription: string
  notes: string
  guarantors: SelectedGuarantor[]
}

interface FormErrors {
  [key: string]: string
}

interface DocumentUpload {
  id?: string
  documentName: string
  file?: File
  fileName?: string
  fileSize?: number
  uploadedAt?: string
  uploading?: boolean
}

function NewLoanContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  const { data: session } = useSession()
  
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loanTypes, setLoanTypes] = useState<any[]>([])
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [detailedCustomer, setDetailedCustomer] = useState<DetailedCustomer | null>(null)
  const [loadingCustomerDetails, setLoadingCustomerDetails] = useState(false)
  const [loading, setLoading] = useState(true)
  const [loanTypesLoading, setLoanTypesLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [errors, setErrors] = useState<FormErrors>({})
  const [warnings, setWarnings] = useState<FormErrors>({})
  const [calculatedEMI, setCalculatedEMI] = useState<number>(0)
  const [calculatedTotal, setCalculatedTotal] = useState<number>(0)
  const [calculatedInterest, setCalculatedInterest] = useState<number>(0)
  const [numberOfPayments, setNumberOfPayments] = useState<number>(0)
  const [emiFrequencyLabel, setEmiFrequencyLabel] = useState<string>('Monthly EMI')

  const [formData, setFormData] = useState<FormData>({
    customerId: searchParams.get('customerId') || '',
    loanTypeId: '',
    principalAmount: 0,
    interestRate: 12, // Default 12%
    tenure: 12, // Default 12 months
    repaymentFrequency: 'MONTHLY',
    gracePeriod: 0,
    processingFee: 0,
    insuranceFee: 0,
    otherCharges: 0,
    purpose: '',
    collateralDescription: '',
    notes: '',
    guarantors: []
  })

  const [documents, setDocuments] = useState<DocumentUpload[]>([])
  const [selectedLoanType, setSelectedLoanType] = useState<any>(null)
  const [requiredDocuments, setRequiredDocuments] = useState<any[]>([])
  const [allRequiredDocuments, setAllRequiredDocuments] = useState<any[]>([])
  const [customerSearch, setCustomerSearch] = useState('')
  const [customerAssignmentFilter, setCustomerAssignmentFilter] = useState(
    session?.user?.role === 'CREDIT_OFFICER' ? 'ASSIGNED_TO_ME' : 'ALL'
  )

  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        const params = new URLSearchParams({
          limit: '1000', // Get more customers for selection
          ...(customerSearch && { search: customerSearch }),
          ...(customerAssignmentFilter && customerAssignmentFilter !== 'ALL' && { assignmentFilter: customerAssignmentFilter })
        })

        const response = await fetch(`/api/customers?${params}`)
        if (response.ok) {
          const data = await response.json()
          setCustomers(data.customers || [])
          
          // If customerId is provided in URL, select that customer
          if (formData.customerId) {
            const customer = data.customers.find((c: Customer) => c.id === formData.customerId)
            if (customer) {
              setSelectedCustomer(customer)
            }
          }
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load customers",
          variant: "destructive"
        })
      } finally {
        setLoading(false)
      }
    }

    fetchCustomers()
  }, [formData.customerId, customerSearch, customerAssignmentFilter, toast])

  // Update assignment filter when session changes
  useEffect(() => {
    if (session?.user?.role === 'CREDIT_OFFICER' && customerAssignmentFilter === 'ALL') {
      setCustomerAssignmentFilter('ASSIGNED_TO_ME')
    }
  }, [session])

  // Function to fetch detailed customer information
  const fetchDetailedCustomerInfo = async (customerId: string) => {
    setLoadingCustomerDetails(true)
    try {
      const response = await fetch(`/api/customers/${customerId}/detailed`)
      if (response.ok) {
        const data = await response.json()
        setDetailedCustomer(data)
      } else {
        console.error('Failed to fetch detailed customer information')
      }
    } catch (error) {
      console.error('Error fetching detailed customer information:', error)
    } finally {
      setLoadingCustomerDetails(false)
    }
  }

  // Function to fetch all required documents
  const fetchAllRequiredDocuments = async () => {
    try {
      const response = await fetch('/api/required-documents?active=true')
      if (response.ok) {
        const data = await response.json()
        setAllRequiredDocuments(data.documents || [])
      }
    } catch (error) {
      console.error('Error fetching required documents:', error)
    }
  }

  // Initialize required documents when component mounts
  useEffect(() => {
    fetchAllRequiredDocuments()
  }, [])

  // Function to initialize document list with required documents
  const initializeDocumentList = (requiredDocumentIds: string[]) => {
    const requiredDocs = allRequiredDocuments
      .filter(doc => requiredDocumentIds.includes(doc.id))
      .map(doc => ({
        documentName: doc.name,
        requiredDocumentId: doc.id,
        file: undefined,
        fileName: undefined,
        fileSize: undefined,
        uploadedAt: undefined,
        uploading: false
      }))

    setDocuments(requiredDocs)
  }

  useEffect(() => {
    const fetchLoanTypes = async () => {
      try {
        setLoanTypesLoading(true)
        const response = await fetch('/api/loan-types?active=true')
        if (response.ok) {
          const data = await response.json()
          setLoanTypes(data || []) // API returns array directly, not nested object
        } else {
          console.error('Failed to fetch loan types:', response.status, response.statusText)
        }
      } catch (error) {
        console.error('Error fetching loan types:', error)
        toast({
          title: "Error",
          description: "Failed to load loan types",
          variant: "destructive"
        })
      } finally {
        setLoanTypesLoading(false)
      }
    }

    fetchLoanTypes()
  }, [])

  useEffect(() => {
    // Calculate EMI and total amount using the new interest calculation method
    if (formData.principalAmount > 0 && formData.interestRate > 0 && formData.tenure > 0 && selectedLoanType) {
      try {
        // Convert tenure to days based on loan type's tenure unit
        const tenureInDays = convertTenureToDays(formData.tenure, selectedLoanType.tenureUnit)

        // Calculate using the loan type's interest calculation method
        const calculation = calculateLoanInterest({
          principalAmount: formData.principalAmount,
          interestRate: formData.interestRate,
          tenureInDays,
          collectionType: formData.repaymentFrequency as any,
          interestCalculationMethod: selectedLoanType.interestCalculationMethod || 'MONTHLY_INTEREST'
        })

        setCalculatedEMI(calculation.emiAmount)
        setCalculatedInterest(calculation.totalInterest)
        setNumberOfPayments(calculation.numberOfPayments)
        setCalculatedTotal(calculation.totalAmount + formData.processingFee + formData.insuranceFee + formData.otherCharges)
        setEmiFrequencyLabel(getEMIFrequencyLabel(formData.repaymentFrequency))
      } catch (error) {
        console.error('Error calculating loan interest:', error)
        setCalculatedEMI(0)
        setCalculatedInterest(0)
        setNumberOfPayments(0)
        setCalculatedTotal(0)
      }
    } else {
      setCalculatedEMI(0)
      setCalculatedInterest(0)
      setNumberOfPayments(0)
      setCalculatedTotal(0)
    }
  }, [formData.principalAmount, formData.interestRate, formData.tenure, formData.repaymentFrequency, formData.processingFee, formData.insuranceFee, formData.otherCharges, selectedLoanType])

  const handleInputChange = (field: keyof FormData, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }

    // Update selected customer when customerId changes
    if (field === 'customerId') {
      const customer = customers.find(c => c.id === value)
      setSelectedCustomer(customer || null)

      // Fetch detailed customer information
      if (customer) {
        fetchDetailedCustomerInfo(customer.id)
      } else {
        setDetailedCustomer(null)
      }
    }
  }

  const fetchRequiredDocuments = async (documentIds: string[]) => {
    if (documentIds.length === 0) {
      setRequiredDocuments([])
      return
    }

    try {
      const response = await fetch('/api/required-documents?active=true')
      if (response.ok) {
        const allDocuments = await response.json()
        const filteredDocuments = allDocuments.filter((doc: any) => documentIds.includes(doc.id))
        setRequiredDocuments(filteredDocuments)
      }
    } catch (error) {
      console.error('Error fetching required documents:', error)
      setRequiredDocuments([])
    }
  }

  const handleLoanTypeChange = (loanTypeId: string) => {
    const loanType = loanTypes.find(lt => lt.id === loanTypeId)
    setSelectedLoanType(loanType)

    if (loanType) {
      // Convert tenure from days to the appropriate unit for display
      const convertedTenure = convertTenureFromDays(loanType.defaultTenure, loanType.tenureUnit)

      setFormData(prev => ({
        ...prev,
        loanTypeId,
        interestRate: Number(loanType.defaultInterestRate),
        tenure: convertedTenure,
        repaymentFrequency: loanType.collectionType,
        gracePeriod: loanType.gracePeriod,
        processingFee: prev.principalAmount * (Number(loanType.processingFeeRate || 0) / 100),
        insuranceFee: prev.principalAmount * (Number(loanType.insuranceFeeRate || 0) / 100)
      }))

      // Fetch required documents for this loan type
      fetchRequiredDocuments(loanType.requiredDocuments || [])

      // Initialize document list with required documents
      initializeDocumentList(loanType.requiredDocuments || [])
    } else {
      setFormData(prev => ({ ...prev, loanTypeId }))
      setRequiredDocuments([])
      setDocuments([])
    }

    // Clear error when user selects
    if (errors.loanTypeId) {
      setErrors(prev => ({ ...prev, loanTypeId: '' }))
    }
  }

  // Helper function to convert tenure from days to display unit
  const convertTenureFromDays = (days: number, unit: string): number => {
    switch (unit) {
      case 'DAYS': return days
      case 'WEEKS': return Math.round(days / 7)
      case 'MONTHS': return Math.round(days / 30)
      case 'YEARS': return Math.round(days / 365)
      default: return days
    }
  }

  // Helper function to convert tenure to days for validation
  const convertTenureToDays = (value: number, unit: string): number => {
    switch (unit) {
      case 'DAYS': return value
      case 'WEEKS': return value * 7
      case 'MONTHS': return value * 30
      case 'YEARS': return value * 365
      default: return value
    }
  }

  // Enhanced input change handler with validation
  const handleValidatedInputChange = (field: keyof FormData, value: string | number) => {
    let validatedValue = value
    let error = ''

    if (selectedLoanType) {
      switch (field) {
        case 'principalAmount':
          const amount = Number(value)
          if (amount < Number(selectedLoanType.minAmount)) {
            error = `Minimum amount is LKR ${Number(selectedLoanType.minAmount).toLocaleString()}`
          } else if (amount > Number(selectedLoanType.maxAmount)) {
            error = `Maximum amount is LKR ${Number(selectedLoanType.maxAmount).toLocaleString()}`
          }
          // Recalculate fees when principal amount changes
          if (!error) {
            setFormData(prev => ({
              ...prev,
              [field]: validatedValue,
              processingFee: amount * (Number(selectedLoanType.processingFeeRate || 0) / 100),
              insuranceFee: amount * (Number(selectedLoanType.insuranceFeeRate || 0) / 100)
            }))
            setErrors(prev => ({ ...prev, [field]: '' }))
            return
          }
          break

        case 'interestRate':
          const rate = Number(value)
          if (rate < Number(selectedLoanType.minInterestRate)) {
            error = `Minimum interest rate is ${selectedLoanType.minInterestRate}%`
          } else if (rate > Number(selectedLoanType.maxInterestRate)) {
            error = `Maximum interest rate is ${selectedLoanType.maxInterestRate}%`
          }
          break

        case 'tenure':
          const tenure = Number(value)
          const tenureInDays = convertTenureToDays(tenure, selectedLoanType.tenureUnit)
          if (tenureInDays < selectedLoanType.minTenure) {
            const minDisplay = convertTenureFromDays(selectedLoanType.minTenure, selectedLoanType.tenureUnit)
            error = `Minimum tenure is ${minDisplay} ${selectedLoanType.tenureUnit.toLowerCase()}`
          } else if (tenureInDays > selectedLoanType.maxTenure) {
            const maxDisplay = convertTenureFromDays(selectedLoanType.maxTenure, selectedLoanType.tenureUnit)
            error = `Maximum tenure is ${maxDisplay} ${selectedLoanType.tenureUnit.toLowerCase()}`
          }
          break
      }
    }

    // Update form data and errors
    setFormData(prev => ({ ...prev, [field]: validatedValue }))
    setErrors(prev => ({ ...prev, [field]: error }))
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}
    const newWarnings: FormErrors = {}

    if (!formData.customerId) newErrors.customerId = 'Customer is required'
    if (!formData.loanTypeId) newErrors.loanTypeId = 'Loan type is required'
    if (!formData.principalAmount || formData.principalAmount <= 0) newErrors.principalAmount = 'Valid principal amount is required'
    if (!formData.interestRate || formData.interestRate < 0) newErrors.interestRate = 'Valid interest rate is required'
    if (!formData.tenure || formData.tenure <= 0) newErrors.tenure = 'Valid tenure is required'
    if (!formData.purpose.trim()) newErrors.purpose = 'Loan purpose is required'

    // Validate against loan type constraints
    if (selectedLoanType) {
      // Principal amount validation
      if (formData.principalAmount < Number(selectedLoanType.minAmount)) {
        newErrors.principalAmount = `Minimum amount is LKR ${Number(selectedLoanType.minAmount).toLocaleString()}`
      } else if (formData.principalAmount > Number(selectedLoanType.maxAmount)) {
        newErrors.principalAmount = `Maximum amount is LKR ${Number(selectedLoanType.maxAmount).toLocaleString()}`
      }

      // Interest rate validation
      if (formData.interestRate < Number(selectedLoanType.minInterestRate)) {
        newErrors.interestRate = `Minimum interest rate is ${selectedLoanType.minInterestRate}%`
      } else if (formData.interestRate > Number(selectedLoanType.maxInterestRate)) {
        newErrors.interestRate = `Maximum interest rate is ${selectedLoanType.maxInterestRate}%`
      }

      // Tenure validation
      const tenureInDays = convertTenureToDays(formData.tenure, selectedLoanType.tenureUnit)
      if (tenureInDays < selectedLoanType.minTenure) {
        const minDisplay = convertTenureFromDays(selectedLoanType.minTenure, selectedLoanType.tenureUnit)
        newErrors.tenure = `Minimum tenure is ${minDisplay} ${selectedLoanType.tenureUnit.toLowerCase()}`
      } else if (tenureInDays > selectedLoanType.maxTenure) {
        const maxDisplay = convertTenureFromDays(selectedLoanType.maxTenure, selectedLoanType.tenureUnit)
        newErrors.tenure = `Maximum tenure is ${maxDisplay} ${selectedLoanType.tenureUnit.toLowerCase()}`
      }
    }

    // Validate against customer's monthly income (basic affordability check - warning only)
    if (selectedCustomer && calculatedEMI > selectedCustomer.monthlyIncome * 0.5) {
      newWarnings.principalAmount = 'EMI exceeds 50% of customer monthly income'
    }

    // Guarantor validation
    if (selectedLoanType?.requiresGuarantor) {
      const requiredGuarantors = selectedLoanType.maxGuarantors
      const providedGuarantors = formData.guarantors.filter(g => {
        if (g.type === 'existing') {
          return g.customerId && g.customerId.trim() !== ''
        } else {
          return g.guarantorData &&
                 g.guarantorData.firstName.trim() !== '' &&
                 g.guarantorData.lastName.trim() !== '' &&
                 g.guarantorData.nationalId.trim() !== '' &&
                 g.guarantorData.phone.trim() !== '' &&
                 g.guarantorData.address.trim() !== '' &&
                 g.guarantorData.city.trim() !== '' &&
                 g.guarantorData.state.trim() !== ''
        }
      }).length

      if (providedGuarantors < requiredGuarantors) {
        newErrors.guarantors = `This loan type requires ${requiredGuarantors} guarantor(s). You have provided ${providedGuarantors}.`
      }

      // Check for duplicate customer guarantors
      const customerGuarantors = formData.guarantors
        .filter(g => g.type === 'existing' && g.customerId)
        .map(g => g.customerId)
      const uniqueCustomerGuarantors = new Set(customerGuarantors)
      if (uniqueCustomerGuarantors.size !== customerGuarantors.length) {
        newErrors.guarantors = 'Duplicate customer guarantors are not allowed'
      }

      // Check for duplicate national IDs in new guarantors
      const nationalIds = formData.guarantors
        .filter(g => g.type === 'new' && g.guarantorData?.nationalId)
        .map(g => g.guarantorData!.nationalId)
      const uniqueNationalIds = new Set(nationalIds)
      if (uniqueNationalIds.size !== nationalIds.length) {
        newErrors.guarantors = 'Duplicate national IDs are not allowed'
      }
    }

    setErrors(newErrors)
    setWarnings(newWarnings)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors below",
        variant: "destructive"
      })
      return
    }

    setSaving(true)
    try {
      // Include uploaded documents in the loan creation request
      const loanData = {
        ...formData,
        documents: documents
          .filter(doc => doc.id) // Only include successfully uploaded documents
          .map(doc => ({
            id: doc.id,
            documentName: doc.documentName,
            fileName: doc.fileName,
            fileSize: doc.fileSize,
            requiredDocumentId: doc.requiredDocumentId
          }))
      }

      const response = await fetch('/api/loans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(loanData),
      })

      if (response.ok) {
        const loan = await response.json()
        toast({
          title: "Success",
          description: "Loan application created successfully",
        })
        router.push(`/loans/${loan.id}`)
      } else {
        const errorData = await response.json()
        toast({
          title: "Error",
          description: errorData.error || "Failed to create loan application",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create loan application",
        variant: "destructive"
      })
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center gap-4 mb-6">
        <Link href="/loans">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Loans
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">New Loan Application</h1>
          <p className="text-gray-600">Create a new loan application</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Customer Selection */}
        <Card>
          <CardHeader>
            <CardTitle>Customer Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Customer Search and Filter */}
            <div className="space-y-3">
              <Label>Customer Search & Filter</Label>
              <div className="flex gap-3">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search customers by name, phone, or ID..."
                      value={customerSearch}
                      onChange={(e) => setCustomerSearch(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                {/* Assignment Filter - Show for Credit Officers */}
                {session?.user?.role === 'CREDIT_OFFICER' && (
                  <Select value={customerAssignmentFilter} onValueChange={setCustomerAssignmentFilter}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="Filter customers" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ASSIGNED_TO_ME">My Customers</SelectItem>
                      <SelectItem value="ALL">All Customers</SelectItem>
                    </SelectContent>
                  </Select>
                )}
              </div>
            </div>

            <div>
              <Label htmlFor="customerId">Customer *</Label>
              <Select value={formData.customerId} onValueChange={(value) => handleInputChange('customerId', value)}>
                <SelectTrigger className={errors.customerId ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select customer" />
                </SelectTrigger>
                <SelectContent>
                  {customers.length === 0 ? (
                    <SelectItem value="" disabled>
                      {loading ? 'Loading customers...' : 'No customers found'}
                    </SelectItem>
                  ) : (
                    customers.map((customer) => (
                      <SelectItem key={customer.id} value={customer.id}>
                        {customer.firstName} {customer.lastName} - {customer.phone}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              {errors.customerId && <p className="text-red-500 text-sm mt-1">{errors.customerId}</p>}
              {customers.length > 0 && (
                <p className="text-xs text-gray-500 mt-1">
                  {customers.length} customer{customers.length !== 1 ? 's' : ''} found
                </p>
              )}
            </div>

            {selectedCustomer && (
              <div className="space-y-4">
                {/* Basic Customer Details */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">Selected Customer Details</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Name:</span> {selectedCustomer.firstName} {selectedCustomer.lastName}
                    </div>
                    <div>
                      <span className="font-medium">Phone:</span> {selectedCustomer.phone}
                    </div>
                    <div>
                      <span className="font-medium">Email:</span> {selectedCustomer.email || 'N/A'}
                    </div>
                    <div>
                      <span className="font-medium">Monthly Income:</span> LKR {selectedCustomer.monthlyIncome.toLocaleString()}
                    </div>
                  </div>
                </div>

                {/* Detailed Customer Information */}
                {loadingCustomerDetails ? (
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <p className="text-blue-800 text-sm">Loading detailed customer information...</p>
                  </div>
                ) : detailedCustomer && (
                  <div className="space-y-4">
                    {/* Risk Assessment */}
                    {detailedCustomer.riskAssessment.riskFactors.length > 0 && (
                      <div className={`p-4 rounded-lg border ${
                        detailedCustomer.riskAssessment.riskLevel === 'HIGH' ? 'bg-red-50 border-red-200' :
                        detailedCustomer.riskAssessment.riskLevel === 'MEDIUM' ? 'bg-yellow-50 border-yellow-200' :
                        'bg-green-50 border-green-200'
                      }`}>
                        <h5 className="font-medium mb-2 flex items-center gap-2">
                          <span className={`w-2 h-2 rounded-full ${
                            detailedCustomer.riskAssessment.riskLevel === 'HIGH' ? 'bg-red-500' :
                            detailedCustomer.riskAssessment.riskLevel === 'MEDIUM' ? 'bg-yellow-500' :
                            'bg-green-500'
                          }`}></span>
                          Risk Assessment: {detailedCustomer.riskAssessment.riskLevel}
                        </h5>
                        <ul className="text-sm space-y-1">
                          {detailedCustomer.riskAssessment.riskFactors.map((factor, index) => (
                            <li key={index} className="flex items-center gap-2">
                              <span className="text-gray-500">•</span>
                              {factor}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Loan History Summary */}
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h5 className="font-medium mb-2">Loan History</h5>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="font-medium text-blue-800">Total Loans:</span>
                          <p className="font-semibold">{detailedCustomer.loanSummary.totalLoans}</p>
                        </div>
                        <div>
                          <span className="font-medium text-blue-800">Active Loans:</span>
                          <p className="font-semibold">{detailedCustomer.loanSummary.activeLoans}</p>
                        </div>
                        <div>
                          <span className="font-medium text-blue-800">Outstanding:</span>
                          <p className="font-semibold">LKR {detailedCustomer.loanSummary.outstandingAmount.toLocaleString()}</p>
                        </div>
                        <div>
                          <span className="font-medium text-blue-800">Total Paid:</span>
                          <p className="font-semibold">LKR {detailedCustomer.loanSummary.totalPaidAmount.toLocaleString()}</p>
                        </div>
                      </div>
                    </div>

                    {/* Guarantor History Summary */}
                    {detailedCustomer.guarantorSummary.totalGuarantorRoles > 0 && (
                      <div className="bg-purple-50 p-4 rounded-lg">
                        <h5 className="font-medium mb-2">Guarantor History</h5>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-purple-800">Total Guarantor Roles:</span>
                            <p className="font-semibold">{detailedCustomer.guarantorSummary.totalGuarantorRoles}</p>
                          </div>
                          <div>
                            <span className="font-medium text-purple-800">Active Guarantor Roles:</span>
                            <p className="font-semibold">{detailedCustomer.guarantorSummary.activeGuarantorRoles}</p>
                          </div>
                          <div>
                            <span className="font-medium text-purple-800">Total Liability:</span>
                            <p className="font-semibold">LKR {detailedCustomer.guarantorSummary.totalGuarantorLiability.toLocaleString()}</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Loan Details */}
        <Card>
          <CardHeader>
            <CardTitle>Loan Details</CardTitle>
            {selectedLoanType && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-2">
                <h4 className="font-medium text-blue-900 mb-2">Selected Loan Type: {selectedLoanType.name}</h4>
                <div className="text-sm text-blue-800 space-y-1">
                  <p>• <strong>Amount Range:</strong> LKR {Number(selectedLoanType.minAmount).toLocaleString()} - {Number(selectedLoanType.maxAmount).toLocaleString()}</p>
                  <p>• <strong>Interest Rate:</strong> {selectedLoanType.minInterestRate}% - {selectedLoanType.maxInterestRate}% (Default: {selectedLoanType.defaultInterestRate}%)</p>
                  <p>• <strong>Tenure:</strong> {convertTenureFromDays(selectedLoanType.minTenure, selectedLoanType.tenureUnit)} - {convertTenureFromDays(selectedLoanType.maxTenure, selectedLoanType.tenureUnit)} {selectedLoanType.tenureUnit.toLowerCase()}</p>
                  <p>• <strong>Collection:</strong> {selectedLoanType.collectionType} (Fixed)</p>
                  <p>• <strong>Grace Period:</strong> {selectedLoanType.gracePeriod} days (Fixed)</p>
                  <p>• <strong>Processing Fee:</strong> {selectedLoanType.processingFeeRate || 0}% of principal amount</p>
                  <p>• <strong>Insurance Fee:</strong> {selectedLoanType.insuranceFeeRate || 0}% of principal amount</p>
                </div>
              </div>
            )}
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="loanTypeId">Loan Type *</Label>

              <Select value={formData.loanTypeId} onValueChange={handleLoanTypeChange}>
                <SelectTrigger className={errors.loanTypeId ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select loan type" />
                </SelectTrigger>
                <SelectContent>
                  {loanTypesLoading ? (
                    <SelectItem value="loading" disabled>Loading loan types...</SelectItem>
                  ) : loanTypes.length === 0 ? (
                    <SelectItem value="no-types" disabled>No active loan types available</SelectItem>
                  ) : (
                    loanTypes.map((loanType) => (
                      <SelectItem key={loanType.id} value={loanType.id}>
                        {loanType.name}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              {errors.loanTypeId && <p className="text-red-500 text-sm mt-1">{errors.loanTypeId}</p>}
            </div>

            <div>
              <Label htmlFor="principalAmount" className="flex items-center gap-2">
                <Settings className="h-4 w-4 text-blue-500" />
                Principal Amount (LKR) *
                <span className="text-xs text-gray-500">(Configurable within range)</span>
              </Label>
              {selectedLoanType && (
                <p className="text-xs text-gray-500 mb-1">
                  Range: LKR {Number(selectedLoanType.minAmount).toLocaleString()} - {Number(selectedLoanType.maxAmount).toLocaleString()}
                </p>
              )}
              <Input
                id="principalAmount"
                type="number"
                value={formData.principalAmount || ''}
                onChange={(e) => handleValidatedInputChange('principalAmount', parseFloat(e.target.value) || 0)}
                className={errors.principalAmount ? 'border-red-500' : warnings.principalAmount ? 'border-orange-500' : ''}
                min={selectedLoanType ? Number(selectedLoanType.minAmount) : undefined}
                max={selectedLoanType ? Number(selectedLoanType.maxAmount) : undefined}
              />
              {errors.principalAmount && <p className="text-red-500 text-sm mt-1">{errors.principalAmount}</p>}
              {!errors.principalAmount && warnings.principalAmount && <p className="text-orange-500 text-sm mt-1">{warnings.principalAmount}</p>}
            </div>

            <div>
              <Label htmlFor="interestRate" className="flex items-center gap-2">
                <Settings className="h-4 w-4 text-blue-500" />
                {selectedLoanType?.interestCalculationMethod === 'MONTHLY_INTEREST' ? 'Monthly Interest Rate (%) *' : 'Annual Interest Rate (%) *'}
                <span className="text-xs text-gray-500">(Configurable within range)</span>
              </Label>
              {selectedLoanType && (
                <div className="text-xs text-gray-500 mb-1 space-y-1">
                  <p>Range: {selectedLoanType.minInterestRate}% - {selectedLoanType.maxInterestRate}% (Default: {selectedLoanType.defaultInterestRate}%)</p>
                  {selectedLoanType.interestCalculationMethod === 'MONTHLY_INTEREST' && (
                    <p className="text-blue-600 font-medium">
                      📝 Enter monthly rate (e.g., 5% per month)
                    </p>
                  )}
                  {selectedLoanType.interestCalculationMethod === 'COMPOUND_INTEREST' && (
                    <p className="text-green-600 font-medium">
                      📝 Enter annual rate (e.g., 15% per year)
                    </p>
                  )}
                </div>
              )}
              <Input
                id="interestRate"
                type="number"
                step="0.1"
                value={formData.interestRate || ''}
                onChange={(e) => handleValidatedInputChange('interestRate', parseFloat(e.target.value) || 0)}
                className={errors.interestRate ? 'border-red-500' : ''}
                min={selectedLoanType ? Number(selectedLoanType.minInterestRate) : undefined}
                max={selectedLoanType ? Number(selectedLoanType.maxInterestRate) : undefined}
              />
              {errors.interestRate && <p className="text-red-500 text-sm mt-1">{errors.interestRate}</p>}
            </div>

            <div>
              <Label htmlFor="tenure" className="flex items-center gap-2">
                <Settings className="h-4 w-4 text-blue-500" />
                Tenure ({selectedLoanType ? selectedLoanType.tenureUnit.toLowerCase() : 'months'}) *
                <span className="text-xs text-gray-500">(Configurable within range)</span>
              </Label>
              {selectedLoanType && (
                <p className="text-xs text-gray-500 mb-1">
                  Range: {convertTenureFromDays(selectedLoanType.minTenure, selectedLoanType.tenureUnit)} - {convertTenureFromDays(selectedLoanType.maxTenure, selectedLoanType.tenureUnit)} {selectedLoanType.tenureUnit.toLowerCase()}
                </p>
              )}
              <Input
                id="tenure"
                type="number"
                value={formData.tenure || ''}
                onChange={(e) => handleValidatedInputChange('tenure', parseInt(e.target.value) || 0)}
                className={errors.tenure ? 'border-red-500' : ''}
                min={selectedLoanType ? convertTenureFromDays(selectedLoanType.minTenure, selectedLoanType.tenureUnit) : undefined}
                max={selectedLoanType ? convertTenureFromDays(selectedLoanType.maxTenure, selectedLoanType.tenureUnit) : undefined}
              />
              {errors.tenure && <p className="text-red-500 text-sm mt-1">{errors.tenure}</p>}
            </div>

            <div>
              <Label htmlFor="repaymentFrequency" className="flex items-center gap-2">
                <Lock className="h-4 w-4 text-gray-500" />
                Repayment Frequency
                {selectedLoanType && <span className="text-xs text-gray-500">(Fixed by loan type)</span>}
              </Label>
              <Select
                value={formData.repaymentFrequency}
                onValueChange={(value) => handleInputChange('repaymentFrequency', value)}
                disabled={!!selectedLoanType}
              >
                <SelectTrigger className={selectedLoanType ? 'bg-gray-50' : ''}>
                  <SelectValue placeholder="Select frequency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="DAILY">Daily</SelectItem>
                  <SelectItem value="WEEKLY">Weekly</SelectItem>
                  <SelectItem value="MONTHLY">Monthly</SelectItem>
                  <SelectItem value="QUARTERLY">Quarterly</SelectItem>
                  <SelectItem value="YEARLY">Yearly</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="gracePeriod" className="flex items-center gap-2">
                <Lock className="h-4 w-4 text-gray-500" />
                Grace Period (days)
                {selectedLoanType && <span className="text-xs text-gray-500">(Fixed by loan type)</span>}
              </Label>
              <Input
                id="gracePeriod"
                type="number"
                value={formData.gracePeriod || ''}
                onChange={(e) => handleInputChange('gracePeriod', parseInt(e.target.value) || 0)}
                disabled={!!selectedLoanType}
                className={selectedLoanType ? 'bg-gray-50' : ''}
              />
            </div>
          </CardContent>
        </Card>



        {/* Calculation Summary */}
        {calculatedEMI > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                Loan Calculation Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="text-sm font-medium text-blue-600">{emiFrequencyLabel}</p>
                  <p className="text-2xl font-bold text-blue-700">{formatCurrency(calculatedEMI)}</p>
                  <p className="text-xs text-blue-500 mt-1">{numberOfPayments} payments</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <p className="text-sm font-medium text-green-600">Total Amount</p>
                  <p className="text-2xl font-bold text-green-700">LKR {calculatedTotal.toLocaleString()}</p>
                  <p className="text-xs text-green-500 mt-1">Principal + Interest + Fees</p>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <p className="text-sm font-medium text-purple-600">Total Interest</p>
                  <p className="text-2xl font-bold text-purple-700">LKR {calculatedInterest.toLocaleString('en-US', { maximumFractionDigits: 2 })}</p>
                  <p className="text-xs text-purple-500 mt-1">
                    {selectedLoanType?.interestCalculationMethod === 'MONTHLY_INTEREST' ? 'Monthly Interest Method' : 'Compound Interest (EMI) Method'}
                  </p>
                </div>
              </div>

              {/* Fees Breakdown */}
              <div className="mt-6">
                <h4 className="font-medium text-gray-900 mb-3">Fees Breakdown (Auto-calculated from Loan Type)</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <span className="text-sm text-gray-600">Processing Fee</span>
                    <span className="font-medium">LKR {formData.processingFee.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <span className="text-sm text-gray-600">Insurance Fee</span>
                    <span className="font-medium">LKR {formData.insuranceFee.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <span className="text-sm text-gray-600">Other Charges</span>
                    <span className="font-medium">LKR {formData.otherCharges.toLocaleString()}</span>
                  </div>
                </div>
              </div>

              {/* Calculation Method Explanation */}
              {selectedLoanType?.interestCalculationMethod === 'MONTHLY_INTEREST' && (
                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">Interest Calculation Method: Monthly Interest (Simple)</h4>
                  <div className="text-sm text-blue-800 space-y-1">
                    <p>• <strong>Formula:</strong> Principal × (Rate/100) × (Tenure in Months)</p>
                    <p>• <strong>Calculation:</strong> LKR {formData.principalAmount.toLocaleString()} × {formData.interestRate}% × {(convertTenureToDays(formData.tenure, selectedLoanType.tenureUnit) / 30).toFixed(1)} months</p>
                    <p>• <strong>Interest:</strong> LKR {calculatedInterest.toLocaleString('en-US', { maximumFractionDigits: 2 })}</p>
                    <p>• <strong>Total Repayment:</strong> LKR {(formData.principalAmount + calculatedInterest).toLocaleString('en-US', { maximumFractionDigits: 2 })}</p>
                    <p>• <strong>{emiFrequencyLabel}:</strong> LKR {calculatedEMI.toLocaleString('en-US', { maximumFractionDigits: 2 })} × {numberOfPayments} payments</p>
                  </div>
                </div>
              )}

              {selectedLoanType?.interestCalculationMethod === 'COMPOUND_INTEREST' && (
                <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                  <h4 className="font-medium text-green-900 mb-2">Interest Calculation Method: Compound Interest (EMI)</h4>
                  <div className="text-sm text-green-800 space-y-1">
                    <p>• <strong>Formula:</strong> EMI = P × r × (1+r)^n / ((1+r)^n - 1)</p>
                    <p>• <strong>Method:</strong> Standard loan EMI calculation with reducing balance</p>
                    <p>• <strong>Total Interest:</strong> LKR {calculatedInterest.toLocaleString('en-US', { maximumFractionDigits: 2 })}</p>
                    <p>• <strong>{emiFrequencyLabel}:</strong> LKR {calculatedEMI.toLocaleString('en-US', { maximumFractionDigits: 2 })} × {numberOfPayments} payments</p>
                  </div>
                </div>
              )}

              {selectedCustomer && (
                <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-sm text-yellow-800">
                    <strong>Affordability Check:</strong> EMI is {((calculatedEMI / selectedCustomer.monthlyIncome) * 100).toFixed(1)}% of customer's monthly income
                    {calculatedEMI > selectedCustomer.monthlyIncome * 0.5 && (
                      <span className="text-red-600 font-medium"> (⚠️ Exceeds 50% threshold)</span>
                    )}
                  </p>
                </div>
              )}


            </CardContent>
          </Card>
        )}

        {/* Additional Information */}
        <Card>
          <CardHeader>
            <CardTitle>Additional Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="purpose">Loan Purpose *</Label>
              <Textarea
                id="purpose"
                value={formData.purpose}
                onChange={(e) => handleInputChange('purpose', e.target.value)}
                placeholder="Describe the purpose of the loan"
                className={errors.purpose ? 'border-red-500' : ''}
              />
              {errors.purpose && <p className="text-red-500 text-sm mt-1">{errors.purpose}</p>}
            </div>

            <div>
              <Label htmlFor="collateralDescription">Collateral Description</Label>
              <Textarea
                id="collateralDescription"
                value={formData.collateralDescription}
                onChange={(e) => handleInputChange('collateralDescription', e.target.value)}
                placeholder="Describe any collateral offered (optional)"
              />
            </div>

            <div>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder="Additional notes or comments"
              />
            </div>
          </CardContent>
        </Card>

        {/* Guarantor Information */}
        {selectedLoanType?.requiresGuarantor && (
          <GuarantorManagement
            customers={customers}
            selectedCustomerId={formData.customerId}
            maxGuarantors={selectedLoanType.maxGuarantors}
            guarantors={formData.guarantors}
            onGuarantorsChange={(guarantors) => setFormData(prev => ({ ...prev, guarantors }))}
            disabled={saving}
          />
        )}

        {errors.guarantors && (
          <div className="text-red-500 text-sm">
            {errors.guarantors}
          </div>
        )}

        {/* Document Upload */}
        <DocumentUpload
          documents={documents}
          onDocumentsChange={setDocuments}
          customerId={formData.customerId}
          disabled={saving}
          requiredDocuments={requiredDocuments}
        />

        {/* Submit Button */}
        <div className="flex justify-end gap-4">
          <Link href="/loans">
            <Button type="button" variant="outline">Cancel</Button>
          </Link>
          <Button type="submit" disabled={saving}>
            {saving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Create Loan Application
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}

export default function NewLoanPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <NewLoanContent />
    </Suspense>
  )
}
