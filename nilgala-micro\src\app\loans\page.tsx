'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Plus, 
  Search, 
  Eye, 
  Edit, 
  CreditCard,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react'
import Link from 'next/link'
import PageHeader from '@/components/layout/PageHeader'

interface Loan {
  id: string
  loanNumber: string
  principalAmount: number
  totalAmount: number
  emiAmount: number
  status: string
  applicationDate: string
  tenure: number
  interestRate: number
  repaymentFrequency: string
  createdAt: string
  customer: {
    id: string
    nationalId: string
    firstName: string
    lastName: string
    phone: string
  }
  loanType: {
    id: string
    name: string
    interestRate: number
  }
  _count: {
    payments: number
    paymentSchedules: number
  }
}

interface LoanResponse {
  loans: Loan[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

export default function LoansPage() {
  const { data: session } = useSession()
  const [loans, setLoans] = useState<Loan[]>([])
  const [loading, setLoading] = useState(true)
  const [search, setSearch] = useState('')
  const [statusFilter, setStatusFilter] = useState('ALL')
  const [assignmentFilter, setAssignmentFilter] = useState(
    session?.user?.role === 'CREDIT_OFFICER' ? 'ASSIGNED_TO_ME' : 'ALL'
  )
  const [currentPage, setCurrentPage] = useState(1)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  })
  const [stats, setStats] = useState({
    counts: {
      total: 0,
      pending: 0,
      active: 0,
      completed: 0
    }
  })

  const fetchLoans = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        ...(search && { search }),
        ...(statusFilter && statusFilter !== 'ALL' && { status: statusFilter }),
        ...(assignmentFilter && assignmentFilter !== 'ALL' && { assignmentFilter })
      })

      const response = await fetch(`/api/loans?${params}`)
      if (response.ok) {
        const data: LoanResponse = await response.json()
        setLoans(data.loans)
        setPagination(data.pagination)
      }
    } catch (error) {
      console.error('Error fetching loans:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/loans/stats')
      if (response.ok) {
        const data = await response.json()
        setStats(data)
      }
    } catch (error) {
      console.error('Error fetching loan stats:', error)
    }
  }

  useEffect(() => {
    fetchLoans()
    fetchStats()
  }, [currentPage, search, statusFilter, assignmentFilter])

  // Update assignment filter when session changes
  useEffect(() => {
    if (session?.user?.role === 'CREDIT_OFFICER' && assignmentFilter === 'ALL') {
      setAssignmentFilter('ASSIGNED_TO_ME')
    }
  }, [session])

  const getStatusBadge = (status: string) => {
    const variants = {
      DRAFT: 'secondary',
      PENDING_APPROVAL: 'secondary',
      APPROVED: 'default',
      REJECTED: 'destructive',
      DISBURSED: 'default',
      ACTIVE: 'default',
      COMPLETED: 'default',
      DEFAULTED: 'destructive',
      WRITTEN_OFF: 'destructive'
    } as const

    const colors = {
      DRAFT: 'bg-gray-100 text-gray-800',
      PENDING_APPROVAL: 'bg-yellow-100 text-yellow-800',
      APPROVED: 'bg-blue-100 text-blue-800',
      REJECTED: 'bg-red-100 text-red-800',
      DISBURSED: 'bg-green-100 text-green-800',
      ACTIVE: 'bg-green-100 text-green-800',
      COMPLETED: 'bg-gray-100 text-gray-800',
      DEFAULTED: 'bg-red-100 text-red-800',
      WRITTEN_OFF: 'bg-red-100 text-red-800'
    }

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {status}
      </Badge>
    )
  }



  return (
    <PageHeader
      title="Loan Management"
      description="Manage loan applications and disbursements"
      actions={
        <div className="flex gap-2">
          <Link href="/loans/pending">
            <Button variant="outline">
              <Clock className="h-4 w-4 mr-2" />
              Pending Approvals ({stats.counts.pending})
            </Button>
          </Link>
          <Link href="/loans/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Loan Application
            </Button>
          </Link>
        </div>
      }
    >

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Loans</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.counts.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.counts.pending}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.counts.active}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.counts.completed}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by loan ID, customer name, or purpose..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Status</SelectItem>
                <SelectItem value="PENDING_APPROVAL">Pending</SelectItem>
                <SelectItem value="APPROVED">Approved</SelectItem>
                <SelectItem value="REJECTED">Rejected</SelectItem>
                <SelectItem value="DISBURSED">Disbursed</SelectItem>
                <SelectItem value="ACTIVE">Active</SelectItem>
                <SelectItem value="COMPLETED">Completed</SelectItem>
                <SelectItem value="DEFAULTED">Defaulted</SelectItem>
              </SelectContent>
            </Select>

            {/* Assignment Filter - Show for Credit Officers */}
            {session?.user?.role === 'CREDIT_OFFICER' && (
              <Select value={assignmentFilter} onValueChange={setAssignmentFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Filter by assignment" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ASSIGNED_TO_ME">My Customer Loans</SelectItem>
                  <SelectItem value="ALL">All Customer Loans</SelectItem>
                </SelectContent>
              </Select>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Loans Table */}
      <Card>
        <CardHeader>
          <CardTitle>Loans</CardTitle>
          <CardDescription>
            {pagination.total} total loans
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">Loading loans...</div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Loan ID</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Loan Type</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Term</TableHead>
                    <TableHead>Interest Rate</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loans.map((loan) => (
                    <TableRow key={loan.id}>
                      <TableCell className="font-medium">
                        {loan.loanNumber}
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {loan.customer.firstName} {loan.customer.lastName}
                          </div>
                          <div className="text-sm text-gray-500">
                            {loan.customer.nationalId}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {loan.loanType.name}
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            LKR {Number(loan.principalAmount).toLocaleString()}
                          </div>
                          <div className="text-sm text-gray-600">
                            Total: LKR {Number(loan.totalAmount).toLocaleString()}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(loan.status)}
                      </TableCell>
                      <TableCell>
                        {loan.tenure} {loan.repaymentFrequency.toLowerCase()}
                      </TableCell>
                      <TableCell>
                        {loan.interestRate}%
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Link href={`/loans/${loan.id}`}>
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                          {(loan.status === 'PENDING_APPROVAL' || loan.status === 'DRAFT') && (
                            <Link href={`/loans/${loan.id}/approve`}>
                              <Button variant="outline" size="sm">
                                <Edit className="h-4 w-4" />
                              </Button>
                            </Link>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {pagination.pages > 1 && (
                <div className="flex justify-center gap-2 mt-4">
                  <Button
                    variant="outline"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="flex items-center px-4">
                    Page {currentPage} of {pagination.pages}
                  </span>
                  <Button
                    variant="outline"
                    onClick={() => setCurrentPage(Math.min(pagination.pages, currentPage + 1))}
                    disabled={currentPage === pagination.pages}
                  >
                    Next
                  </Button>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </PageHeader>
  )
}
