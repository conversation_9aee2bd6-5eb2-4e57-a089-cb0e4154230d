'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Search, 
  Eye, 
  Calendar,
  AlertTriangle,
  Clock,
  CheckCircle,
  DollarSign,
  TrendingDown
} from 'lucide-react'
import Link from 'next/link'
import PageHeader from '@/components/layout/PageHeader'

interface PaymentSchedule {
  id: string
  installmentNumber: number
  dueDate: string
  principalAmount: number
  interestAmount: number
  totalAmount: number
  paidAmount?: number
  status: string
  paidDate?: string
  overdueDays: number
  isOverdue: boolean
  loan: {
    id: string
    loanId: string
    customer: {
      id: string
      firstName: string
      lastName: string
      phone: string
      nationalId: string
    }
    loanType: {
      name: string
    }
  }
}

interface ScheduleResponse {
  schedules: PaymentSchedule[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

interface Summary {
  totalPending: number
  overdue: {
    count: number
    amount: number
  }
  upcoming: {
    week: {
      count: number
      amount: number
    }
    month: {
      count: number
      amount: number
    }
  }
  collected: {
    thisMonth: number
  }
}

export default function PaymentSchedulesPage() {
  const { data: session } = useSession()
  const [schedules, setSchedules] = useState<PaymentSchedule[]>([])
  const [summary, setSummary] = useState<Summary | null>(null)
  const [loading, setLoading] = useState(true)
  const [statusFilter, setStatusFilter] = useState('ALL')
  const [viewFilter, setViewFilter] = useState('ALL')
  const [currentPage, setCurrentPage] = useState(1)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  })

  const fetchSchedules = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        ...(statusFilter && statusFilter !== 'ALL' && { status: statusFilter }),
        ...(viewFilter === 'OVERDUE' && { overdue: 'true' }),
        ...(viewFilter === 'UPCOMING' && { upcoming: 'true' })
      })

      const response = await fetch(`/api/payment-schedules?${params}`)
      if (response.ok) {
        const data: ScheduleResponse = await response.json()
        setSchedules(data.schedules)
        setPagination(data.pagination)
      }
    } catch (error) {
      console.error('Error fetching payment schedules:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchSummary = async () => {
    try {
      const response = await fetch('/api/payment-schedules', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'summary' }),
      })
      if (response.ok) {
        const data = await response.json()
        setSummary(data)
      }
    } catch (error) {
      console.error('Error fetching summary:', error)
    }
  }

  useEffect(() => {
    fetchSchedules()
  }, [currentPage, statusFilter, viewFilter])

  useEffect(() => {
    fetchSummary()
  }, [])

  const getStatusBadge = (status: string, isOverdue: boolean) => {
    if (isOverdue && ['PENDING', 'PARTIAL'].includes(status)) {
      return (
        <Badge className="bg-red-100 text-red-800">
          OVERDUE
        </Badge>
      )
    }

    const variants = {
      PENDING: 'bg-yellow-100 text-yellow-800',
      PARTIAL: 'bg-orange-100 text-orange-800',
      PAID: 'bg-green-100 text-green-800',
      OVERDUE: 'bg-red-100 text-red-800'
    } as const

    return (
      <Badge className={variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800'}>
        {status}
      </Badge>
    )
  }

  return (
    <PageHeader
      title="Payment Schedules"
      description="Monitor upcoming and overdue payments"
      actions={
        <Link href="/payments">
          <Button variant="outline">
            <DollarSign className="h-4 w-4 mr-2" />
            View Payments
          </Button>
        </Link>
      }
    >

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Pending</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.totalPending}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Overdue</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{summary.overdue.count}</div>
              <div className="text-sm text-red-600">
                LKR {summary.overdue.amount.toLocaleString()}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Due This Week</CardTitle>
              <Calendar className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{summary.upcoming.week.count}</div>
              <div className="text-sm text-orange-600">
                LKR {summary.upcoming.week.amount.toLocaleString()}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Due This Month</CardTitle>
              <TrendingDown className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{summary.upcoming.month.count}</div>
              <div className="text-sm text-blue-600">
                LKR {summary.upcoming.month.amount.toLocaleString()}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Collected</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                LKR {summary.collected.thisMonth.toLocaleString()}
              </div>
              <div className="text-sm text-green-600">This month</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Filter & View</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Select value={viewFilter} onValueChange={setViewFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="View filter" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Schedules</SelectItem>
                <SelectItem value="OVERDUE">Overdue Only</SelectItem>
                <SelectItem value="UPCOMING">Upcoming (7 days)</SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Status filter" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Status</SelectItem>
                <SelectItem value="PENDING">Pending</SelectItem>
                <SelectItem value="PARTIAL">Partial</SelectItem>
                <SelectItem value="PAID">Paid</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Schedules Table */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Schedules</CardTitle>
          <CardDescription>
            {pagination.total} total schedules
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">Loading payment schedules...</div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer</TableHead>
                    <TableHead>Loan ID</TableHead>
                    <TableHead>Installment</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Paid</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {schedules.map((schedule) => (
                    <TableRow key={schedule.id} className={schedule.isOverdue ? 'bg-red-50' : ''}>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {schedule.loan.customer.firstName} {schedule.loan.customer.lastName}
                          </div>
                          <div className="text-sm text-gray-500">
                            {schedule.loan.customer.nationalId}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{schedule.loan.loanId}</div>
                          <div className="text-sm text-gray-500">
                            {schedule.loan.loanType.name}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        #{schedule.installmentNumber}
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className={schedule.isOverdue ? 'text-red-600 font-medium' : ''}>
                            {new Date(schedule.dueDate).toLocaleDateString()}
                          </div>
                          {schedule.isOverdue && (
                            <div className="text-sm text-red-600">
                              {schedule.overdueDays} days overdue
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            LKR {schedule.totalAmount.toLocaleString()}
                          </div>
                          <div className="text-sm text-gray-500">
                            P: {schedule.principalAmount.toLocaleString()} | 
                            I: {schedule.interestAmount.toLocaleString()}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {schedule.paidAmount ? (
                          <div className="text-green-600 font-medium">
                            LKR {schedule.paidAmount.toLocaleString()}
                          </div>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(schedule.status, schedule.isOverdue)}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Link href={`/loans/${schedule.loan.id}`}>
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                          {['PENDING', 'PARTIAL'].includes(schedule.status) && (
                            <Link href={`/payments/new?loanId=${schedule.loan.id}`}>
                              <Button size="sm">
                                Pay
                              </Button>
                            </Link>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {pagination.pages > 1 && (
                <div className="flex justify-center gap-2 mt-4">
                  <Button
                    variant="outline"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="flex items-center px-4">
                    Page {currentPage} of {pagination.pages}
                  </span>
                  <Button
                    variant="outline"
                    onClick={() => setCurrentPage(Math.min(pagination.pages, currentPage + 1))}
                    disabled={currentPage === pagination.pages}
                  >
                    Next
                  </Button>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </PageHeader>
  )
}
