'use client'

import { useState, useEffect, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { ArrowLeft, DollarSign, CreditCard, Calendar, FileText, Search, User, Banknote } from 'lucide-react'
import Link from 'next/link'
import PageHeader from '@/components/layout/PageHeader'
import { formatCurrency } from '@/lib/utils'

interface Loan {
  id: string
  loanNumber: string
  principalAmount: number
  totalAmount: number
  disbursedAmount: number
  outstandingAmount: number
  emiAmount: number
  status: string
  interestRate: number
  tenure: number
  repaymentFrequency: string
  customer: {
    id: string
    firstName: string
    lastName: string
    phone: string
    nationalId: string
  }
  loanType: {
    name: string
    interestRate: number
  }
}

interface FormData {
  loanId: string
  amount: number
  paymentMethod: string
  referenceNumber: string
  notes: string
  paymentDate: string
}

interface FormErrors {
  [key: string]: string
}

// Generate payment reference number
const generatePaymentReference = () => {
  const date = new Date()
  const year = date.getFullYear().toString().slice(-2)
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const time = date.getTime().toString().slice(-6)
  return `PAY${year}${month}${day}${time}`
}

// Calculate EMI amount
const calculateEMI = (principal: number, rate: number, tenure: number, frequency: string = 'MONTHLY') => {
  if (!principal || !rate || !tenure) return 0

  let periodsPerYear = 12
  if (frequency === 'WEEKLY') periodsPerYear = 52
  if (frequency === 'DAILY') periodsPerYear = 365

  const monthlyRate = rate / 100 / periodsPerYear
  const totalPeriods = tenure * (periodsPerYear / 12) // Convert tenure to periods

  if (monthlyRate === 0) return principal / totalPeriods

  const emi = (principal * monthlyRate * Math.pow(1 + monthlyRate, totalPeriods)) /
              (Math.pow(1 + monthlyRate, totalPeriods) - 1)

  return Math.round(emi * 100) / 100
}

function RecordPaymentContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { data: session } = useSession()
  const { toast } = useToast()

  const [loans, setLoans] = useState<Loan[]>([])
  const [selectedLoan, setSelectedLoan] = useState<Loan | null>(null)
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [errors, setErrors] = useState<FormErrors>({})

  const [formData, setFormData] = useState<FormData>({
    loanId: searchParams.get('loanId') || '',
    amount: 0,
    paymentMethod: '',
    referenceNumber: generatePaymentReference(),
    notes: '',
    paymentDate: new Date().toISOString().split('T')[0]
  })

  const paymentMethods = [
    { value: 'CASH', label: 'Cash' },
    { value: 'BANK_TRANSFER', label: 'Bank Transfer' },
    { value: 'CHEQUE', label: 'Cheque' },
    { value: 'ONLINE', label: 'Online Payment' },
    { value: 'MOBILE_PAYMENT', label: 'Mobile Payment' }
  ]

  useEffect(() => {
    fetchLoans()
  }, [])

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchTerm.length >= 2 || searchTerm.length === 0) {
        fetchLoans(searchTerm)
      }
    }, 500)

    return () => clearTimeout(timeoutId)
  }, [searchTerm])

  useEffect(() => {
    if (formData.loanId) {
      const loan = loans.find(l => l.id === formData.loanId)
      setSelectedLoan(loan || null)
      if (loan && formData.amount === 0) {
        setFormData(prev => ({ ...prev, amount: loan.emiAmount }))
      }
    }
  }, [formData.loanId, loans])

  // Handle payment method change and auto-generate reference if needed
  const handlePaymentMethodChange = (method: string) => {
    let newReferenceNumber = formData.referenceNumber

    // Auto-generate reference for specific payment methods if empty
    if ((method === 'BANK_TRANSFER' || method === 'CHEQUE' || method === 'ONLINE') && !formData.referenceNumber) {
      newReferenceNumber = generatePaymentReference()
    }

    setFormData(prev => ({
      ...prev,
      paymentMethod: method,
      referenceNumber: newReferenceNumber
    }))
  }

  const fetchLoans = async (searchQuery = '') => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (searchQuery) {
        params.append('search', searchQuery)
      }

      const response = await fetch(`/api/loans/active?${params}`)
      if (response.ok) {
        const data = await response.json()
        setLoans(data.loans || [])
      } else {
        toast({
          title: 'Error',
          description: 'Failed to fetch active loans',
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error fetching loans:', error)
      toast({
        title: 'Error',
        description: 'Failed to fetch active loans',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const validateForm = () => {
    const newErrors: FormErrors = {}

    if (!formData.loanId) {
      newErrors.loanId = 'Please select a loan'
    }

    if (!formData.amount || formData.amount <= 0) {
      newErrors.amount = 'Amount must be greater than 0'
    }

    if (selectedLoan && formData.amount > selectedLoan.outstandingAmount) {
      newErrors.amount = 'Amount cannot exceed outstanding balance'
    }

    if (!formData.paymentMethod) {
      newErrors.paymentMethod = 'Please select a payment method'
    }

    if (!formData.paymentDate) {
      newErrors.paymentDate = 'Payment date is required'
    }

    if (formData.paymentMethod === 'CHEQUE' && !formData.referenceNumber) {
      newErrors.referenceNumber = 'Cheque number is required'
    }

    if (formData.paymentMethod === 'BANK_TRANSFER' && !formData.referenceNumber) {
      newErrors.referenceNumber = 'Transaction reference is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors below",
        variant: "destructive"
      })
      return
    }

    try {
      setSaving(true)
      
      const response = await fetch('/api/payments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          loanId: formData.loanId,
          amount: formData.amount,
          paymentMethod: formData.paymentMethod,
          referenceNumber: formData.referenceNumber || undefined,
          notes: formData.notes || undefined,
          paymentDate: formData.paymentDate
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to record payment')
      }

      const payment = await response.json()

      toast({
        title: "Success",
        description: "Payment recorded successfully",
      })

      router.push('/payments')
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive"
      })
    } finally {
      setSaving(false)
    }
  }

  // Use loans directly since we're doing server-side search
  const filteredLoans = loans

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-100 text-green-800'
      case 'OVERDUE': return 'bg-red-100 text-red-800'
      case 'COMPLETED': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <PageHeader
      title="Record Payment"
      description="Record a loan payment from a customer"
      actions={
        <Link href="/payments">
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Payments
          </Button>
        </Link>
      }
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Loan Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Select Loan
            </CardTitle>
            <CardDescription>
              Choose the loan for which you want to record a payment
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Search Loans</Label>
              <Input
                placeholder="Search by loan ID, customer name, phone, or national ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="mb-3"
              />
            </div>

            <div>
              <Label>Select Loan *</Label>
              <Select
                value={formData.loanId}
                onValueChange={(value) => setFormData(prev => ({ ...prev, loanId: value }))}
              >
                <SelectTrigger className={errors.loanId ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select a loan" />
                </SelectTrigger>
                <SelectContent>
                  {loading ? (
                    <SelectItem value="loading" disabled>
                      Loading loans...
                    </SelectItem>
                  ) : filteredLoans.length === 0 ? (
                    <SelectItem value="no-loans" disabled>
                      {searchTerm ? 'No loans found matching your search' : 'No active loans available'}
                    </SelectItem>
                  ) : (
                    filteredLoans.map((loan) => (
                      <SelectItem key={loan.id} value={loan.id}>
                        <div className="flex items-center justify-between w-full">
                          <div>
                            <div className="font-medium">
                              {loan.loanNumber} - {loan.customer.firstName} {loan.customer.lastName}
                            </div>
                            <div className="text-sm text-gray-500">
                              {loan.customer.phone} | Outstanding: LKR {loan.outstandingAmount.toLocaleString()}
                            </div>
                          </div>
                          <Badge className={getStatusBadgeColor(loan.status)}>
                            {loan.status}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              {errors.loanId && <p className="text-red-500 text-sm mt-1">{errors.loanId}</p>}
            </div>

            {selectedLoan && (
              <Card className="bg-blue-50 border-blue-200">
                <CardContent className="pt-4">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="font-semibold text-lg">Loan Details</h3>
                      <Badge className={getStatusBadgeColor(selectedLoan.status)}>
                        {selectedLoan.status}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-600">Customer:</span>
                        <p className="font-semibold">{selectedLoan.customer.firstName} {selectedLoan.customer.lastName}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-600">Phone:</span>
                        <p className="font-semibold">{selectedLoan.customer.phone}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-600">National ID:</span>
                        <p className="font-semibold">{selectedLoan.customer.nationalId}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-600">Loan Type:</span>
                        <p className="font-semibold">{selectedLoan.loanType.name}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-600">Principal Amount:</span>
                        <p className="font-semibold">{formatCurrency(selectedLoan.disbursedAmount || selectedLoan.principalAmount)}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-600">Interest Rate:</span>
                        <p className="font-semibold">{selectedLoan.interestRate || selectedLoan.loanType.interestRate || 0}%</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-600">EMI Amount:</span>
                        <p className="font-semibold text-green-600">
                          {formatCurrency(selectedLoan.emiAmount || calculateEMI(
                            selectedLoan.disbursedAmount || selectedLoan.principalAmount,
                            selectedLoan.interestRate || selectedLoan.loanType.interestRate || 0,
                            selectedLoan.tenure || 12,
                            selectedLoan.repaymentFrequency || 'MONTHLY'
                          ))}
                        </p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-600">Outstanding Balance:</span>
                        <p className="font-semibold text-red-600">LKR {selectedLoan.outstandingAmount.toLocaleString()}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-600">Repayment Frequency:</span>
                        <p className="font-semibold">{selectedLoan.repaymentFrequency || 'Monthly'}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </CardContent>
        </Card>

        {/* Payment Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Payment Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="amount">Payment Amount (LKR) *</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  value={formData.amount || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}
                  className={errors.amount ? 'border-red-500' : ''}
                  placeholder="Enter payment amount"
                />
                {errors.amount && <p className="text-red-500 text-sm mt-1">{errors.amount}</p>}
                {selectedLoan && (
                  <div className="text-sm text-gray-500 mt-1 space-y-1">
                    <p>Suggested EMI: LKR {(selectedLoan.emiAmount || calculateEMI(
                      selectedLoan.disbursedAmount || selectedLoan.principalAmount,
                      selectedLoan.interestRate || selectedLoan.loanType.interestRate || 0,
                      selectedLoan.tenure || 12,
                      selectedLoan.repaymentFrequency || 'MONTHLY'
                    )).toLocaleString()}</p>
                    <p>Outstanding: LKR {selectedLoan.outstandingAmount.toLocaleString()}</p>
                  </div>
                )}
              </div>

              <div>
                <Label htmlFor="paymentDate">Payment Date *</Label>
                <Input
                  id="paymentDate"
                  type="date"
                  value={formData.paymentDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, paymentDate: e.target.value }))}
                  className={errors.paymentDate ? 'border-red-500' : ''}
                />
                {errors.paymentDate && <p className="text-red-500 text-sm mt-1">{errors.paymentDate}</p>}
              </div>
            </div>

            <div>
              <Label>Payment Method *</Label>
              <Select
                value={formData.paymentMethod}
                onValueChange={handlePaymentMethodChange}
              >
                <SelectTrigger className={errors.paymentMethod ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                <SelectContent>
                  {paymentMethods.map((method) => (
                    <SelectItem key={method.value} value={method.value}>
                      {method.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.paymentMethod && <p className="text-red-500 text-sm mt-1">{errors.paymentMethod}</p>}
            </div>

            <div>
              <Label htmlFor="referenceNumber">
                Reference Number
                {(formData.paymentMethod === 'CHEQUE' || formData.paymentMethod === 'BANK_TRANSFER') && ' *'}
              </Label>
              <Input
                id="referenceNumber"
                value={formData.referenceNumber}
                onChange={(e) => setFormData(prev => ({ ...prev, referenceNumber: e.target.value }))}
                className={errors.referenceNumber ? 'border-red-500' : ''}
                placeholder={
                  formData.paymentMethod === 'CHEQUE' ? 'Enter cheque number' :
                  formData.paymentMethod === 'BANK_TRANSFER' ? 'Enter transaction reference' :
                  'Enter reference number (optional)'
                }
              />
              {errors.referenceNumber && <p className="text-red-500 text-sm mt-1">{errors.referenceNumber}</p>}
            </div>

            <div>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Add any additional notes about this payment..."
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end gap-4">
          <Link href="/payments">
            <Button type="button" variant="outline" disabled={saving}>
              Cancel
            </Button>
          </Link>
          <Button type="submit" disabled={saving}>
            {saving ? 'Recording...' : 'Record Payment'}
          </Button>
        </div>
      </form>
    </PageHeader>
  )
}

export default function RecordPaymentPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <RecordPaymentContent />
    </Suspense>
  )
}
