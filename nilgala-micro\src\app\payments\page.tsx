'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Plus, 
  Search, 
  Eye, 
  DollarSign,
  TrendingUp,
  Clock,
  AlertTriangle,
  Calendar
} from 'lucide-react'
import Link from 'next/link'
import PageHeader from '@/components/layout/PageHeader'
import { formatCurrency } from '@/lib/utils'

interface Payment {
  id: string
  amount: number | any // Handle Prisma Decimal objects
  paymentMethod: string
  referenceNumber?: string
  paymentDate: string
  notes?: string
  loan: {
    id: string
    loanNumber: string
    customer: {
      id: string
      nationalId: string
      firstName: string
      lastName: string
      phone: string
    }
    loanType: {
      name: string
    }
  }
  createdByUser?: {
    firstName: string
    lastName: string
  }
}

interface PaymentResponse {
  payments: Payment[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

export default function PaymentsPage() {
  const { data: session } = useSession()
  const [payments, setPayments] = useState<Payment[]>([])
  const [loading, setLoading] = useState(true)
  const [search, setSearch] = useState('')
  const [methodFilter, setMethodFilter] = useState('ALL')
  const [currentPage, setCurrentPage] = useState(1)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  })

  const fetchPayments = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        ...(search && { search }),
        ...(methodFilter && methodFilter !== 'ALL' && { paymentMethod: methodFilter })
      })

      const response = await fetch(`/api/payments?${params}`)
      if (response.ok) {
        const data: PaymentResponse = await response.json()
        setPayments(data.payments)
        setPagination(data.pagination)
      }
    } catch (error) {
      console.error('Error fetching payments:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPayments()
  }, [currentPage, search, methodFilter])

  const getMethodBadge = (method: string) => {
    const colors = {
      CASH: 'bg-green-100 text-green-800',
      BANK_TRANSFER: 'bg-blue-100 text-blue-800',
      CHEQUE: 'bg-purple-100 text-purple-800',
      ONLINE: 'bg-orange-100 text-orange-800',
      MOBILE_PAYMENT: 'bg-pink-100 text-pink-800'
    }

    return (
      <Badge className={colors[method as keyof typeof colors] || 'bg-gray-100 text-gray-800'}>
        {method.replace('_', ' ')}
      </Badge>
    )
  }

  // Calculate summary statistics
  const stats = {
    total: pagination.total,
    totalAmount: payments.reduce((sum, payment) => {
      // Convert Decimal to number properly
      const amount = typeof payment.amount === 'object' && payment.amount.toNumber
        ? payment.amount.toNumber()
        : Number(payment.amount)
      return sum + amount
    }, 0),
    todayPayments: payments.filter(p =>
      new Date(p.paymentDate).toDateString() === new Date().toDateString()
    ).length,
    avgPayment: payments.length > 0 ?
      payments.reduce((sum, payment) => {
        // Convert Decimal to number properly
        const amount = typeof payment.amount === 'object' && payment.amount.toNumber
          ? payment.amount.toNumber()
          : Number(payment.amount)
        return sum + amount
      }, 0) / payments.length : 0
  }

  return (
    <PageHeader
      title="Payment Management"
      description="Track and manage loan payments"
      actions={
        <div className="flex gap-2">
          <Link href="/payment-schedules">
            <Button variant="outline">
              <Calendar className="h-4 w-4 mr-2" />
              Payment Schedules
            </Button>
          </Link>
          <Link href="/payments/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Record Payment
            </Button>
          </Link>
        </div>
      }
    >

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Payments</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(stats.totalAmount)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Payments</CardTitle>
            <Clock className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.todayPayments}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Payment</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {formatCurrency(Math.round(stats.avgPayment))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by reference number, customer name, or loan ID..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={methodFilter} onValueChange={setMethodFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by method" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Methods</SelectItem>
                <SelectItem value="CASH">Cash</SelectItem>
                <SelectItem value="BANK_TRANSFER">Bank Transfer</SelectItem>
                <SelectItem value="CHEQUE">Cheque</SelectItem>
                <SelectItem value="ONLINE">Online</SelectItem>
                <SelectItem value="MOBILE_PAYMENT">Mobile Payment</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Payments Table */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Payments</CardTitle>
          <CardDescription>
            {pagination.total} total payments
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">Loading payments...</div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Loan ID</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Method</TableHead>
                    <TableHead>Reference</TableHead>
                    <TableHead>Recorded By</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {payments.map((payment) => (
                    <TableRow key={payment.id}>
                      <TableCell>
                        <div>
                          <div>{new Date(payment.paymentDate).toLocaleDateString()}</div>
                          <div className="text-sm text-gray-500">
                            {new Date(payment.paymentDate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {payment.loan.customer.firstName} {payment.loan.customer.lastName}
                          </div>
                          <div className="text-sm text-gray-500">
                            {payment.loan.customer.customerId}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{payment.loan.loanNumber}</div>
                          <div className="text-sm text-gray-500">
                            {payment.loan.loanType.name}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium text-green-600">
                          {formatCurrency(payment.amount)}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getMethodBadge(payment.paymentMethod)}
                      </TableCell>
                      <TableCell>
                        {payment.referenceNumber || '-'}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {payment.createdByUser ?
                            `${payment.createdByUser.firstName} ${payment.createdByUser.lastName}` :
                            'System'
                          }
                        </div>
                      </TableCell>
                      <TableCell>
                        <Link href={`/payments/${payment.id}`}>
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </Link>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {pagination.pages > 1 && (
                <div className="flex justify-center gap-2 mt-4">
                  <Button
                    variant="outline"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="flex items-center px-4">
                    Page {currentPage} of {pagination.pages}
                  </span>
                  <Button
                    variant="outline"
                    onClick={() => setCurrentPage(Math.min(pagination.pages, currentPage + 1))}
                    disabled={currentPage === pagination.pages}
                  >
                    Next
                  </Button>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </PageHeader>
  )
}
