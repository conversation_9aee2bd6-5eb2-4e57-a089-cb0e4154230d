'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import <PERSON>Header from '@/components/layout/PageHeader'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Target,
  TrendingUp,
  TrendingDown,
  DollarSign,
  FileText,
  Users,
  Calendar,
  Award,
  BarChart3,
  PieChart,
  Activity
} from 'lucide-react'

interface PerformanceData {
  currentMonth: {
    target: number
    achievement: number
    percentage: number
    rank: number
    totalOfficers: number
  }
  monthlyHistory: Array<{
    month: string
    target: number
    achievement: number
    percentage: number
  }>
  yearlyStats: {
    totalTarget: number
    totalAchievement: number
    averagePercentage: number
    bestMonth: string
    bestPercentage: number
  }
  loanMetrics: {
    totalLoans: number
    activeLoans: number
    completedLoans: number
    averageLoanAmount: number
    totalDisbursed: number
  }
  customerMetrics: {
    assignedCustomers: number
    newCustomers: number
    retentionRate: number
    satisfactionScore: number
  }
}

export default function PerformancePage() {
  const { data: session } = useSession()
  const [performanceData, setPerformanceData] = useState<PerformanceData | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear().toString())
  const [selectedPeriod, setSelectedPeriod] = useState('monthly')

  useEffect(() => {
    if (session?.user) {
      fetchPerformanceData()
    }
  }, [session, selectedYear, selectedPeriod])

  const fetchPerformanceData = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/performance?year=${selectedYear}&period=${selectedPeriod}`)
      if (response.ok) {
        const data = await response.json()
        setPerformanceData(data)
      }
    } catch (error) {
      console.error('Error fetching performance data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getPerformanceBadge = (percentage: number) => {
    if (percentage >= 100) return <Badge className="bg-green-100 text-green-800">Excellent</Badge>
    if (percentage >= 80) return <Badge className="bg-blue-100 text-blue-800">Good</Badge>
    if (percentage >= 60) return <Badge className="bg-yellow-100 text-yellow-800">Average</Badge>
    return <Badge className="bg-red-100 text-red-800">Needs Improvement</Badge>
  }

  const getTrendIcon = (current: number, previous: number) => {
    if (current > previous) return <TrendingUp className="h-4 w-4 text-green-600" />
    if (current < previous) return <TrendingDown className="h-4 w-4 text-red-600" />
    return <Activity className="h-4 w-4 text-gray-600" />
  }

  if (loading) {
    return (
      <PageHeader title="My Performance" description="Track your performance metrics and achievements">
        <div className="text-center py-8">Loading performance data...</div>
      </PageHeader>
    )
  }

  return (
    <PageHeader 
      title="My Performance" 
      description="Track your performance metrics and achievements"
      actions={
        <div className="flex gap-2">
          <Select value={selectedYear} onValueChange={setSelectedYear}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="2024">2024</SelectItem>
              <SelectItem value="2023">2023</SelectItem>
              <SelectItem value="2022">2022</SelectItem>
            </SelectContent>
          </Select>
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
              <SelectItem value="yearly">Yearly</SelectItem>
            </SelectContent>
          </Select>
        </div>
      }
    >
      <div className="space-y-6">
        {/* Current Performance Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Current Target</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                LKR {performanceData?.currentMonth.target.toLocaleString() || '0'}
              </div>
              <p className="text-xs text-muted-foreground">Monthly target</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Achievement</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                LKR {performanceData?.currentMonth.achievement.toLocaleString() || '0'}
              </div>
              <p className="text-xs text-muted-foreground">This month</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Achievement Rate</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {performanceData?.currentMonth.percentage.toFixed(1) || '0'}%
              </div>
              <div className="mt-2">
                {performanceData && getPerformanceBadge(performanceData.currentMonth.percentage)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Team Rank</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                #{performanceData?.currentMonth.rank || 'N/A'}
              </div>
              <p className="text-xs text-muted-foreground">
                of {performanceData?.currentMonth.totalOfficers || 0} officers
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Performance Tabs */}
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
            <TabsTrigger value="loans">Loan Metrics</TabsTrigger>
            <TabsTrigger value="customers">Customer Metrics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* Monthly Progress */}
            <Card>
              <CardHeader>
                <CardTitle>Monthly Progress</CardTitle>
                <CardDescription>Your performance progress this month</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Target Achievement</span>
                    <span className="text-sm text-muted-foreground">
                      {performanceData?.currentMonth.percentage.toFixed(1) || '0'}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                      style={{ 
                        width: `${Math.min(performanceData?.currentMonth.percentage || 0, 100)}%` 
                      }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="font-medium">
                      LKR {performanceData?.currentMonth.achievement.toLocaleString() || '0'}
                    </span>
                    <span className="text-muted-foreground">
                      LKR {performanceData?.currentMonth.target.toLocaleString() || '0'} target
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Yearly Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Yearly Summary ({selectedYear})</CardTitle>
                <CardDescription>Your overall performance this year</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      LKR {performanceData?.yearlyStats.totalAchievement.toLocaleString() || '0'}
                    </div>
                    <div className="text-sm text-gray-600">Total Achievement</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {performanceData?.yearlyStats.averagePercentage.toFixed(1) || '0'}%
                    </div>
                    <div className="text-sm text-gray-600">Average Performance</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">
                      {performanceData?.yearlyStats.bestPercentage.toFixed(1) || '0'}%
                    </div>
                    <div className="text-sm text-gray-600">
                      Best Month ({performanceData?.yearlyStats.bestMonth || 'N/A'})
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="trends" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Performance Trends</CardTitle>
                <CardDescription>Monthly performance over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {performanceData?.monthlyHistory.map((month, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <div className="font-medium">{month.month}</div>
                        <div className="text-sm text-gray-600">
                          LKR {month.achievement.toLocaleString()} / LKR {month.target.toLocaleString()}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold">{month.percentage.toFixed(1)}%</div>
                        {getPerformanceBadge(month.percentage)}
                      </div>
                    </div>
                  )) || <div className="text-center py-8 text-gray-500">No data available</div>}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="loans" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Loans</CardTitle>
                  <FileText className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {performanceData?.loanMetrics.totalLoans || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">All time</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Loans</CardTitle>
                  <Activity className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">
                    {performanceData?.loanMetrics.activeLoans || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">Currently active</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Average Loan</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    LKR {performanceData?.loanMetrics.averageLoanAmount.toLocaleString() || '0'}
                  </div>
                  <p className="text-xs text-muted-foreground">Per loan</p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="customers" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Assigned Customers</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {performanceData?.customerMetrics.assignedCustomers || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">Total portfolio</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">New Customers</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600">
                    {performanceData?.customerMetrics.newCustomers || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">This month</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Retention Rate</CardTitle>
                  <Award className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">
                    {performanceData?.customerMetrics.retentionRate.toFixed(1) || '0'}%
                  </div>
                  <p className="text-xs text-muted-foreground">Customer retention</p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </PageHeader>
  )
}
