'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { 
  Database, 
  Download, 
  Trash2, 
  Refresh<PERSON><PERSON>, 
  Upload, 
  <PERSON><PERSON><PERSON><PERSON>gle,
  CheckCircle,
  Clock,
  HardDrive
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface DatabaseBackup {
  id: string
  filename: string
  originalName: string
  fileSize: string // Changed from bigint to string for JSON serialization
  backupType: string
  status: string
  filePath: string
  createdAt: string
  completedAt: string | null
  errorMessage: string | null
  description: string | null
  user: {
    firstName: string
    lastName: string
    email: string
  }
}

export default function BackupManager() {
  const [backups, setBackups] = useState<DatabaseBackup[]>([])
  const [loading, setLoading] = useState(true)
  const [creating, setCreating] = useState(false)
  const [restoring, setRestoring] = useState(false)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showRestoreDialog, setShowRestoreDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [selectedBackup, setSelectedBackup] = useState<DatabaseBackup | null>(null)
  const [description, setDescription] = useState('')
  const { toast } = useToast()

  useEffect(() => {
    fetchBackups()
    // Auto-refresh every 30 seconds to update backup status
    const interval = setInterval(fetchBackups, 30000)
    return () => clearInterval(interval)
  }, [])

  const fetchBackups = async () => {
    try {
      const response = await fetch('/api/admin/backups')
      if (response.ok) {
        const data = await response.json()
        setBackups(data)
      } else {
        throw new Error('Failed to fetch backups')
      }
    } catch (error) {
      console.error('Error fetching backups:', error)
      toast({
        title: 'Error',
        description: 'Failed to load backups',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const createBackup = async () => {
    try {
      setCreating(true)
      const response = await fetch('/api/admin/backups', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          description: description.trim() || undefined,
          backupType: 'MANUAL'
        })
      })

      if (response.ok) {
        const newBackup = await response.json()
        setBackups(prev => [newBackup, ...prev])
        setShowCreateDialog(false)
        setDescription('')
        toast({
          title: 'Success',
          description: 'Backup creation started. Please wait for completion.'
        })
      } else {
        throw new Error('Failed to create backup')
      }
    } catch (error) {
      console.error('Error creating backup:', error)
      toast({
        title: 'Error',
        description: 'Failed to create backup',
        variant: 'destructive'
      })
    } finally {
      setCreating(false)
    }
  }

  const downloadBackup = async (backup: DatabaseBackup) => {
    try {
      const response = await fetch(`/api/admin/backups/${backup.id}`)
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = backup.originalName
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        
        toast({
          title: 'Success',
          description: 'Backup downloaded successfully'
        })
      } else {
        throw new Error('Failed to download backup')
      }
    } catch (error) {
      console.error('Error downloading backup:', error)
      toast({
        title: 'Error',
        description: 'Failed to download backup',
        variant: 'destructive'
      })
    }
  }

  const restoreBackup = async () => {
    if (!selectedBackup) return

    try {
      setRestoring(true)
      const response = await fetch(`/api/admin/backups/${selectedBackup.id}/restore`, {
        method: 'POST'
      })

      if (response.ok) {
        setShowRestoreDialog(false)
        setSelectedBackup(null)
        toast({
          title: 'Success',
          description: 'Database restored successfully. Please refresh the page.'
        })
      } else {
        const error = await response.json()
        throw new Error(error.details || 'Failed to restore backup')
      }
    } catch (error) {
      console.error('Error restoring backup:', error)
      toast({
        title: 'Error',
        description: error.message || 'Failed to restore backup',
        variant: 'destructive'
      })
    } finally {
      setRestoring(false)
    }
  }

  const deleteBackup = async () => {
    if (!selectedBackup) return

    try {
      const response = await fetch(`/api/admin/backups/${selectedBackup.id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setBackups(prev => prev.filter(b => b.id !== selectedBackup.id))
        setShowDeleteDialog(false)
        setSelectedBackup(null)
        toast({
          title: 'Success',
          description: 'Backup deleted successfully'
        })
      } else {
        throw new Error('Failed to delete backup')
      }
    } catch (error) {
      console.error('Error deleting backup:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete backup',
        variant: 'destructive'
      })
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Completed</Badge>
      case 'CREATING':
        return <Badge className="bg-blue-100 text-blue-800"><Clock className="h-3 w-3 mr-1" />Creating</Badge>
      case 'FAILED':
        return <Badge className="bg-red-100 text-red-800"><AlertTriangle className="h-3 w-3 mr-1" />Failed</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const formatFileSize = (bytes: string) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const numBytes = parseInt(bytes)
    if (numBytes === 0) return '0 Bytes'
    const i = Math.floor(Math.log(numBytes) / Math.log(1024))
    return Math.round(numBytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Database Backup Management
          </CardTitle>
          <CardDescription>
            Create, manage, and restore database backups
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center gap-2">
              <HardDrive className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">
                {backups.length} backup{backups.length !== 1 ? 's' : ''} available
              </span>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={fetchBackups}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button
                onClick={() => setShowCreateDialog(true)}
                disabled={creating}
              >
                <Database className="h-4 w-4 mr-2" />
                Create Backup
              </Button>
            </div>
          </div>

          {loading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
              <p className="text-gray-500">Loading backups...</p>
            </div>
          ) : backups.length === 0 ? (
            <div className="text-center py-8">
              <Database className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p className="text-gray-500 mb-2">No backups found</p>
              <p className="text-sm text-gray-400">Create your first backup to get started</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Filename</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Size</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Created By</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {backups.map((backup) => (
                  <TableRow key={backup.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{backup.originalName}</div>
                        {backup.description && (
                          <div className="text-sm text-gray-500">{backup.description}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(backup.status)}</TableCell>
                    <TableCell>{formatFileSize(backup.fileSize)}</TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>{new Date(backup.createdAt).toLocaleDateString()}</div>
                        <div className="text-gray-500">
                          {new Date(backup.createdAt).toLocaleTimeString()}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>{backup.user.firstName} {backup.user.lastName}</div>
                        <div className="text-gray-500">{backup.user.email}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        {backup.status === 'COMPLETED' && (
                          <>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => downloadBackup(backup)}
                            >
                              <Download className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                setSelectedBackup(backup)
                                setShowRestoreDialog(true)
                              }}
                            >
                              <Upload className="h-3 w-3" />
                            </Button>
                          </>
                        )}
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setSelectedBackup(backup)
                            setShowDeleteDialog(true)
                          }}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Create Backup Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Database Backup</DialogTitle>
            <DialogDescription>
              Create a complete backup of the database. This may take several minutes.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                placeholder="Enter a description for this backup..."
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCreateDialog(false)}
              disabled={creating}
            >
              Cancel
            </Button>
            <Button onClick={createBackup} disabled={creating}>
              {creating ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Database className="h-4 w-4 mr-2" />
                  Create Backup
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Restore Confirmation Dialog */}
      <AlertDialog open={showRestoreDialog} onOpenChange={setShowRestoreDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              Restore Database
            </AlertDialogTitle>
            <AlertDialogDescription asChild>
              <div className="space-y-2">
                <div>
                  <strong>WARNING:</strong> This will completely replace the current database with the backup data.
                </div>
                <div>All current data will be lost. This action cannot be undone.</div>
                {selectedBackup && (
                  <div className="mt-4 p-3 bg-gray-50 rounded">
                    <div><strong>Backup:</strong> {selectedBackup.originalName}</div>
                    <div><strong>Created:</strong> {new Date(selectedBackup.createdAt).toLocaleString()}</div>
                    <div><strong>Size:</strong> {formatFileSize(selectedBackup.fileSize)}</div>
                  </div>
                )}
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={restoring}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={restoreBackup}
              disabled={restoring}
              className="bg-red-600 hover:bg-red-700"
            >
              {restoring ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Restoring...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  Restore Database
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Backup</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this backup? This action cannot be undone.
              {selectedBackup && (
                <div className="mt-2 p-2 bg-gray-50 rounded">
                  <strong>{selectedBackup.originalName}</strong>
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={deleteBackup}
              className="bg-red-600 hover:bg-red-700"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Backup
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
