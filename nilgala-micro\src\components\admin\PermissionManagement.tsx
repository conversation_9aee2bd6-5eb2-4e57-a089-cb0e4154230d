'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Shield, Save, History, AlertTriangle } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface PermissionData {
  allPermissions: Record<string, string[]>
  currentPermissions: Record<string, string[]>
  roles: string[]
}

interface AuditLog {
  id: string
  action: string
  resource: string
  resourceId: string | null
  timestamp: string
  oldValues: any
  newValues: any
  user: {
    firstName: string
    lastName: string
    email: string
  }
}

export default function PermissionManagement() {
  const [permissionData, setPermissionData] = useState<PermissionData | null>(null)
  const [selectedRole, setSelectedRole] = useState('')
  const [rolePermissions, setRolePermissions] = useState<string[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([])
  const [auditLoading, setAuditLoading] = useState(false)

  const { toast } = useToast()

  const roleLabels: Record<string, string> = {
    'SUPER_ADMIN': 'Super Admin',
    'HIGHER_MANAGEMENT': 'Higher Management',
    'MANAGER': 'Manager',
    'CREDIT_OFFICER': 'Credit Officer',
    'CUSTOMER_SERVICE_OFFICER': 'Customer Service Officer'
  }

  const permissionLabels: Record<string, string> = {
    'users': 'User Management',
    'customers': 'Customer Management',
    'loans': 'Loan Management',
    'payments': 'Payment Management',
    'reports': 'Reports & Analytics',
    'settings': 'System Settings',
    'audit': 'Audit Logs',
    'documents': 'Document Management'
  }

  const actionLabels: Record<string, string> = {
    'create': 'Create',
    'read': 'View',
    'update': 'Edit',
    'delete': 'Delete',
    'approve': 'Approve',
    'disburse': 'Disburse',
    'export': 'Export'
  }

  useEffect(() => {
    fetchPermissionData()
    fetchAuditLogs()
  }, [])

  useEffect(() => {
    if (selectedRole && permissionData) {
      setRolePermissions(permissionData.currentPermissions[selectedRole] || [])
    }
  }, [selectedRole, permissionData])

  const fetchPermissionData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/permissions')
      if (!response.ok) throw new Error('Failed to fetch permissions')

      const data = await response.json()
      setPermissionData(data)
      
      if (data.roles.length > 0 && !selectedRole) {
        setSelectedRole(data.roles[0])
      }
    } catch (error) {
      console.error('Error fetching permissions:', error)
      toast({
        title: 'Error',
        description: 'Failed to fetch permission data',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchAuditLogs = async () => {
    try {
      setAuditLoading(true)
      const response = await fetch('/api/admin/permissions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'PERMISSIONS_UPDATED', page: 1, limit: 10 })
      })
      
      if (!response.ok) throw new Error('Failed to fetch audit logs')

      const data = await response.json()
      setAuditLogs(data.logs)
    } catch (error) {
      console.error('Error fetching audit logs:', error)
    } finally {
      setAuditLoading(false)
    }
  }

  const handlePermissionChange = (permission: string, checked: boolean) => {
    if (checked) {
      setRolePermissions(prev => [...prev, permission])
    } else {
      setRolePermissions(prev => prev.filter(p => p !== permission))
    }
  }

  const handleSavePermissions = async () => {
    if (!selectedRole) return

    try {
      setSaving(true)
      const response = await fetch('/api/admin/permissions', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          role: selectedRole,
          permissions: rolePermissions
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update permissions')
      }

      toast({
        title: 'Success',
        description: `Permissions updated for ${roleLabels[selectedRole]}`,
      })

      // Refresh data
      await fetchPermissionData()
      await fetchAuditLogs()
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive'
      })
    } finally {
      setSaving(false)
    }
  }

  const formatPermissionName = (permission: string) => {
    const [module, action] = permission.split(':')
    return `${permissionLabels[module] || module} - ${actionLabels[action] || action}`
  }

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'SUPER_ADMIN': return 'bg-red-100 text-red-800'
      case 'HIGHER_MANAGEMENT': return 'bg-purple-100 text-purple-800'
      case 'MANAGER': return 'bg-blue-100 text-blue-800'
      case 'CREDIT_OFFICER': return 'bg-green-100 text-green-800'
      case 'CUSTOMER_SERVICE_OFFICER': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Shield className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading permission data...</p>
        </div>
      </div>
    )
  }

  if (!permissionData) {
    return (
      <div className="text-center py-8">
        <AlertTriangle className="h-8 w-8 mx-auto mb-4 text-yellow-500" />
        <p>Failed to load permission data</p>
        <Button onClick={fetchPermissionData} className="mt-4">Retry</Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Permission Management</h2>
          <p className="text-muted-foreground">Configure role-based permissions for system access</p>
        </div>
      </div>

      <Tabs defaultValue="permissions" className="space-y-4">
        <TabsList>
          <TabsTrigger value="permissions">Role Permissions</TabsTrigger>
          <TabsTrigger value="audit">Audit Trail</TabsTrigger>
        </TabsList>

        <TabsContent value="permissions" className="space-y-4">
          {/* Role Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Configure Role Permissions
              </CardTitle>
              <CardDescription>
                Select a role and configure its permissions. Changes will be logged for audit purposes.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4 items-center mb-6">
                <div className="flex-1">
                  <Select value={selectedRole} onValueChange={setSelectedRole}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a role" />
                    </SelectTrigger>
                    <SelectContent>
                      {permissionData.roles.map((role) => (
                        <SelectItem key={role} value={role}>
                          {roleLabels[role] || role}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <Button 
                  onClick={handleSavePermissions} 
                  disabled={!selectedRole || saving}
                  className="flex items-center gap-2"
                >
                  <Save className="h-4 w-4" />
                  {saving ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>

              {selectedRole && (
                <div className="space-y-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Badge className={getRoleBadgeColor(selectedRole)}>
                      {roleLabels[selectedRole]}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {rolePermissions.length} permissions assigned
                    </span>
                  </div>

                  {/* Permission Categories */}
                  <div className="grid gap-6">
                    {Object.entries(permissionData.allPermissions).map(([category, permissions]) => (
                      <Card key={category}>
                        <CardHeader className="pb-3">
                          <CardTitle className="text-lg">
                            {permissionLabels[category] || category}
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                            {permissions.map((permission) => (
                              <div key={permission} className="flex items-center space-x-2">
                                <Checkbox
                                  id={permission}
                                  checked={rolePermissions.includes(permission)}
                                  onCheckedChange={(checked) => 
                                    handlePermissionChange(permission, checked as boolean)
                                  }
                                />
                                <label
                                  htmlFor={permission}
                                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                >
                                  {actionLabels[permission.split(':')[1]] || permission.split(':')[1]}
                                </label>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="audit" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <History className="h-5 w-5" />
                Permission Change Audit Trail
              </CardTitle>
              <CardDescription>
                Track all permission changes made by administrators
              </CardDescription>
            </CardHeader>
            <CardContent>
              {auditLoading ? (
                <div className="text-center py-8">Loading audit logs...</div>
              ) : auditLogs.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No permission changes recorded yet
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Action</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Changed By</TableHead>
                      <TableHead>Details</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {auditLogs.map((log) => (
                      <TableRow key={log.id}>
                        <TableCell>
                          {new Date(log.timestamp).toLocaleString()}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{log.action}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={getRoleBadgeColor(log.resourceId || '')}>
                            {roleLabels[log.resourceId || ''] || log.resourceId || 'N/A'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {log.user.firstName} {log.user.lastName}
                        </TableCell>
                        <TableCell className="max-w-xs truncate">
                          {log.newValues?.permissions?.length || 0} permissions assigned
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
