'use client'

import { ReactNode } from 'react'
import { usePermissions } from '@/hooks/usePermissions'

interface PermissionGuardProps {
  children: ReactNode
  permission?: string
  permissions?: string[]
  requireAll?: boolean
  fallback?: ReactNode
  loading?: ReactNode
}

/**
 * PermissionGuard component that conditionally renders children based on user permissions
 * 
 * @param children - Content to render if permission check passes
 * @param permission - Single permission to check
 * @param permissions - Array of permissions to check
 * @param requireAll - If true, user must have ALL permissions. If false, user needs ANY permission (default: false)
 * @param fallback - Content to render if permission check fails
 * @param loading - Content to render while permissions are loading
 */
export function PermissionGuard({
  children,
  permission,
  permissions,
  requireAll = false,
  fallback = null,
  loading = null
}: PermissionGuardProps) {
  const { hasPermission, hasAnyPermission, hasAllPermissions, loading: isLoading } = usePermissions()

  if (isLoading) {
    return <>{loading}</>
  }

  // Check single permission
  if (permission) {
    return hasPermission(permission) ? <>{children}</> : <>{fallback}</>
  }

  // Check multiple permissions
  if (permissions && permissions.length > 0) {
    const hasRequiredPermissions = requireAll 
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions)
    
    return hasRequiredPermissions ? <>{children}</> : <>{fallback}</>
  }

  // If no permissions specified, render children
  return <>{children}</>
}

/**
 * Hook to conditionally execute functions based on permissions
 */
export function usePermissionGuard() {
  const { hasPermission, hasAnyPermission, hasAllPermissions } = usePermissions()

  const withPermission = (permission: string, callback: () => void) => {
    return () => {
      if (hasPermission(permission)) {
        callback()
      }
    }
  }

  const withAnyPermission = (permissions: string[], callback: () => void) => {
    return () => {
      if (hasAnyPermission(permissions)) {
        callback()
      }
    }
  }

  const withAllPermissions = (permissions: string[], callback: () => void) => {
    return () => {
      if (hasAllPermissions(permissions)) {
        callback()
      }
    }
  }

  return {
    withPermission,
    withAnyPermission,
    withAllPermissions,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions
  }
}

/**
 * Higher-order component for permission-based rendering
 */
export function withPermissions<T extends object>(
  Component: React.ComponentType<T>,
  requiredPermissions: string | string[],
  requireAll = false
) {
  return function PermissionWrappedComponent(props: T) {
    const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions]
    
    return (
      <PermissionGuard 
        permissions={permissions} 
        requireAll={requireAll}
        fallback={<div className="text-gray-500 text-sm">Access denied</div>}
      >
        <Component {...props} />
      </PermissionGuard>
    )
  }
}
