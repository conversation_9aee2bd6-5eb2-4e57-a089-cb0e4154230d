'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Bell, Phone, MessageSquare, Mail, AlertTriangle, Clock, CheckCircle } from 'lucide-react'

interface CalendarEvent {
  id: string
  title: string
  date: string
  time: string
  type: 'payment' | 'meeting' | 'collection' | 'reminder'
  status: 'pending' | 'completed' | 'overdue'
  amount?: number
  customer?: {
    name: string
    id: string
  }
  loan?: {
    id: string
    amount: number
  }
}

interface PaymentRemindersProps {
  events: CalendarEvent[]
  onEventUpdate: () => void
}

export default function PaymentReminders({ events, onEventUpdate }: PaymentRemindersProps) {
  const [activeReminderTab, setActiveReminderTab] = useState('overdue')

  // Filter events for different reminder categories
  const overduePayments = events.filter(event => 
    event.type === 'payment' && event.status === 'overdue'
  )

  const todayPayments = events.filter(event => {
    const today = new Date().toISOString().split('T')[0]
    return event.type === 'payment' && event.date === today && event.status === 'pending'
  })

  const upcomingPayments = events.filter(event => {
    const today = new Date()
    const eventDate = new Date(event.date)
    const diffTime = eventDate.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return event.type === 'payment' && 
           event.status === 'pending' && 
           diffDays > 0 && 
           diffDays <= 7
  })

  const handleSendReminder = async (eventId: string, method: 'sms' | 'call' | 'email') => {
    try {
      const response = await fetch('/api/calendar/reminders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ eventId, method })
      })
      
      if (response.ok) {
        console.log(`${method} reminder sent for event ${eventId}`)
        // You could show a toast notification here
      }
    } catch (error) {
      console.error('Error sending reminder:', error)
    }
  }

  const ReminderCard = ({ event, priority }: { event: CalendarEvent, priority: 'high' | 'medium' | 'low' }) => {
    const priorityColors = {
      high: 'border-red-500 bg-red-50',
      medium: 'border-yellow-500 bg-yellow-50',
      low: 'border-green-500 bg-green-50'
    }

    const priorityIcons = {
      high: <AlertTriangle className="h-4 w-4 text-red-600" />,
      medium: <Clock className="h-4 w-4 text-yellow-600" />,
      low: <CheckCircle className="h-4 w-4 text-green-600" />
    }

    return (
      <Card className={`${priorityColors[priority]} border-l-4`}>
        <CardContent className="p-4">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                {priorityIcons[priority]}
                <h4 className="font-semibold">{event.title}</h4>
                <Badge variant="outline" className="text-xs">
                  {priority.toUpperCase()}
                </Badge>
              </div>
              
              <div className="space-y-1 text-sm text-gray-600">
                <p><strong>Customer:</strong> {event.customer?.name || 'N/A'}</p>
                <p><strong>Date:</strong> {new Date(event.date).toLocaleDateString()}</p>
                <p><strong>Time:</strong> {event.time}</p>
                {event.amount && (
                  <p><strong>Amount:</strong> Rs. {event.amount.toLocaleString()}</p>
                )}
                {event.loan && (
                  <p><strong>Loan ID:</strong> {event.loan.id}</p>
                )}
              </div>
            </div>
            
            <div className="flex flex-col gap-2 ml-4">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleSendReminder(event.id, 'sms')}
                className="flex items-center gap-1"
              >
                <MessageSquare className="h-3 w-3" />
                SMS
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleSendReminder(event.id, 'call')}
                className="flex items-center gap-1"
              >
                <Phone className="h-3 w-3" />
                Call
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleSendReminder(event.id, 'email')}
                className="flex items-center gap-1"
              >
                <Mail className="h-3 w-3" />
                Email
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="border-red-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-red-600">Overdue Payments</p>
                <p className="text-2xl font-bold text-red-700">{overduePayments.length}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-yellow-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-yellow-600">Due Today</p>
                <p className="text-2xl font-bold text-yellow-700">{todayPayments.length}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600">Upcoming (7 days)</p>
                <p className="text-2xl font-bold text-blue-700">{upcomingPayments.length}</p>
              </div>
              <Bell className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Reminder Tabs */}
      <Tabs value={activeReminderTab} onValueChange={setActiveReminderTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overdue" className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            Overdue ({overduePayments.length})
          </TabsTrigger>
          <TabsTrigger value="today" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Due Today ({todayPayments.length})
          </TabsTrigger>
          <TabsTrigger value="upcoming" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Upcoming ({upcomingPayments.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overdue" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-red-700">Overdue Payments - Immediate Action Required</h3>
            <Button variant="destructive" size="sm">
              <MessageSquare className="h-4 w-4 mr-2" />
              Send All Reminders
            </Button>
          </div>
          
          <div className="space-y-4">
            {overduePayments.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center text-gray-500">
                  <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
                  <p>No overdue payments! Great job!</p>
                </CardContent>
              </Card>
            ) : (
              overduePayments.map(event => (
                <ReminderCard key={event.id} event={event} priority="high" />
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="today" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-yellow-700">Payments Due Today</h3>
            <Button variant="outline" size="sm">
              <Phone className="h-4 w-4 mr-2" />
              Call All Customers
            </Button>
          </div>
          
          <div className="space-y-4">
            {todayPayments.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center text-gray-500">
                  <Clock className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p>No payments due today</p>
                </CardContent>
              </Card>
            ) : (
              todayPayments.map(event => (
                <ReminderCard key={event.id} event={event} priority="medium" />
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="upcoming" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-blue-700">Upcoming Payments (Next 7 Days)</h3>
            <Button variant="outline" size="sm">
              <Mail className="h-4 w-4 mr-2" />
              Send Courtesy Reminders
            </Button>
          </div>
          
          <div className="space-y-4">
            {upcomingPayments.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center text-gray-500">
                  <Bell className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p>No upcoming payments in the next 7 days</p>
                </CardContent>
              </Card>
            ) : (
              upcomingPayments.map(event => (
                <ReminderCard key={event.id} event={event} priority="low" />
              ))
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
