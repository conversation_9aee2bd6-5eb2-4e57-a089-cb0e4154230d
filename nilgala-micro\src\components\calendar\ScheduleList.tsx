'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Search, Filter, CheckCircle, Clock, AlertTriangle, Eye, Edit, Trash2 } from 'lucide-react'

interface CalendarEvent {
  id: string
  title: string
  date: string
  time: string
  type: 'payment' | 'meeting' | 'collection' | 'reminder'
  status: 'pending' | 'completed' | 'overdue'
  amount?: number
  customer?: {
    name: string
    id: string
  }
  loan?: {
    id: string
    amount: number
  }
}

interface ScheduleListProps {
  events: CalendarEvent[]
  onEventUpdate: () => void
}

export default function ScheduleList({ events, onEventUpdate }: ScheduleListProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [typeFilter, setTypeFilter] = useState('ALL')
  const [statusFilter, setStatusFilter] = useState('ALL')

  const filteredEvents = events.filter(event => {
    const matchesSearch = event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.customer?.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = typeFilter === 'ALL' || event.type === typeFilter
    const matchesStatus = statusFilter === 'ALL' || event.status === statusFilter
    
    return matchesSearch && matchesType && matchesStatus
  })

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'overdue':
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>
      case 'overdue':
        return <Badge className="bg-red-100 text-red-800">Overdue</Badge>
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getTypeBadge = (type: string) => {
    switch (type) {
      case 'payment':
        return <Badge className="bg-blue-100 text-blue-800">Payment</Badge>
      case 'meeting':
        return <Badge className="bg-green-100 text-green-800">Meeting</Badge>
      case 'collection':
        return <Badge className="bg-orange-100 text-orange-800">Collection</Badge>
      case 'reminder':
        return <Badge className="bg-purple-100 text-purple-800">Reminder</Badge>
      default:
        return <Badge variant="secondary">{type}</Badge>
    }
  }

  const handleMarkComplete = async (eventId: string) => {
    try {
      const response = await fetch(`/api/calendar/events/${eventId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'completed' })
      })
      
      if (response.ok) {
        onEventUpdate()
      }
    } catch (error) {
      console.error('Error updating event:', error)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Schedule List</CardTitle>
        
        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search events or customers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">All Types</SelectItem>
              <SelectItem value="payment">Payment</SelectItem>
              <SelectItem value="meeting">Meeting</SelectItem>
              <SelectItem value="collection">Collection</SelectItem>
              <SelectItem value="reminder">Reminder</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">All Status</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="overdue">Overdue</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date & Time</TableHead>
                <TableHead>Event</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredEvents.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                    No events found matching your criteria
                  </TableCell>
                </TableRow>
              ) : (
                filteredEvents.map((event) => (
                  <TableRow key={event.id}>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">{new Date(event.date).toLocaleDateString()}</span>
                        <span className="text-sm text-gray-500">{event.time}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(event.status)}
                        <span className="font-medium">{event.title}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {event.customer ? (
                        <div className="flex flex-col">
                          <span className="font-medium">{event.customer.name}</span>
                          <span className="text-sm text-gray-500">ID: {event.customer.id}</span>
                        </div>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {getTypeBadge(event.type)}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(event.status)}
                    </TableCell>
                    <TableCell>
                      {event.amount ? (
                        <span className="font-semibold text-green-600">
                          Rs. {event.amount.toLocaleString()}
                        </span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        {event.status === 'pending' && (
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleMarkComplete(event.id)}
                            className="text-green-600 hover:text-green-700"
                          >
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                        )}
                        <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
        
        {/* Summary */}
        <div className="mt-4 flex justify-between items-center text-sm text-gray-600">
          <span>Showing {filteredEvents.length} of {events.length} events</span>
          <div className="flex gap-4">
            <span>Pending: {filteredEvents.filter(e => e.status === 'pending').length}</span>
            <span>Completed: {filteredEvents.filter(e => e.status === 'completed').length}</span>
            <span>Overdue: {filteredEvents.filter(e => e.status === 'overdue').length}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
