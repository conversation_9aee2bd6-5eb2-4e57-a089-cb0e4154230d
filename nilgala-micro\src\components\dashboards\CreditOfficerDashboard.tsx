'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  UserPlus,
  FileText,
  Clock,
  AlertTriangle,
  Search,
  Target,
  TrendingUp,
  Users,
  DollarSign,
  CreditCard
} from 'lucide-react'
import Link from 'next/link'
import { PermissionGuard } from '@/components/auth/PermissionGuard'
import { CREDIT_OFFICER_PERMISSIONS } from '@/hooks/usePermissions'
import { useSession } from 'next-auth/react'

interface CreditOfficerStats {
  assignedCustomers: number
  activeLoans: number
  pendingApplications: number
  pendingDisburse: number
  rejectedLoans: number
  monthlyTarget: number
  monthlyAchievement: number
  recentApplications: Array<{
    id: string // This is now the loan number, not the internal ID
    customerName: string
    amount: number
    status: 'pending' | 'under_review' | 'approved' | 'rejected'
    submittedDate: string
  }>
}



export default function CreditOfficerDashboard() {
  const { data: session } = useSession()
  const [stats, setStats] = useState<CreditOfficerStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [retryCount, setRetryCount] = useState(0)

  useEffect(() => {
    fetchCreditOfficerStats()
  }, [])

  const fetchCreditOfficerStats = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/dashboard/stats?role=CREDIT_OFFICER', {
        headers: {
          'Cache-Control': 'no-cache',
        },
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch credit officer stats: ${response.status}`)
      }

      const data = await response.json()
      setStats(data)
      setRetryCount(0) // Reset retry count on success
    } catch (error) {
      console.error('Error fetching credit officer stats:', error)
      setError(error instanceof Error ? error.message : 'Failed to load dashboard data')

      // Only set fallback stats if this is the first attempt
      if (retryCount === 0) {
        setStats({
          assignedCustomers: 0,
          activeLoans: 0,
          pendingApplications: 0,
          pendingDisburse: 0,
          rejectedLoans: 0,
          monthlyTarget: 0,
          monthlyAchievement: 0,
          recentApplications: []
        })
      }
    } finally {
      setLoading(false)
    }
  }

  const handleRetry = () => {
    setRetryCount(prev => prev + 1)
    fetchCreditOfficerStats()
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: 'bg-yellow-100 text-yellow-800',
      under_review: 'bg-blue-100 text-blue-800',
      approved: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800'
    }
    return (
      <Badge className={variants[status as keyof typeof variants]}>
        {status.replace('_', ' ').toUpperCase()}
      </Badge>
    )
  }



  const achievementPercentage = stats ? (stats.monthlyAchievement / stats.monthlyTarget) * 100 : 0

  // Error state
  if (error && !stats) {
    return (
      <div className="space-y-6">
        <div className="bg-gradient-to-r from-blue-600 to-cyan-600 text-white p-6 rounded-lg">
          <h2 className="text-2xl font-bold mb-2">Credit Officer Dashboard</h2>
          <p className="text-blue-100">Customer onboarding and loan processing</p>
        </div>

        <Card>
          <CardContent className="text-center py-8">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Failed to Load Dashboard</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={handleRetry} className="mr-2">
              Try Again
            </Button>
            <Button variant="outline" onClick={() => window.location.reload()}>
              Refresh Page
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="space-y-6">
        {/* Welcome Section Loading */}
        <div className="bg-gradient-to-r from-blue-600 to-cyan-600 text-white p-6 rounded-lg">
          <div className="animate-pulse">
            <div className="h-8 bg-blue-500 rounded w-1/2 mb-2"></div>
            <div className="h-4 bg-blue-400 rounded w-1/3"></div>
          </div>
        </div>

        {/* Key Metrics Loading */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent className="animate-pulse">
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Performance & Schedule Loading */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[...Array(2)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="animate-pulse">
                <div className="h-5 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent className="animate-pulse space-y-3">
                <div className="h-4 bg-gray-200 rounded w-full"></div>
                <div className="h-2 bg-gray-200 rounded w-full"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Loading message */}
        <div className="text-center py-4">
          <div className="inline-flex items-center gap-2 text-gray-600">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            Loading dashboard data...
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 to-cyan-600 text-white p-4 sm:p-6 rounded-lg">
        <h2 className="text-xl sm:text-2xl font-bold mb-2">
          {session?.user?.firstName ? `${session.user.firstName}, Welcome to Credit Officer Dashboard` : 'Credit Officer Dashboard'}
        </h2>
        <p className="text-blue-100 text-sm sm:text-base">Customer onboarding and loan processing</p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-3 sm:gap-4">
        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 sm:pb-3">
            <CardTitle className="text-sm sm:text-base font-medium">Assigned Customers</CardTitle>
            <Users className="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pt-2">
            <div className="text-xl sm:text-2xl font-bold">{stats?.assignedCustomers}</div>
            <p className="text-xs sm:text-sm text-muted-foreground">Active portfolio</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 sm:pb-3">
            <CardTitle className="text-sm sm:text-base font-medium">Pending Approval</CardTitle>
            <Clock className="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pt-2">
            <div className="text-xl sm:text-2xl font-bold text-orange-600">{stats?.pendingApplications}</div>
            <p className="text-xs sm:text-sm text-muted-foreground">Awaiting approval</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 sm:pb-3">
            <CardTitle className="text-sm sm:text-base font-medium">Pending Disburse</CardTitle>
            <DollarSign className="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pt-2">
            <div className="text-xl sm:text-2xl font-bold text-blue-600">{stats?.pendingDisburse}</div>
            <p className="text-xs sm:text-sm text-muted-foreground">Ready to disburse</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 sm:pb-3">
            <CardTitle className="text-sm sm:text-base font-medium">Active Loans</CardTitle>
            <FileText className="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pt-2">
            <div className="text-xl sm:text-2xl font-bold text-green-600">{stats?.activeLoans}</div>
            <p className="text-xs sm:text-sm text-muted-foreground">Currently servicing</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 sm:pb-3">
            <CardTitle className="text-sm sm:text-base font-medium">Rejected Loans</CardTitle>
            <AlertTriangle className="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pt-2">
            <div className="text-xl sm:text-2xl font-bold text-red-600">{stats?.rejectedLoans}</div>
            <p className="text-xs sm:text-sm text-muted-foreground">Not approved</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 sm:pb-3">
            <CardTitle className="text-sm sm:text-base font-medium">Monthly Target</CardTitle>
            <Target className="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pt-2">
            <div className="text-xl sm:text-2xl font-bold text-blue-600">
              LKR {stats?.monthlyTarget ? Number(stats.monthlyTarget).toLocaleString() : '0'}
            </div>
            <p className="text-xs sm:text-sm text-muted-foreground">Target amount</p>
          </CardContent>
        </Card>
      </div>

      {/* Monthly Performance */}
      <Card className="hover:shadow-md transition-shadow">
        <CardHeader className="pb-3 sm:pb-4">
          <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
            <Target className="h-4 w-4 sm:h-5 sm:w-5" />
            Monthly Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 sm:space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm sm:text-base font-medium">Target Achievement Progress</span>
              <span className="text-sm sm:text-base text-muted-foreground font-semibold">
                {achievementPercentage.toFixed(1)}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 sm:h-3">
              <div
                className="bg-blue-600 h-2 sm:h-3 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(achievementPercentage, 100)}%` }}
              ></div>
            </div>
            <div className="flex justify-between text-xs sm:text-sm">
              <span className="font-medium">
                LKR {stats?.monthlyAchievement ? Number(stats.monthlyAchievement).toLocaleString() : '0'}
              </span>
              <span className="text-muted-foreground">
                LKR {stats?.monthlyTarget ? Number(stats.monthlyTarget).toLocaleString() : '0'} target
              </span>
            </div>
          </div>
        </CardContent>
      </Card>



      {/* Recent Applications */}
      <Card className="hover:shadow-md transition-shadow">
        <CardHeader className="pb-3 sm:pb-4">
          <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
            <FileText className="h-4 w-4 sm:h-5 sm:w-5" />
            Recent Applications
          </CardTitle>
          <CardDescription className="text-sm sm:text-base">Latest loan applications in your queue</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 sm:space-y-4">
            {stats?.recentApplications.map((application, index) => (
              <div key={index} className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 sm:p-4 border rounded-lg hover:bg-gray-50 transition-colors space-y-2 sm:space-y-0">
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-sm sm:text-base truncate">{application.customerName}</h4>
                  <p className="text-xs sm:text-sm text-gray-600">
                    {application.id} • LKR {application.amount.toLocaleString()}
                  </p>
                </div>
                <div className="flex items-center justify-between sm:flex-col sm:items-end sm:text-right">
                  <div className="mb-0 sm:mb-1">
                    {getStatusBadge(application.status)}
                  </div>
                  <p className="text-xs sm:text-sm text-gray-500">
                    {new Date(application.submittedDate).toLocaleDateString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Credit Officer Actions */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        <PermissionGuard permission={CREDIT_OFFICER_PERMISSIONS.READ_CUSTOMERS}>
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3 sm:pb-4">
              <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                <UserPlus className="h-4 w-4 sm:h-5 sm:w-5" />
                Customer Management
              </CardTitle>
              <CardDescription className="text-sm sm:text-base">Customer onboarding and management</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <PermissionGuard permission={CREDIT_OFFICER_PERMISSIONS.CREATE_CUSTOMERS}>
                <Link href="/customers/new">
                  <Button className="w-full justify-start h-11 sm:h-12 text-sm sm:text-base" variant="outline">
                    <UserPlus className="h-4 w-4 mr-2" />
                    Add New Customer
                  </Button>
                </Link>
              </PermissionGuard>
              <Link href="/customers">
                <Button className="w-full justify-start h-11 sm:h-12 text-sm sm:text-base" variant="outline">
                  <Search className="h-4 w-4 mr-2" />
                  Search Customers
                </Button>
              </Link>
            </CardContent>
          </Card>
        </PermissionGuard>

        <PermissionGuard permission={CREDIT_OFFICER_PERMISSIONS.READ_LOANS}>
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3 sm:pb-4">
              <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                <FileText className="h-4 w-4 sm:h-5 sm:w-5" />
                Loan Processing
              </CardTitle>
              <CardDescription className="text-sm sm:text-base">Loan applications and processing</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Link href="/loans?status=pending">
                <Button className="w-full justify-start h-11 sm:h-12 text-sm sm:text-base" variant="outline">
                  <Clock className="h-4 w-4 mr-2" />
                  Pending Applications ({stats?.pendingApplications})
                </Button>
              </Link>
            </CardContent>
          </Card>
        </PermissionGuard>

        <PermissionGuard permission={CREDIT_OFFICER_PERMISSIONS.CREATE_PAYMENTS}>
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3 sm:pb-4">
              <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                <CreditCard className="h-4 w-4 sm:h-5 sm:w-5" />
                Payment Collection
              </CardTitle>
              <CardDescription className="text-sm sm:text-base">Record customer payments</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Link href="/payments/new">
                <Button className="w-full justify-start h-11 sm:h-12 text-sm sm:text-base" variant="outline">
                  <DollarSign className="h-4 w-4 mr-2" />
                  Record Payment
                </Button>
              </Link>
              <PermissionGuard permission={CREDIT_OFFICER_PERMISSIONS.READ_PAYMENTS}>
                <Link href="/payments">
                  <Button className="w-full justify-start h-11 sm:h-12 text-sm sm:text-base" variant="outline">
                    <CreditCard className="h-4 w-4 mr-2" />
                    Payment History
                  </Button>
                </Link>
              </PermissionGuard>
            </CardContent>
          </Card>
        </PermissionGuard>
      </div>

      {/* Quick Access */}
      <Card className="hover:shadow-md transition-shadow">
        <CardHeader className="pb-3 sm:pb-4">
          <CardTitle className="text-base sm:text-lg">Credit Officer Quick Access</CardTitle>
          <CardDescription className="text-sm sm:text-base">Frequently used functions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 sm:gap-4">
            <PermissionGuard permission={CREDIT_OFFICER_PERMISSIONS.READ_CUSTOMERS}>
              <Link href="/customers">
                <Button variant="outline" className="w-full h-11 sm:h-12 text-xs sm:text-sm flex-col sm:flex-row gap-1 sm:gap-2">
                  <Users className="h-4 w-4" />
                  <span className="hidden sm:inline">My Customers</span>
                  <span className="sm:hidden">Customers</span>
                </Button>
              </Link>
            </PermissionGuard>
            <PermissionGuard permission={CREDIT_OFFICER_PERMISSIONS.READ_LOANS}>
              <Link href="/loans">
                <Button variant="outline" className="w-full h-11 sm:h-12 text-xs sm:text-sm flex-col sm:flex-row gap-1 sm:gap-2">
                  <FileText className="h-4 w-4" />
                  <span className="hidden sm:inline">Loan Applications</span>
                  <span className="sm:hidden">Loans</span>
                </Button>
              </Link>
            </PermissionGuard>
            <PermissionGuard permission={CREDIT_OFFICER_PERMISSIONS.CREATE_PAYMENTS}>
              <Link href="/payments/new">
                <Button variant="outline" className="w-full h-11 sm:h-12 text-xs sm:text-sm flex-col sm:flex-row gap-1 sm:gap-2">
                  <DollarSign className="h-4 w-4" />
                  <span className="hidden sm:inline">Record Payment</span>
                  <span className="sm:hidden">Payment</span>
                </Button>
              </Link>
            </PermissionGuard>

            <PermissionGuard permission={CREDIT_OFFICER_PERMISSIONS.READ_REPORTS}>
              <Link href="/performance">
                <Button variant="outline" className="w-full h-11 sm:h-12 text-xs sm:text-sm flex-col sm:flex-row gap-1 sm:gap-2">
                  <TrendingUp className="h-4 w-4" />
                  <span className="hidden sm:inline">My Performance</span>
                  <span className="sm:hidden">Performance</span>
                </Button>
              </Link>
            </PermissionGuard>
          </div>
        </CardContent>
      </Card>


    </div>
  )
}
