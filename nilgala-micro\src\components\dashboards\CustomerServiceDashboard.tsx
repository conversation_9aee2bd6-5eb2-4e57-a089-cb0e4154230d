'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Phone, 
  DollarSign, 
  MessageCircle, 
  Clock,
  CheckCircle,
  AlertTriangle,
  Users,
  Calendar,
  Search,
  FileText,
  Headphones,
  Star,
  TrendingUp,
  Mail
} from 'lucide-react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'

interface CustomerServiceStats {
  dailyPayments: number
  dailyPaymentAmount: number
  customerInquiries: number
  resolvedIssues: number
  pendingFollowUps: number
  customerSatisfaction: number
  responseTime: number
  recentPayments: Array<{
    id: string
    customerName: string
    amount: number
    method: string
    time: string
  }>
  recentInquiries: Array<{
    id: string
    customerName: string
    type: 'payment' | 'balance' | 'schedule' | 'complaint' | 'general'
    status: 'open' | 'in_progress' | 'resolved'
    priority: 'high' | 'medium' | 'low'
    time: string
  }>
  todaysTasks: Array<{
    type: 'follow_up' | 'payment_reminder' | 'customer_call' | 'documentation'
    description: string
    customerName: string
    priority: 'high' | 'medium' | 'low'
    time?: string
  }>
}

export default function CustomerServiceDashboard() {
  const { data: session } = useSession()
  const [stats, setStats] = useState<CustomerServiceStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchCustomerServiceStats()
  }, [])

  const fetchCustomerServiceStats = async () => {
    try {
      const response = await fetch('/api/dashboard/stats?role=CUSTOMER_SERVICE_OFFICER')
      if (!response.ok) {
        throw new Error('Failed to fetch customer service stats')
      }
      const data = await response.json()
      setStats(data)
    } catch (error) {
      console.error('Error fetching customer service stats:', error)
      // Fallback to empty stats
      setStats({
        dailyPayments: 0,
        dailyPaymentAmount: 0,
        customerInquiries: 0,
        resolvedIssues: 0,
        pendingFollowUps: 0,
        customerSatisfaction: 0,
        responseTime: 0,
        recentPayments: [],
        recentInquiries: [],
        todaysTasks: []
      })
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      open: 'bg-red-100 text-red-800',
      in_progress: 'bg-yellow-100 text-yellow-800',
      resolved: 'bg-green-100 text-green-800'
    }
    return (
      <Badge className={variants[status as keyof typeof variants]} size="sm">
        {status.replace('_', ' ').toUpperCase()}
      </Badge>
    )
  }

  const getPriorityBadge = (priority: string) => {
    const variants = {
      high: 'bg-red-100 text-red-800',
      medium: 'bg-yellow-100 text-yellow-800',
      low: 'bg-green-100 text-green-800'
    }
    return (
      <Badge className={variants[priority as keyof typeof variants]} size="sm">
        {priority.toUpperCase()}
      </Badge>
    )
  }

  const getInquiryIcon = (type: string) => {
    const icons = {
      payment: <DollarSign className="h-4 w-4" />,
      balance: <FileText className="h-4 w-4" />,
      schedule: <Calendar className="h-4 w-4" />,
      complaint: <AlertTriangle className="h-4 w-4" />,
      general: <MessageCircle className="h-4 w-4" />
    }
    return icons[type as keyof typeof icons] || <MessageCircle className="h-4 w-4" />
  }

  const getTaskIcon = (type: string) => {
    const icons = {
      follow_up: <Phone className="h-4 w-4" />,
      payment_reminder: <Clock className="h-4 w-4" />,
      customer_call: <Headphones className="h-4 w-4" />,
      documentation: <FileText className="h-4 w-4" />
    }
    return icons[type as keyof typeof icons] || <MessageCircle className="h-4 w-4" />
  }

  if (loading) {
    return <div className="text-center py-8">Loading dashboard...</div>
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-pink-600 to-rose-600 text-white p-6 rounded-lg">
        <h2 className="text-2xl font-bold mb-2">
          {session?.user?.firstName ? `${session.user.firstName}, Welcome to Customer Service Dashboard` : 'Customer Service Dashboard'}
        </h2>
        <p className="text-pink-100">Customer support and payment processing</p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Daily Payments</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats?.dailyPayments}</div>
            <p className="text-xs text-muted-foreground">Processed today</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Payment Amount</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              LKR {stats?.dailyPaymentAmount.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">Today's total</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customer Inquiries</CardTitle>
            <MessageCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats?.customerInquiries}</div>
            <p className="text-xs text-muted-foreground">Today's inquiries</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Resolved Issues</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats?.resolvedIssues}</div>
            <p className="text-xs text-muted-foreground">Successfully resolved</p>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customer Satisfaction</CardTitle>
            <Star className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {stats?.customerSatisfaction}/5.0
              </div>
              <div className="flex justify-center mt-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <span 
                    key={star} 
                    className={`text-lg ${star <= (stats?.customerSatisfaction || 0) ? 'text-yellow-400' : 'text-gray-300'}`}
                  >
                    ★
                  </span>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Response Time</CardTitle>
            <Clock className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {stats?.responseTime} min
              </div>
              <p className="text-xs text-muted-foreground">Average response</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Follow-ups</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {stats?.pendingFollowUps}
              </div>
              <p className="text-xs text-muted-foreground">Require attention</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Today's Tasks */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Today's Tasks
          </CardTitle>
          <CardDescription>Scheduled customer service tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {stats?.todaysTasks.map((task, index) => (
              <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                <div className="text-blue-600">
                  {getTaskIcon(task.type)}
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium">{task.description}</p>
                  <p className="text-xs text-gray-500">Customer: {task.customerName}</p>
                  {task.time && (
                    <p className="text-xs text-blue-600">{task.time}</p>
                  )}
                </div>
                <div>
                  {getPriorityBadge(task.priority)}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Activities */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Recent Payments
            </CardTitle>
            <CardDescription>Latest payment transactions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats?.recentPayments.map((payment, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <h4 className="font-medium text-sm">{payment.customerName}</h4>
                    <p className="text-xs text-gray-600">{payment.id} • {payment.method}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-bold text-green-600">
                      LKR {payment.amount.toLocaleString()}
                    </div>
                    <p className="text-xs text-gray-500">{payment.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="h-5 w-5" />
              Recent Inquiries
            </CardTitle>
            <CardDescription>Customer support requests</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats?.recentInquiries.map((inquiry, index) => (
                <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                  <div className="text-blue-600">
                    {getInquiryIcon(inquiry.type)}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-sm">{inquiry.customerName}</h4>
                    <p className="text-xs text-gray-600">{inquiry.type} • {inquiry.time}</p>
                  </div>
                  <div className="flex flex-col gap-1">
                    {getStatusBadge(inquiry.status)}
                    {getPriorityBadge(inquiry.priority)}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Service Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Payment Processing
            </CardTitle>
            <CardDescription>Process customer payments</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <Link href="/payments/new">
              <Button className="w-full justify-start" variant="outline">
                <DollarSign className="h-4 w-4 mr-2" />
                Record Payment
              </Button>
            </Link>
            <Link href="/payment-schedules">
              <Button className="w-full justify-start" variant="outline">
                <Calendar className="h-4 w-4 mr-2" />
                Payment Schedules
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Headphones className="h-5 w-5" />
              Customer Support
            </CardTitle>
            <CardDescription>Customer service functions</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button className="w-full justify-start" variant="outline">
              <MessageCircle className="h-4 w-4 mr-2" />
              Open Inquiries ({stats?.customerInquiries - stats?.resolvedIssues})
            </Button>
            <Button className="w-full justify-start" variant="outline">
              <Phone className="h-4 w-4 mr-2" />
              Follow-up Calls ({stats?.pendingFollowUps})
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Quick Access */}
      <Card>
        <CardHeader>
          <CardTitle>Customer Service Quick Access</CardTitle>
          <CardDescription>Frequently used customer service functions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Link href="/customers">
              <Button variant="outline" className="w-full">
                <Search className="h-4 w-4 mr-2" />
                Find Customer
              </Button>
            </Link>
            <Link href="/payments">
              <Button variant="outline" className="w-full">
                <DollarSign className="h-4 w-4 mr-2" />
                Payments
              </Button>
            </Link>
            <Button variant="outline" className="w-full">
              <Phone className="h-4 w-4 mr-2" />
              Call Log
            </Button>
            <Link href="/reports">
              <Button variant="outline" className="w-full">
                <FileText className="h-4 w-4 mr-2" />
                Reports
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
