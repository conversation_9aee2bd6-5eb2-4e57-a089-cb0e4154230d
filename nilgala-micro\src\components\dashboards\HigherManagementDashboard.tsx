'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatCurrency } from '@/lib/utils'
import { useSession } from 'next-auth/react'
import {
  TrendingUp,
  DollarSign,
  AlertTriangle,
  BarChart3,
  PieChart,
  Target,
  Users,
  FileText,
  Calendar,
  Download,
  Eye,
  CheckCircle
} from 'lucide-react'
import Link from 'next/link'
import { usePermissions } from '@/hooks/usePermissions'

interface ManagementStats {
  portfolioValue: number
  monthlyDisbursement: number
  monthlyCollection: number
  collectionRate: number
  activeLoans: number
  overdueLoans: number
  nplRatio: number
  profitability: number
  customerGrowth: number
  totalCustomers: number
  totalCreditOfficers: number
  averageLoanSize: number
  totalLoans: number
}

export default function HigherManagementDashboard() {
  const { data: session } = useSession()
  const [stats, setStats] = useState<ManagementStats | null>(null)
  const [loading, setLoading] = useState(true)
  const { hasPermission, loading: permissionsLoading } = usePermissions()

  useEffect(() => {
    fetchManagementStats()
  }, [])

  const fetchManagementStats = async () => {
    try {
      const response = await fetch('/api/dashboard/stats?role=HIGHER_MANAGEMENT')
      if (!response.ok) {
        throw new Error('Failed to fetch management stats')
      }
      const data = await response.json()
      setStats(data)
    } catch (error) {
      console.error('Error fetching management stats:', error)
      // Fallback to empty stats
      setStats({
        portfolioValue: 0,
        monthlyDisbursement: 0,
        monthlyCollection: 0,
        collectionRate: 0,
        activeLoans: 0,
        overdueLoans: 0,
        nplRatio: 0,
        profitability: 0,
        customerGrowth: 0,
        totalCustomers: 0,
        totalCreditOfficers: 0,
        averageLoanSize: 0,
        totalLoans: 0
      })
    } finally {
      setLoading(false)
    }
  }

  const getPerformanceBadge = (value: number, threshold: number) => {
    if (value >= threshold) {
      return <Badge className="bg-green-100 text-green-800">Excellent</Badge>
    } else if (value >= threshold * 0.8) {
      return <Badge className="bg-yellow-100 text-yellow-800">Good</Badge>
    } else {
      return <Badge className="bg-red-100 text-red-800">Needs Attention</Badge>
    }
  }

  if (loading || permissionsLoading) {
    return <div className="text-center py-8">Loading dashboard...</div>
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white p-6 rounded-lg">
        <h2 className="text-2xl font-bold mb-2">
          {session?.user?.firstName ? `${session.user.firstName}, Welcome to Higher Management Dashboard` : 'Higher Management Dashboard'}
        </h2>
        <p className="text-purple-100">Strategic oversight and portfolio management</p>
      </div>

      {/* Key Performance Indicators */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {hasPermission('loans:read') && (
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Portfolio Value</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(stats?.portfolioValue)}
              </div>
              <p className="text-xs text-muted-foreground">Total outstanding</p>
            </CardContent>
          </Card>
        )}

        {hasPermission('payments:read') && (
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Collection Rate</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {stats?.collectionRate}%
              </div>
              <p className="text-xs text-muted-foreground">This month</p>
            </CardContent>
          </Card>
        )}

        {hasPermission('audit:read') && (
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">NPL Ratio</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {stats?.nplRatio}%
              </div>
              <p className="text-xs text-muted-foreground">Non-performing loans</p>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Profitability</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {stats?.profitability}%
            </div>
            <p className="text-xs text-muted-foreground">ROI this quarter</p>
          </CardContent>
        </Card>
      </div>

      {/* Monthly Performance */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Disbursement</CardTitle>
            <FileText className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              LKR {stats?.monthlyDisbursement.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">New loans this month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Collection</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              LKR {stats?.monthlyCollection.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">Payments received</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customer Growth</CardTitle>
            <Users className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              +{stats?.customerGrowth}%
            </div>
            <p className="text-xs text-muted-foreground">Month over month</p>
          </CardContent>
        </Card>
      </div>

      {/* Business Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {stats?.totalCustomers.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">Active customer base</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Credit Officers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {stats?.totalCreditOfficers}
            </div>
            <p className="text-xs text-muted-foreground">Active staff</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Loan Size</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {formatCurrency(stats?.averageLoanSize)}
            </div>
            <p className="text-xs text-muted-foreground">Per loan</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Loans</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-indigo-600">
              {stats?.totalLoans.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">All time</p>
          </CardContent>
        </Card>
      </div>

      {/* Management Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {hasPermission('reports:read') && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Strategic Reports
              </CardTitle>
              <CardDescription>Comprehensive business intelligence</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <Link href="/reports?tab=portfolio">
                <Button className="w-full justify-start" variant="outline">
                  <PieChart className="h-4 w-4 mr-2" />
                  Portfolio Analysis
                </Button>
              </Link>
              <Link href="/reports?tab=overview">
                <Button className="w-full justify-start" variant="outline">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Growth Trends
                </Button>
              </Link>
              {hasPermission('reports:export') && (
                <Link href="/reports">
                  <Button className="w-full justify-start" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Export Reports
                  </Button>
                </Link>
              )}
            </CardContent>
          </Card>
        )}

        {hasPermission('users:read') && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Oversight Functions
              </CardTitle>
              <CardDescription>Management oversight and control</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              {hasPermission('users:create') && (
                <Link href="/admin/users">
                  <Button className="w-full justify-start" variant="outline">
                    <Users className="h-4 w-4 mr-2" />
                    User Management
                  </Button>
                </Link>
              )}
              {hasPermission('audit:read') && (
                <Button className="w-full justify-start" variant="outline">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Risk Assessment
                </Button>
              )}
              <Button className="w-full justify-start" variant="outline">
                <Users className="h-4 w-4 mr-2" />
                Team Performance
              </Button>
              <Link href="/admin/targets">
                <Button className="w-full justify-start" variant="outline">
                  <Target className="h-4 w-4 mr-2" />
                  Performance Targets
                </Button>
              </Link>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Quick Access */}
      <Card>
        <CardHeader>
          <CardTitle>Management Quick Access</CardTitle>
          <CardDescription>Key management functions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {hasPermission('loans:read') && (
              <Link href="/loans">
                <Button variant="outline" className="w-full">
                  <FileText className="h-4 w-4 mr-2" />
                  Loan Portfolio
                </Button>
              </Link>
            )}
            {hasPermission('payments:read') && (
              <Link href="/payment-schedules">
                <Button variant="outline" className="w-full">
                  <Calendar className="h-4 w-4 mr-2" />
                  Collections
                </Button>
              </Link>
            )}
            {hasPermission('customers:read') && (
              <Link href="/customers">
                <Button variant="outline" className="w-full">
                  <Users className="h-4 w-4 mr-2" />
                  Customers
                </Button>
              </Link>
            )}
            {hasPermission('reports:read') && (
              <Link href="/reports">
                <Button variant="outline" className="w-full">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Reports
                </Button>
              </Link>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
