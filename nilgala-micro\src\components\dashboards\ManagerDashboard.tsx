'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Users, 
  FileText, 
  DollarSign, 
  Clock,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  Calendar,
  UserCheck,
  Target,
  Award,
  Activity
} from 'lucide-react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'

interface ManagerStats {
  teamSize: number
  teamLoans: number
  teamCollections: number
  pendingApprovals: number
  monthlyTarget: number
  monthlyAchievement: number
  customerSatisfaction: number
  teamPerformance: Array<{
    name: string
    role: string
    loans: number
    collections: number
    performance: 'excellent' | 'good' | 'needs_improvement'
  }>
  recentActivities: Array<{
    type: string
    description: string
    time: string
  }>
}

export default function ManagerDashboard() {
  const { data: session } = useSession()
  const [stats, setStats] = useState<ManagerStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchManagerStats()
  }, [])

  const fetchManagerStats = async () => {
    try {
      const response = await fetch('/api/dashboard/stats?role=MANAGER')
      if (!response.ok) {
        throw new Error('Failed to fetch manager stats')
      }
      const data = await response.json()
      setStats(data)
    } catch (error) {
      console.error('Error fetching manager stats:', error)
      // Fallback to empty stats
      setStats({
        teamSize: 0,
        teamLoans: 0,
        teamCollections: 0,
        pendingApprovals: 0,
        monthlyTarget: 0,
        monthlyAchievement: 0,
        customerSatisfaction: 0,
        teamPerformance: [],
        recentActivities: []
      })
    } finally {
      setLoading(false)
    }
  }

  const getPerformanceBadge = (performance: string) => {
    const variants = {
      excellent: 'bg-green-100 text-green-800',
      good: 'bg-blue-100 text-blue-800',
      needs_improvement: 'bg-yellow-100 text-yellow-800'
    }
    return (
      <Badge className={variants[performance as keyof typeof variants]}>
        {performance.replace('_', ' ').toUpperCase()}
      </Badge>
    )
  }

  const achievementPercentage = stats ? (stats.monthlyAchievement / stats.monthlyTarget) * 100 : 0

  if (loading) {
    return <div className="text-center py-8">Loading dashboard...</div>
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-green-600 to-teal-600 text-white p-6 rounded-lg">
        <h2 className="text-2xl font-bold mb-2">
          {session?.user?.firstName ? `${session.user.firstName}, Welcome to Manager Dashboard` : 'Manager Dashboard'}
        </h2>
        <p className="text-green-100">Team oversight and management</p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Team Size</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.teamSize}</div>
            <p className="text-xs text-muted-foreground">Active team members</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Team Loans</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats?.teamLoans}</div>
            <p className="text-xs text-muted-foreground">Active loans</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Collections</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              LKR {stats?.teamCollections.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">This month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats?.pendingApprovals}</div>
            <p className="text-xs text-muted-foreground">Awaiting review</p>
          </CardContent>
        </Card>
      </div>

      {/* Performance Tracking */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Monthly Target Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Target Achievement</span>
                <span className="text-sm text-muted-foreground">
                  {achievementPercentage.toFixed(1)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full" 
                  style={{ width: `${Math.min(achievementPercentage, 100)}%` }}
                ></div>
              </div>
              <div className="flex justify-between text-sm">
                <span>LKR {stats?.monthlyAchievement.toLocaleString()}</span>
                <span>LKR {stats?.monthlyTarget.toLocaleString()}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              Customer Satisfaction
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">
                {stats?.customerSatisfaction}/5.0
              </div>
              <p className="text-sm text-muted-foreground">Average rating</p>
              <div className="flex justify-center mt-2">
                {[1, 2, 3, 4, 5].map((star) => (
                  <span 
                    key={star} 
                    className={`text-lg ${star <= (stats?.customerSatisfaction || 0) ? 'text-yellow-400' : 'text-gray-300'}`}
                  >
                    ★
                  </span>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Team Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            Team Performance
          </CardTitle>
          <CardDescription>Individual team member performance</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {stats?.teamPerformance.map((member, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">{member.name}</h4>
                  <p className="text-sm text-gray-600">{member.role}</p>
                </div>
                <div className="text-center">
                  <div className="text-sm font-medium">{member.loans} loans</div>
                  <div className="text-sm text-green-600">
                    LKR {member.collections.toLocaleString()}
                  </div>
                </div>
                <div>
                  {getPerformanceBadge(member.performance)}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Activities */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Recent Activities
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {stats?.recentActivities.map((activity, index) => (
              <div key={index} className="flex items-center gap-3 p-3 border-l-4 border-blue-500 bg-blue-50">
                <div className="flex-1">
                  <p className="text-sm font-medium">{activity.description}</p>
                  <p className="text-xs text-gray-500">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Manager Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Approval Center
            </CardTitle>
            <CardDescription>Loan approvals and reviews</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button className="w-full justify-start" variant="outline">
              <Clock className="h-4 w-4 mr-2" />
              Pending Approvals ({stats?.pendingApprovals})
            </Button>
            <Button className="w-full justify-start" variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              Review Applications
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Team Management
            </CardTitle>
            <CardDescription>Manage team and assignments</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button className="w-full justify-start" variant="outline">
              <UserCheck className="h-4 w-4 mr-2" />
              Team Performance
            </Button>
            <Button className="w-full justify-start" variant="outline">
              <Target className="h-4 w-4 mr-2" />
              Set Targets
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Quick Access */}
      <Card>
        <CardHeader>
          <CardTitle>Manager Quick Access</CardTitle>
          <CardDescription>Frequently used management functions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Link href="/loans">
              <Button variant="outline" className="w-full">
                <FileText className="h-4 w-4 mr-2" />
                Loans
              </Button>
            </Link>
            <Link href="/customers">
              <Button variant="outline" className="w-full">
                <Users className="h-4 w-4 mr-2" />
                Customers
              </Button>
            </Link>
            <Link href="/payment-schedules">
              <Button variant="outline" className="w-full">
                <Calendar className="h-4 w-4 mr-2" />
                Collections
              </Button>
            </Link>
            <Link href="/reports">
              <Button variant="outline" className="w-full">
                <TrendingUp className="h-4 w-4 mr-2" />
                Reports
              </Button>
            </Link>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
            <Link href="/admin/targets">
              <Button variant="outline" className="w-full">
                <Target className="h-4 w-4 mr-2" />
                Team Targets
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
