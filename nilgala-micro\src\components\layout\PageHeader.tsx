'use client'

import { useSession, signOut } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { ArrowLeft, LogOut, User, Home } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useState, useEffect } from 'react'

interface PageHeaderProps {
  title: string
  description?: string
  showBackButton?: boolean
  backUrl?: string
  actions?: React.ReactNode
  children?: React.ReactNode
}

export default function PageHeader({
  title,
  description,
  showBackButton = true,
  backUrl = '/dashboard',
  actions,
  children
}: PageHeaderProps) {
  const { data: session } = useSession()
  const router = useRouter()
  const [companySettings, setCompanySettings] = useState({
    systemTitle: 'Nilgala Micro',
    companyLogo: ''
  })

  useEffect(() => {
    fetchCompanySettings()
  }, [])

  const fetchCompanySettings = async () => {
    try {
      const response = await fetch('/api/company-settings')
      if (response.ok) {
        const data = await response.json()
        setCompanySettings({
          systemTitle: data.systemTitle || 'Nilgala Micro',
          companyLogo: data.companyLogo || ''
        })
      }
    } catch (error) {
      console.error('Error fetching company settings:', error)
    }
  }

  const handleBack = () => {
    if (backUrl) {
      router.push(backUrl)
    } else {
      router.back()
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Navigation Bar */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-3 sm:py-4">
            <div className="flex items-center space-x-2 sm:space-x-4 min-w-0 flex-1">
              <Link href="/dashboard" className="flex items-center space-x-2 min-w-0">
                {companySettings.companyLogo ? (
                  <img
                    src={companySettings.companyLogo}
                    alt="Company Logo"
                    className="h-6 sm:h-8 object-contain flex-shrink-0"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none'
                    }}
                  />
                ) : null}
                {!companySettings.companyLogo && (
                  <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 truncate">
                    {companySettings.systemTitle}
                  </h1>
                )}
              </Link>
              {showBackButton && (
                <div className="flex items-center space-x-1 sm:space-x-2">
                  <span className="text-gray-400 hidden sm:inline">|</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleBack}
                    className="text-gray-600 hover:text-gray-900 px-2 sm:px-3"
                  >
                    <ArrowLeft className="h-4 w-4 mr-1 sm:mr-2" />
                    <span className="hidden sm:inline">Back</span>
                  </Button>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-2 sm:space-x-4 flex-shrink-0">
              <Link href="/dashboard" className="hidden sm:block">
                <Button variant="ghost" size="sm">
                  <Home className="h-4 w-4 mr-2" />
                  Dashboard
                </Button>
              </Link>

              {/* Mobile Dashboard Link */}
              <Link href="/dashboard" className="sm:hidden">
                <Button variant="ghost" size="sm" className="px-2">
                  <Home className="h-4 w-4" />
                </Button>
              </Link>

              {session?.user && (
                <>
                  {/* Desktop User Info */}
                  <div className="hidden lg:flex items-center space-x-2">
                    <User className="h-5 w-5 text-gray-500" />
                    <span className="text-sm text-gray-700">
                      {session.user.firstName} {session.user.lastName}
                    </span>
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                      {session.user.role.replace('_', ' ')}
                    </span>
                  </div>

                  {/* Mobile User Info */}
                  <div className="lg:hidden flex items-center space-x-1">
                    <User className="h-4 w-4 text-gray-500" />
                    <span className="text-xs bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded">
                      {session.user.role.replace('_', ' ').split(' ')[0]}
                    </span>
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => signOut({ callbackUrl: '/auth/signin' })}
                    className="px-2 sm:px-3"
                  >
                    <LogOut className="h-4 w-4 mr-0 sm:mr-2" />
                    <span className="hidden sm:inline">Sign Out</span>
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Page Content */}
      <main className="max-w-7xl mx-auto py-4 sm:py-6 px-4 sm:px-6 lg:px-8">
        <div className="py-4 sm:py-6">
          {/* Page Title Section */}
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-4 mb-4 sm:mb-6">
            <div className="min-w-0 flex-1">
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 truncate">{title}</h1>
              {description && (
                <p className="text-sm sm:text-base text-gray-600 mt-1">{description}</p>
              )}
            </div>
            {actions && (
              <div className="flex items-center space-x-2 flex-shrink-0">
                {actions}
              </div>
            )}
          </div>

          {/* Page Content */}
          {children}
        </div>
      </main>
    </div>
  )
}
