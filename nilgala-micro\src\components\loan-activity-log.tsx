'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Clock, 
  User, 
  FileText, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  DollarSign,
  Calendar,
  Edit,
  Plus,
  Trash2,
  Eye,
  Download,
  Upload,
  RefreshCw
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

// Simple avatar component for initials
const UserAvatar = ({ name, className = "" }: { name: string, className?: string }) => {
  const initials = name
    .split(' ')
    .map(n => n.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)

  return (
    <div className={`flex items-center justify-center bg-blue-500 text-white rounded-full font-medium ${className}`}>
      {initials}
    </div>
  )
}

interface ActivityLogEntry {
  id: string
  action: string
  resource: string
  resourceId: string
  details: string
  timestamp: string
  user: {
    id: string
    firstName: string
    lastName: string
    role: string
    email: string
  }
}

interface LoanActivityLogProps {
  loanId: string
}

const getActivityIcon = (action: string, details: string) => {
  const actionLower = action.toLowerCase()
  const detailsLower = details.toLowerCase()
  
  if (actionLower === 'create') return <Plus className="h-4 w-4" />
  if (actionLower === 'update') {
    if (detailsLower.includes('approved')) return <CheckCircle className="h-4 w-4" />
    if (detailsLower.includes('rejected')) return <XCircle className="h-4 w-4" />
    if (detailsLower.includes('disbursed')) return <DollarSign className="h-4 w-4" />
    if (detailsLower.includes('schedule')) return <Calendar className="h-4 w-4" />
    if (detailsLower.includes('edit')) return <Edit className="h-4 w-4" />
    return <RefreshCw className="h-4 w-4" />
  }
  if (actionLower === 'delete') return <Trash2 className="h-4 w-4" />
  if (actionLower === 'view') return <Eye className="h-4 w-4" />
  if (actionLower === 'download') return <Download className="h-4 w-4" />
  if (actionLower === 'upload') return <Upload className="h-4 w-4" />
  
  return <FileText className="h-4 w-4" />
}

const getActivityColor = (action: string, details: string) => {
  const actionLower = action.toLowerCase()
  const detailsLower = details.toLowerCase()
  
  if (actionLower === 'create') return 'bg-blue-500'
  if (actionLower === 'update') {
    if (detailsLower.includes('approved')) return 'bg-green-500'
    if (detailsLower.includes('rejected')) return 'bg-red-500'
    if (detailsLower.includes('disbursed')) return 'bg-emerald-500'
    if (detailsLower.includes('schedule')) return 'bg-purple-500'
    return 'bg-orange-500'
  }
  if (actionLower === 'delete') return 'bg-red-500'
  
  return 'bg-gray-500'
}

const getRoleBadgeColor = (role: string) => {
  switch (role) {
    case 'SUPER_ADMIN': return 'bg-purple-100 text-purple-800 border-purple-200'
    case 'MANAGER': return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'CREDIT_OFFICER': return 'bg-green-100 text-green-800 border-green-200'
    case 'FIELD_OFFICER': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    default: return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

const formatRoleName = (role: string) => {
  return role.split('_').map(word =>
    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  ).join(' ')
}

// Function to parse and format activity details
const formatActivityDetails = (action: string, details: string, resource: string) => {
  // If details is a JSON string, try to parse it
  try {
    const parsed = JSON.parse(details)

    // Format based on action type
    switch (action) {
      case 'APPROVE':
        if (parsed.status === 'APPROVED') {
          return `Loan approved successfully. ${parsed.loanType ? `Loan Type: ${parsed.loanType}` : ''} ${parsed.customerName ? `Customer: ${parsed.customerName}` : ''} ${parsed.loanNumber ? `Loan Number: ${parsed.loanNumber}` : ''}`
        }
        break

      case 'DISBURSE':
        if (parsed.status === 'ACTIVE') {
          const amount = parsed.disbursedAmount ? `Amount: LKR ${Number(parsed.disbursedAmount).toLocaleString()}` : ''
          const method = parsed.disbursementMethod ? `Method: ${parsed.disbursementMethod}` : ''
          const reference = parsed.disbursementReference ? `Reference: ${parsed.disbursementReference}` : ''
          return `Loan disbursed successfully. ${[amount, method, reference].filter(Boolean).join(', ')}`
        }
        break

      case 'CREATE':
        if (resource === 'Loan') {
          const amount = parsed.principalAmount ? `Amount: LKR ${Number(parsed.principalAmount).toLocaleString()}` : ''
          const customer = parsed.customerName ? `Customer: ${parsed.customerName}` : ''
          const loanType = parsed.loanType ? `Type: ${parsed.loanType}` : ''
          return `New loan application created. ${[customer, amount, loanType].filter(Boolean).join(', ')}`
        }
        break

      case 'UPDATE':
        if (parsed.status) {
          return `Loan status updated to ${parsed.status}. ${parsed.loanNumber ? `Loan: ${parsed.loanNumber}` : ''}`
        }
        break

      case 'CREATE_COMMENT':
        return `Added ${parsed.isInternal ? 'internal' : 'public'} comment: "${parsed.comment}"`

      default:
        // Try to format common fields
        const commonFields = []
        if (parsed.status) commonFields.push(`Status: ${parsed.status}`)
        if (parsed.amount) commonFields.push(`Amount: LKR ${Number(parsed.amount).toLocaleString()}`)
        if (parsed.loanNumber) commonFields.push(`Loan: ${parsed.loanNumber}`)
        if (parsed.customerName) commonFields.push(`Customer: ${parsed.customerName}`)

        if (commonFields.length > 0) {
          return commonFields.join(', ')
        }
    }
  } catch (e) {
    // If not JSON or parsing fails, return the original details
  }

  // Fallback: clean up the raw details string
  if (details.startsWith('{"') && details.endsWith('"}')) {
    try {
      const parsed = JSON.parse(details)
      const entries = Object.entries(parsed)
        .filter(([key, value]) => value !== null && value !== undefined && value !== '')
        .map(([key, value]) => {
          const formattedKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
          if (key.includes('Amount') && typeof value === 'number') {
            return `${formattedKey}: LKR ${value.toLocaleString()}`
          }
          return `${formattedKey}: ${value}`
        })

      if (entries.length > 0) {
        return entries.join(', ')
      }
    } catch (e) {
      // Continue to fallback
    }
  }

  return details
}

export default function LoanActivityLog({ loanId }: LoanActivityLogProps) {
  const [activities, setActivities] = useState<ActivityLogEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchActivities()
  }, [loanId])

  const fetchActivities = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/loans/${loanId}/activities`)
      
      if (response.ok) {
        const data = await response.json()
        setActivities(data)
      } else {
        setError('Failed to fetch loan activities')
      }
    } catch (error) {
      console.error('Error fetching activities:', error)
      setError('Failed to fetch loan activities')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Loan Activity Log
          </CardTitle>
          <CardDescription>Complete history of all loan activities</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Loan Activity Log
          </CardTitle>
          <CardDescription>Complete history of all loan activities</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8 text-red-600">
            <AlertCircle className="h-5 w-5 mr-2" />
            {error}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Loan Activity Log
        </CardTitle>
        <CardDescription>
          Complete history of all loan activities - {activities.length} entries
        </CardDescription>
      </CardHeader>
      <CardContent>
        {activities.length === 0 ? (
          <div className="flex items-center justify-center py-8 text-gray-500">
            <FileText className="h-5 w-5 mr-2" />
            No activities recorded yet
          </div>
        ) : (
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"></div>
            
            <div className="space-y-6">
              {activities.map((activity, index) => (
                <div key={activity.id} className="relative flex items-start gap-4">
                  {/* Timeline dot */}
                  <div className={`relative z-10 flex items-center justify-center w-12 h-12 rounded-full ${getActivityColor(activity.action, activity.details)} text-white shadow-lg`}>
                    {getActivityIcon(activity.action, activity.details)}
                  </div>
                  
                  {/* Activity content */}
                  <div className="flex-1 min-w-0 bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-semibold text-gray-900">
                            {activity.action} {activity.resource}
                          </h4>
                          <Badge
                            variant="outline"
                            className={getRoleBadgeColor(activity.user.role)}
                          >
                            {formatRoleName(activity.user.role)}
                          </Badge>
                        </div>
                        
                        <p className="text-gray-700 mb-3">
                          {formatActivityDetails(activity.action, activity.details, activity.resource)}
                        </p>
                        
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <div className="flex items-center gap-2">
                            <UserAvatar
                              name={`${activity.user.firstName} ${activity.user.lastName}`}
                              className="h-6 w-6 text-xs"
                            />
                            <span className="font-medium">
                              {activity.user.firstName} {activity.user.lastName}
                            </span>
                          </div>
                          
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            <span>
                              {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
                            </span>
                          </div>

                          <div className="text-xs text-gray-400">
                            {new Date(activity.timestamp).toLocaleString()}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
