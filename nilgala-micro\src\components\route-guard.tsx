'use client'

import { useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter, usePathname } from 'next/navigation'

export function RouteGuard({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    // Prevent browser back navigation to auth pages when authenticated
    const handlePopState = (event: PopStateEvent) => {
      if (session && (pathname === '/auth/signin' || pathname === '/auth/signup')) {
        event.preventDefault()
        router.replace('/dashboard')
        return
      }
    }

    // Add history state to prevent back navigation to auth pages
    if (session && (pathname === '/dashboard' || pathname.startsWith('/dashboard'))) {
      // Replace the current history entry to prevent going back to signin
      window.history.replaceState(null, '', pathname)
      
      // Add a new history entry
      window.history.pushState(null, '', pathname)
    }

    window.addEventListener('popstate', handlePopState)

    return () => {
      window.removeEventListener('popstate', handlePopState)
    }
  }, [session, pathname, router])

  // Additional protection: if user somehow reaches auth pages while authenticated
  useEffect(() => {
    if (status === 'loading') return

    if (session && (pathname === '/auth/signin' || pathname === '/auth/signup')) {
      router.replace('/dashboard')
    }
  }, [session, status, pathname, router])

  return <>{children}</>
}
