'use client'

import { useEffect, useState } from 'react'
import { useSession, signOut } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'

const SESSION_TIMEOUT = 30 * 60 * 1000 // 30 minutes in milliseconds
const WARNING_TIME = 5 * 60 * 1000 // Show warning 5 minutes before timeout

export function SessionTimeout() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [showWarning, setShowWarning] = useState(false)
  const [timeLeft, setTimeLeft] = useState(0)

  useEffect(() => {
    if (status !== 'authenticated' || !session) return

    let warningTimer: NodeJS.Timeout
    let logoutTimer: NodeJS.Timeout
    let countdownTimer: NodeJS.Timeout

    const resetTimers = () => {
      clearTimeout(warningTimer)
      clearTimeout(logoutTimer)
      clearTimeout(countdownTimer)
      setShowWarning(false)

      // Set warning timer
      warningTimer = setTimeout(() => {
        setShowWarning(true)
        setTimeLeft(WARNING_TIME)

        // Start countdown
        countdownTimer = setInterval(() => {
          setTimeLeft((prev) => {
            if (prev <= 1000) {
              clearInterval(countdownTimer)
              return 0
            }
            return prev - 1000
          })
        }, 1000)
      }, SESSION_TIMEOUT - WARNING_TIME)

      // Set logout timer
      logoutTimer = setTimeout(() => {
        handleLogout()
      }, SESSION_TIMEOUT)
    }

    const handleActivity = () => {
      if (showWarning) {
        setShowWarning(false)
      }
      resetTimers()
    }

    const handleLogout = async () => {
      setShowWarning(false)
      await signOut({ redirect: false })
      router.push('/auth/signin?message=Session expired')
    }

    const extendSession = () => {
      setShowWarning(false)
      resetTimers()
    }

    // Activity events to track
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']

    // Add event listeners
    events.forEach(event => {
      document.addEventListener(event, handleActivity, true)
    })

    // Initialize timers
    resetTimers()

    // Cleanup
    return () => {
      clearTimeout(warningTimer)
      clearTimeout(logoutTimer)
      clearTimeout(countdownTimer)
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true)
      })
    }
  }, [session, status, router, showWarning])

  const formatTime = (milliseconds: number) => {
    const minutes = Math.floor(milliseconds / 60000)
    const seconds = Math.floor((milliseconds % 60000) / 1000)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  const handleExtendSession = () => {
    setShowWarning(false)
    // Reset activity tracking
    const event = new Event('mousedown')
    document.dispatchEvent(event)
  }

  const handleLogoutNow = async () => {
    setShowWarning(false)
    await signOut({ redirect: false })
    router.push('/auth/signin')
  }

  if (status !== 'authenticated' || !showWarning) {
    return null
  }

  return (
    <Dialog open={showWarning} onOpenChange={() => {}}>
      <DialogContent className="sm:max-w-md" onPointerDownOutside={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle>Session Timeout Warning</DialogTitle>
          <DialogDescription>
            Your session will expire in {formatTime(timeLeft)} due to inactivity.
            Would you like to extend your session?
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={handleLogoutNow}>
            Logout Now
          </Button>
          <Button onClick={handleExtendSession}>
            Extend Session
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
