'use client'

import React, { useState, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Upload, X, FileText, Image, File } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

// Map required document names to DocumentType enum values
const mapDocumentNameToType = (documentName: string): string => {
  const name = documentName.toLowerCase()

  if (name.includes('nic') || name.includes('national identity') || name.includes('identity card')) {
    return 'NATIONAL_ID'
  }
  if (name.includes('passport')) {
    return 'PASSPORT'
  }
  if (name.includes('driving') || name.includes('license')) {
    return 'DRIVING_LICENSE'
  }
  if (name.includes('utility') || name.includes('address proof') || name.includes('address')) {
    return 'UTILITY_BILL'
  }
  if (name.includes('bank statement') || name.includes('bank')) {
    return 'BANK_STATEMENT'
  }
  if (name.includes('income') || name.includes('salary') || name.includes('employment')) {
    return 'INCOME_CERTIFICATE'
  }
  if (name.includes('property') || name.includes('deed') || name.includes('land')) {
    return 'PROPERTY_DEED'
  }
  if (name.includes('business') || name.includes('registration') || name.includes('license')) {
    return 'BUSINESS_REGISTRATION'
  }
  if (name.includes('guarantor')) {
    return 'GUARANTOR_ID'
  }
  if (name.includes('loan application') || name.includes('application')) {
    return 'LOAN_APPLICATION'
  }

  return 'OTHER'
}

interface DocumentUpload {
  id?: string
  documentName: string
  file?: File
  fileName?: string
  fileSize?: number
  uploadedAt?: string
  uploading?: boolean
  requiredDocumentId?: string // Link to required document
}

interface RequiredDocument {
  id: string
  name: string
  description?: string
  isActive: boolean
}

interface DocumentUploadProps {
  documents: DocumentUpload[]
  onDocumentsChange: (documents: DocumentUpload[]) => void
  loanId?: string
  customerId?: string
  disabled?: boolean
  requiredDocuments?: RequiredDocument[] // Required documents from loan type
}

export default function DocumentUpload({
  documents,
  onDocumentsChange,
  loanId,
  customerId,
  disabled = false,
  requiredDocuments = []
}: DocumentUploadProps) {
  const [uploading, setUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { toast } = useToast()

  // Initialize required documents if they don't exist in documents array
  const initializeRequiredDocuments = () => {
    if (requiredDocuments.length === 0) return

    const existingDocumentIds = documents.map(doc => doc.requiredDocumentId).filter(Boolean)
    const missingRequiredDocs = requiredDocuments.filter(reqDoc =>
      !existingDocumentIds.includes(reqDoc.id)
    )

    if (missingRequiredDocs.length > 0) {
      const newDocuments = missingRequiredDocs.map(reqDoc => ({
        documentName: reqDoc.name,
        requiredDocumentId: reqDoc.id,
        file: undefined
      }))
      onDocumentsChange([...documents, ...newDocuments])
    }
  }

  // Initialize required documents on component mount or when required documents change
  React.useEffect(() => {
    initializeRequiredDocuments()
  }, [requiredDocuments, documents])

  const addDocument = () => {
    const newDocument: DocumentUpload = {
      documentName: '',
      file: undefined
    }
    onDocumentsChange([...documents, newDocument])
  }

  const removeDocument = (index: number) => {
    const newDocuments = documents.filter((_, i) => i !== index)
    onDocumentsChange(newDocuments)
  }

  const updateDocumentName = (index: number, name: string) => {
    const newDocuments = [...documents]
    newDocuments[index].documentName = name
    onDocumentsChange(newDocuments)
  }

  const handleFileSelect = (index: number, file: File) => {
    // Validate file
    if (file.size > 10 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "File size must be less than 10MB",
        variant: "destructive"
      })
      return
    }

    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf']
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "Invalid file type",
        description: "Only JPEG, PNG, and PDF files are allowed",
        variant: "destructive"
      })
      return
    }

    const newDocuments = [...documents]
    newDocuments[index].file = file
    newDocuments[index].fileName = file.name
    newDocuments[index].fileSize = file.size
    onDocumentsChange(newDocuments)
  }

  const uploadDocument = async (index: number) => {
    const document = documents[index]
    if (!document.file || !document.documentName.trim()) {
      toast({
        title: "Missing information",
        description: "Please provide both document name and file",
        variant: "destructive"
      })
      return
    }

    setUploading(true)
    const newDocuments = [...documents]
    newDocuments[index].uploading = true
    onDocumentsChange(newDocuments)

    try {
      const formData = new FormData()
      formData.append('file', document.file)
      formData.append('documentName', document.documentName)

      // Map document name to appropriate document type
      const documentType = mapDocumentNameToType(document.documentName)
      formData.append('documentType', documentType)

      if (loanId) formData.append('loanId', loanId)
      if (customerId) formData.append('customerId', customerId)

      const response = await fetch('/api/documents', {
        method: 'POST',
        body: formData
      })

      if (response.ok) {
        const result = await response.json()
        newDocuments[index] = {
          ...newDocuments[index],
          id: result.id,
          uploadedAt: result.uploadedAt,
          uploading: false
        }
        onDocumentsChange(newDocuments)
        
        toast({
          title: "Success",
          description: "Document uploaded successfully"
        })
      } else {
        const error = await response.json()
        throw new Error(error.error || 'Upload failed')
      }
    } catch (error) {
      console.error('Upload error:', error)
      newDocuments[index].uploading = false
      onDocumentsChange(newDocuments)
      
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "Failed to upload document",
        variant: "destructive"
      })
    } finally {
      setUploading(false)
    }
  }

  const getFileIcon = (fileName?: string, mimeType?: string) => {
    if (!fileName && !mimeType) return <File className="h-4 w-4" />
    
    const type = mimeType || fileName?.split('.').pop()?.toLowerCase()
    
    if (type?.includes('image') || ['jpg', 'jpeg', 'png', 'gif'].includes(type || '')) {
      return <Image className="h-4 w-4" />
    }
    
    if (type?.includes('pdf') || type === 'pdf') {
      return <FileText className="h-4 w-4" />
    }
    
    return <File className="h-4 w-4" />
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // Separate required and optional documents
  const requiredDocs = documents.filter(doc => doc.requiredDocumentId)
  const optionalDocs = documents.filter(doc => !doc.requiredDocumentId)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Document Upload</span>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addDocument}
            disabled={disabled}
          >
            <Upload className="h-4 w-4 mr-2" />
            Add Optional Document
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Required Documents Section */}
        {requiredDocs.length > 0 && (
          <div>
            <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
              <FileText className="h-4 w-4 text-blue-600" />
              Required Documents
            </h4>
            <div className="space-y-4">
              {requiredDocs.map((document, index) => {
                const actualIndex = documents.indexOf(document)
                const requiredDoc = requiredDocuments.find(rd => rd.id === document.requiredDocumentId)
                const isUploaded = !!document.id

                return (
                  <div key={`required-${index}`} className={`border rounded-lg p-4 space-y-3 ${
                    isUploaded ? 'bg-green-50 border-green-200' : 'bg-blue-50 border-blue-200'
                  }`}>
                    <div className="flex items-center justify-between">
                      <div className="flex-1 space-y-2">
                        <div>
                          <Label htmlFor={`req-doc-${index}`} className="flex items-center gap-2">
                            <span className={`font-medium ${isUploaded ? 'text-green-900' : 'text-blue-900'}`}>
                              {document.documentName}
                            </span>
                            <Badge variant={isUploaded ? "default" : "secondary"} className="text-xs">
                              {isUploaded ? "Uploaded" : "Required"}
                            </Badge>
                          </Label>
                          {requiredDoc?.description && (
                            <p className={`text-xs mt-1 ${isUploaded ? 'text-green-700' : 'text-blue-700'}`}>
                              {requiredDoc.description}
                            </p>
                          )}
                        </div>

                        {!isUploaded && (
                          <div>
                            <Label>Select File</Label>
                            <Input
                              type="file"
                              accept=".jpg,.jpeg,.png,.pdf"
                              onChange={(e) => {
                                const file = e.target.files?.[0]
                                if (file) handleFileSelect(actualIndex, file)
                              }}
                              disabled={disabled || document.uploading}
                            />
                            {document.file && (
                              <div className="mt-2 flex items-center gap-2 text-sm text-gray-600">
                                <File className="h-4 w-4" />
                                <span>{document.fileName} ({(document.fileSize! / 1024).toFixed(1)} KB)</span>
                              </div>
                            )}
                          </div>
                        )}

                        {isUploaded && (
                          <div className="flex items-center gap-2 text-sm text-green-700">
                            <FileText className="h-4 w-4" />
                            <span>{document.fileName}</span>
                            <span className="text-gray-500">
                              • Uploaded {new Date(document.uploadedAt!).toLocaleDateString()}
                            </span>
                          </div>
                        )}
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center justify-between pt-2">
                        <div className="flex items-center gap-2">
                          {document.uploading && (
                            <div className="flex items-center gap-2 text-sm text-blue-600">
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                              Uploading...
                            </div>
                          )}
                        </div>

                        <div className="flex gap-2">
                          {!isUploaded && document.file && (
                            <Button
                              type="button"
                              size="sm"
                              onClick={() => uploadDocument(actualIndex)}
                              disabled={disabled || document.uploading || uploading}
                              className="bg-blue-600 hover:bg-blue-700"
                            >
                              <Upload className="h-4 w-4 mr-2" />
                              {document.uploading ? 'Uploading...' : 'Upload'}
                            </Button>
                          )}

                          {isUploaded && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeDocument(actualIndex)}
                              disabled={disabled}
                              className="text-red-600 hover:text-red-700"
                            >
                              <X className="h-4 w-4 mr-1" />
                              Remove
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {/* Optional Documents Section */}
        {optionalDocs.length > 0 && (
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Additional Documents</h4>
            <div className="space-y-4">
              {optionalDocs.map((document, index) => {
                const actualIndex = documents.indexOf(document)
                return (
                  <div key={`optional-${index}`} className="border rounded-lg p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 space-y-2">
                        <div>
                          <Label htmlFor={`opt-doc-${index}`}>Document Name</Label>
                          <Input
                            id={`opt-doc-${index}`}
                            value={document.documentName}
                            onChange={(e) => updateDocumentName(actualIndex, e.target.value)}
                            placeholder="e.g., National ID, Bank Statement, etc."
                            disabled={disabled || document.uploading}
                          />
                        </div>

                        {!document.id && (
                          <div>
                            <Label>File</Label>
                            <Input
                              type="file"
                              accept=".jpg,.jpeg,.png,.pdf"
                              onChange={(e) => {
                                const file = e.target.files?.[0]
                                if (file) handleFileSelect(actualIndex, file)
                              }}
                              disabled={disabled || document.uploading}
                            />
                          </div>
                        )}
                      </div>

                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeDocument(actualIndex)}
                        disabled={disabled || document.uploading}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>

                    {document.file && (
                      <div className="flex items-center justify-between bg-gray-50 p-2 rounded">
                        <div className="flex items-center gap-2">
                          {getFileIcon(document.fileName, document.file.type)}
                          <span className="text-sm">{document.fileName}</span>
                          <Badge variant="outline">{formatFileSize(document.fileSize || 0)}</Badge>
                        </div>

                        {!document.id && (
                          <Button
                            type="button"
                            size="sm"
                            onClick={() => uploadDocument(actualIndex)}
                            disabled={disabled || document.uploading || uploading}
                          >
                            {document.uploading ? 'Uploading...' : 'Upload'}
                          </Button>
                        )}

                        {document.id && (
                          <Badge variant="default">Uploaded</Badge>
                        )}
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {/* Show message when no documents */}
        {documents.length === 0 && requiredDocuments.length === 0 && (
          <p className="text-gray-500 text-center py-4">
            No documents added yet. Click "Add Optional Document" to upload loan-related documents.
          </p>
        )}
      </CardContent>
    </Card>
  )
}
