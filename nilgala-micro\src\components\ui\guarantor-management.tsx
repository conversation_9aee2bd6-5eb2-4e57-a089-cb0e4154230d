'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Upload, X, FileText, User, UserPlus, Plus, Trash2 } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface Customer {
  id: string
  firstName: string
  lastName: string
  phone: string
  email?: string
}

interface GuarantorDocument {
  id?: string
  documentName: string
  file?: File
  fileName?: string
  fileSize?: number
  uploadedAt?: string
  uploading?: boolean
}

interface NewGuarantor {
  firstName: string
  lastName: string
  nationalId: string
  phone: string
  email?: string
  address: string
  city: string
  state: string
  dateOfBirth?: string
  occupation?: string
  monthlyIncome?: number
  relationship?: string
  documents: GuarantorDocument[]
}

interface SelectedGuarantor {
  type: 'existing' | 'new'
  customerId?: string
  guarantorData?: NewGuarantor
  documents?: GuarantorDocument[] // Add documents for existing customers
}

interface GuarantorManagementProps {
  customers: Customer[]
  selectedCustomerId: string
  maxGuarantors: number
  guarantors: SelectedGuarantor[]
  onGuarantorsChange: (guarantors: SelectedGuarantor[]) => void
  disabled?: boolean
}

export default function GuarantorManagement({
  customers,
  selectedCustomerId,
  maxGuarantors,
  guarantors,
  onGuarantorsChange,
  disabled = false
}: GuarantorManagementProps) {
  const { toast } = useToast()

  const addGuarantor = () => {
    if (guarantors.length >= maxGuarantors) return
    
    const newGuarantor: SelectedGuarantor = {
      type: 'existing',
      customerId: '',
      documents: []
    }
    onGuarantorsChange([...guarantors, newGuarantor])
  }

  const removeGuarantor = (index: number) => {
    const newGuarantors = guarantors.filter((_, i) => i !== index)
    onGuarantorsChange(newGuarantors)
  }

  const updateGuarantor = (index: number, guarantor: SelectedGuarantor) => {
    const newGuarantors = [...guarantors]
    newGuarantors[index] = guarantor
    onGuarantorsChange(newGuarantors)
  }

  const addDocumentToGuarantor = (guarantorIndex: number) => {
    if (guarantors[guarantorIndex].type !== 'new' || !guarantors[guarantorIndex].guarantorData) return
    
    const newDocument: GuarantorDocument = {
      documentName: '',
      file: undefined
    }
    
    const updatedGuarantor = { ...guarantors[guarantorIndex] }
    updatedGuarantor.guarantorData!.documents.push(newDocument)
    updateGuarantor(guarantorIndex, updatedGuarantor)
  }

  const removeDocumentFromGuarantor = (guarantorIndex: number, docIndex: number) => {
    if (guarantors[guarantorIndex].type !== 'new' || !guarantors[guarantorIndex].guarantorData) return
    
    const updatedGuarantor = { ...guarantors[guarantorIndex] }
    updatedGuarantor.guarantorData!.documents = updatedGuarantor.guarantorData!.documents.filter((_, i) => i !== docIndex)
    updateGuarantor(guarantorIndex, updatedGuarantor)
  }

  const updateGuarantorDocument = (guarantorIndex: number, docIndex: number, field: string, value: any) => {
    if (guarantors[guarantorIndex].type !== 'new' || !guarantors[guarantorIndex].guarantorData) return
    
    const updatedGuarantor = { ...guarantors[guarantorIndex] }
    updatedGuarantor.guarantorData!.documents[docIndex] = {
      ...updatedGuarantor.guarantorData!.documents[docIndex],
      [field]: value
    }
    updateGuarantor(guarantorIndex, updatedGuarantor)
  }

  const handleFileSelect = (guarantorIndex: number, docIndex: number, file: File) => {
    // Validate file
    if (file.size > 10 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "File size must be less than 10MB",
        variant: "destructive"
      })
      return
    }

    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf']
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "Invalid file type",
        description: "Only JPEG, PNG, and PDF files are allowed",
        variant: "destructive"
      })
      return
    }

    const guarantor = guarantors[guarantorIndex]

    if (guarantor.type === 'existing') {
      // Handle existing customer documents
      const newGuarantor = { ...guarantor }
      if (!newGuarantor.documents) newGuarantor.documents = []
      newGuarantor.documents[docIndex] = {
        ...newGuarantor.documents[docIndex],
        file,
        fileName: file.name,
        fileSize: file.size
      }
      updateGuarantor(guarantorIndex, newGuarantor)
    } else {
      // Handle new guarantor documents
      updateGuarantorDocument(guarantorIndex, docIndex, 'file', file)
      updateGuarantorDocument(guarantorIndex, docIndex, 'fileName', file.name)
      updateGuarantorDocument(guarantorIndex, docIndex, 'fileSize', file.size)
    }
  }

  const availableCustomers = customers.filter(customer => 
    customer.id !== selectedCustomerId && 
    !guarantors.some(g => g.type === 'existing' && g.customerId === customer.id)
  )

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Guarantor Information
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          This loan type requires {maxGuarantors} guarantor(s). You can select existing customers or add new guarantor details.
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        {guarantors.map((guarantor, index) => (
          <Card key={index} className="border-dashed">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Guarantor {index + 1}</h4>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeGuarantor(index)}
                  disabled={disabled}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Tabs 
                value={guarantor.type} 
                onValueChange={(value) => {
                  const newGuarantor: SelectedGuarantor = {
                    type: value as 'existing' | 'new',
                    ...(value === 'existing' ? { customerId: '', documents: [] } : {
                      guarantorData: {
                        firstName: '',
                        lastName: '',
                        nationalId: '',
                        phone: '',
                        email: '',
                        address: '',
                        city: '',
                        state: '',
                        documents: []
                      }
                    })
                  }
                  updateGuarantor(index, newGuarantor)
                }}
              >
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="existing" className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Existing Customer
                  </TabsTrigger>
                  <TabsTrigger value="new" className="flex items-center gap-2">
                    <UserPlus className="h-4 w-4" />
                    New Guarantor
                  </TabsTrigger>
                </TabsList>
                
                <TabsContent value="existing" className="space-y-4">
                  <div>
                    <Label>Select Customer</Label>
                    <Select
                      value={guarantor.customerId || ''}
                      onValueChange={(value) => updateGuarantor(index, { ...guarantor, customerId: value })}
                      disabled={disabled}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a customer as guarantor" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableCustomers.map((customer) => (
                          <SelectItem key={customer.id} value={customer.id}>
                            {customer.firstName} {customer.lastName} - {customer.phone}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Display selected customer details */}
                  {guarantor.customerId && (() => {
                    const selectedCustomer = customers.find(c => c.id === guarantor.customerId)
                    return selectedCustomer ? (
                      <div className="mt-4 p-4 bg-gray-50 rounded-lg border">
                        <h5 className="font-medium text-gray-900 mb-3">Selected Guarantor Details</h5>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-gray-600">Name:</span>
                            <p className="font-semibold">{selectedCustomer.firstName} {selectedCustomer.lastName}</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-600">Phone:</span>
                            <p className="font-semibold">{selectedCustomer.phone}</p>
                          </div>
                          {selectedCustomer.email && (
                            <div>
                              <span className="font-medium text-gray-600">Email:</span>
                              <p className="font-semibold">{selectedCustomer.email}</p>
                            </div>
                          )}
                          <div>
                            <span className="font-medium text-gray-600">Monthly Income:</span>
                            <p className="font-semibold">LKR {selectedCustomer.monthlyIncome?.toLocaleString() || 'N/A'}</p>
                          </div>
                        </div>
                      </div>
                    ) : null
                  })()}

                  {/* Document Upload for Existing Customer */}
                  {guarantor.customerId && (
                    <div className="mt-4">
                      <h5 className="font-medium text-gray-900 mb-3">Guarantor Documents</h5>
                      <div className="space-y-3">
                        {(guarantor.documents || []).map((doc, docIndex) => (
                          <div key={docIndex} className="flex items-center gap-3 p-3 border rounded-lg">
                            <div className="flex-1">
                              <Input
                                placeholder="Document name (e.g., National ID, Bank Statement)"
                                value={doc.documentName}
                                onChange={(e) => {
                                  const newGuarantor = { ...guarantor }
                                  if (!newGuarantor.documents) newGuarantor.documents = []
                                  newGuarantor.documents[docIndex].documentName = e.target.value
                                  updateGuarantor(index, newGuarantor)
                                }}
                                disabled={disabled}
                              />
                            </div>
                            <div className="flex-1">
                              <Input
                                type="file"
                                accept=".pdf,.jpg,.jpeg,.png"
                                onChange={(e) => {
                                  const file = e.target.files?.[0]
                                  if (file) {
                                    handleFileSelect(index, docIndex, file)
                                  }
                                }}
                                disabled={disabled || doc.uploading}
                              />
                            </div>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const newGuarantor = { ...guarantor }
                                if (!newGuarantor.documents) newGuarantor.documents = []
                                newGuarantor.documents.splice(docIndex, 1)
                                updateGuarantor(index, newGuarantor)
                              }}
                              disabled={disabled}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}

                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const newGuarantor = { ...guarantor }
                            if (!newGuarantor.documents) newGuarantor.documents = []
                            newGuarantor.documents.push({
                              documentName: '',
                              file: undefined,
                              fileName: '',
                              fileSize: 0
                            })
                            updateGuarantor(index, newGuarantor)
                          }}
                          disabled={disabled}
                          className="w-full"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Document
                        </Button>
                      </div>
                    </div>
                  )}
                </TabsContent>
                
                <TabsContent value="new" className="space-y-4">
                  {guarantor.guarantorData && (
                    <>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label>First Name *</Label>
                          <Input
                            value={guarantor.guarantorData.firstName}
                            onChange={(e) => {
                              const updatedGuarantor = { ...guarantor }
                              updatedGuarantor.guarantorData!.firstName = e.target.value
                              updateGuarantor(index, updatedGuarantor)
                            }}
                            disabled={disabled}
                          />
                        </div>
                        <div>
                          <Label>Last Name *</Label>
                          <Input
                            value={guarantor.guarantorData.lastName}
                            onChange={(e) => {
                              const updatedGuarantor = { ...guarantor }
                              updatedGuarantor.guarantorData!.lastName = e.target.value
                              updateGuarantor(index, updatedGuarantor)
                            }}
                            disabled={disabled}
                          />
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label>National ID *</Label>
                          <Input
                            value={guarantor.guarantorData.nationalId}
                            onChange={(e) => {
                              const updatedGuarantor = { ...guarantor }
                              updatedGuarantor.guarantorData!.nationalId = e.target.value
                              updateGuarantor(index, updatedGuarantor)
                            }}
                            disabled={disabled}
                          />
                        </div>
                        <div>
                          <Label>Phone *</Label>
                          <Input
                            value={guarantor.guarantorData.phone}
                            onChange={(e) => {
                              const updatedGuarantor = { ...guarantor }
                              updatedGuarantor.guarantorData!.phone = e.target.value
                              updateGuarantor(index, updatedGuarantor)
                            }}
                            disabled={disabled}
                          />
                        </div>
                      </div>
                      
                      <div>
                        <Label>Email</Label>
                        <Input
                          type="email"
                          value={guarantor.guarantorData.email || ''}
                          onChange={(e) => {
                            const updatedGuarantor = { ...guarantor }
                            updatedGuarantor.guarantorData!.email = e.target.value
                            updateGuarantor(index, updatedGuarantor)
                          }}
                          disabled={disabled}
                        />
                      </div>
                      
                      <div>
                        <Label>Address *</Label>
                        <Input
                          value={guarantor.guarantorData.address}
                          onChange={(e) => {
                            const updatedGuarantor = { ...guarantor }
                            updatedGuarantor.guarantorData!.address = e.target.value
                            updateGuarantor(index, updatedGuarantor)
                          }}
                          disabled={disabled}
                        />
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label>City *</Label>
                          <Input
                            value={guarantor.guarantorData.city}
                            onChange={(e) => {
                              const updatedGuarantor = { ...guarantor }
                              updatedGuarantor.guarantorData!.city = e.target.value
                              updateGuarantor(index, updatedGuarantor)
                            }}
                            disabled={disabled}
                          />
                        </div>
                        <div>
                          <Label>State *</Label>
                          <Input
                            value={guarantor.guarantorData.state}
                            onChange={(e) => {
                              const updatedGuarantor = { ...guarantor }
                              updatedGuarantor.guarantorData!.state = e.target.value
                              updateGuarantor(index, updatedGuarantor)
                            }}
                            disabled={disabled}
                          />
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label>Occupation</Label>
                          <Input
                            value={guarantor.guarantorData.occupation || ''}
                            onChange={(e) => {
                              const updatedGuarantor = { ...guarantor }
                              updatedGuarantor.guarantorData!.occupation = e.target.value
                              updateGuarantor(index, updatedGuarantor)
                            }}
                            disabled={disabled}
                          />
                        </div>
                        <div>
                          <Label>Monthly Income</Label>
                          <Input
                            type="number"
                            value={guarantor.guarantorData.monthlyIncome || ''}
                            onChange={(e) => {
                              const updatedGuarantor = { ...guarantor }
                              updatedGuarantor.guarantorData!.monthlyIncome = parseFloat(e.target.value) || undefined
                              updateGuarantor(index, updatedGuarantor)
                            }}
                            disabled={disabled}
                          />
                        </div>
                      </div>
                      
                      <div>
                        <Label>Relationship to Borrower</Label>
                        <Input
                          value={guarantor.guarantorData.relationship || ''}
                          onChange={(e) => {
                            const updatedGuarantor = { ...guarantor }
                            updatedGuarantor.guarantorData!.relationship = e.target.value
                            updateGuarantor(index, updatedGuarantor)
                          }}
                          placeholder="e.g., Friend, Relative, Colleague"
                          disabled={disabled}
                        />
                      </div>
                      
                      {/* Documents Section */}
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <Label>Documents</Label>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => addDocumentToGuarantor(index)}
                            disabled={disabled}
                          >
                            <Upload className="h-4 w-4 mr-2" />
                            Add Document
                          </Button>
                        </div>
                        
                        {guarantor.guarantorData.documents.map((document, docIndex) => (
                          <div key={docIndex} className="flex items-end gap-3 p-3 border rounded-lg">
                            <div className="flex-1 space-y-2">
                              <div>
                                <Label>Document Name</Label>
                                <Input
                                  value={document.documentName}
                                  onChange={(e) => updateGuarantorDocument(index, docIndex, 'documentName', e.target.value)}
                                  placeholder="e.g., National ID, Address Proof, Income Certificate"
                                  disabled={disabled}
                                />
                              </div>
                              
                              <div>
                                <Label>File</Label>
                                <Input
                                  type="file"
                                  accept=".jpg,.jpeg,.png,.pdf"
                                  onChange={(e) => {
                                    const file = e.target.files?.[0]
                                    if (file) handleFileSelect(index, docIndex, file)
                                  }}
                                  disabled={disabled}
                                />
                                {document.fileName && (
                                  <div className="flex items-center gap-2 mt-1">
                                    <FileText className="h-4 w-4" />
                                    <span className="text-sm text-muted-foreground">{document.fileName}</span>
                                    <Badge variant="secondary" className="text-xs">
                                      {document.fileSize ? `${Math.round(document.fileSize / 1024)} KB` : ''}
                                    </Badge>
                                  </div>
                                )}
                              </div>
                            </div>
                            
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeDocumentFromGuarantor(index, docIndex)}
                              disabled={disabled}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </>
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        ))}
        
        {guarantors.length < maxGuarantors && (
          <Button
            type="button"
            variant="dashed"
            onClick={addGuarantor}
            disabled={disabled}
            className="w-full"
          >
            <UserPlus className="h-4 w-4 mr-2" />
            Add Guarantor ({guarantors.length}/{maxGuarantors})
          </Button>
        )}
      </CardContent>
    </Card>
  )
}
