'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { UserRole } from '@prisma/client'

interface PermissionData {
  allPermissions: Record<string, string[]>
  currentPermissions: Record<string, string[]>
  roles: string[]
}

interface UsePermissionsReturn {
  permissions: string[]
  hasPermission: (permission: string) => boolean
  hasAnyPermission: (permissions: string[]) => boolean
  hasAllPermissions: (permissions: string[]) => boolean
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function usePermissions(): UsePermissionsReturn {
  const { data: session } = useSession()
  const [permissionData, setPermissionData] = useState<PermissionData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchPermissions = useCallback(async () => {
    if (!session?.user?.role) {
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      // Use user-specific permissions endpoint for non-admin users
      const endpoint = session.user.role === 'SUPER_ADMIN'
        ? '/api/admin/permissions'
        : '/api/user/permissions'

      const response = await fetch(endpoint)
      if (!response.ok) {
        throw new Error('Failed to fetch permissions')
      }

      const data = await response.json()

      // Transform user permissions response to match admin format
      if (endpoint === '/api/user/permissions') {
        setPermissionData({
          allPermissions: data.availablePermissions,
          currentPermissions: { [data.role]: data.permissions },
          roles: [data.role]
        })
      } else {
        setPermissionData(data)
      }
    } catch (err) {
      console.error('Error fetching permissions:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch permissions')
    } finally {
      setLoading(false)
    }
  }, [session?.user?.role])

  useEffect(() => {
    fetchPermissions()
  }, [fetchPermissions])

  const userRole = session?.user?.role as UserRole
  const userPermissions = permissionData?.currentPermissions?.[userRole] || []

  const hasPermission = useCallback((permission: string): boolean => {
    return userPermissions.includes(permission)
  }, [userPermissions])

  const hasAnyPermission = useCallback((permissions: string[]): boolean => {
    return permissions.some(permission => userPermissions.includes(permission))
  }, [userPermissions])

  const hasAllPermissions = useCallback((permissions: string[]): boolean => {
    return permissions.every(permission => userPermissions.includes(permission))
  }, [userPermissions])

  return {
    permissions: userPermissions,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    loading,
    error,
    refetch: fetchPermissions
  }
}

// Hook specifically for Credit Officer permissions
export function useCreditOfficerPermissions() {
  const { data: session } = useSession()
  const permissions = usePermissions()

  // Only return permissions if user is a Credit Officer
  if (session?.user?.role !== 'CREDIT_OFFICER') {
    return {
      ...permissions,
      permissions: [],
      hasPermission: () => false,
      hasAnyPermission: () => false,
      hasAllPermissions: () => false
    }
  }

  return permissions
}

// Hook specifically for Higher Management permissions
export function useHigherManagementPermissions() {
  const { data: session } = useSession()
  const permissions = usePermissions()

  // Only return permissions if user is Higher Management
  if (session?.user?.role !== 'HIGHER_MANAGEMENT') {
    return {
      ...permissions,
      permissions: [],
      hasPermission: () => false,
      hasAnyPermission: () => false,
      hasAllPermissions: () => false
    }
  }

  return permissions
}

// Permission constants for Credit Officer features
export const CREDIT_OFFICER_PERMISSIONS = {
  // Customer management
  CREATE_CUSTOMERS: 'customers:create',
  READ_CUSTOMERS: 'customers:read',
  UPDATE_CUSTOMERS: 'customers:update',

  // Loan management
  CREATE_LOANS: 'loans:create',
  READ_LOANS: 'loans:read',
  UPDATE_LOANS: 'loans:update',
  DISBURSE_LOANS: 'loans:disburse',

  // Payment management
  CREATE_PAYMENTS: 'payments:create',
  READ_PAYMENTS: 'payments:read',
  UPDATE_PAYMENTS: 'payments:update',

  // Document management
  CREATE_DOCUMENTS: 'documents:create',
  READ_DOCUMENTS: 'documents:read',
  UPDATE_DOCUMENTS: 'documents:update',

  // Reports
  READ_REPORTS: 'reports:read',

  // Loan types
  READ_LOAN_TYPES: 'loan-types:read'
} as const

// Permission constants for Higher Management features
export const HIGHER_MANAGEMENT_PERMISSIONS = {
  // Portfolio management
  READ_PORTFOLIO: 'loans:read',
  MANAGE_PORTFOLIO: 'loans:update',

  // Financial oversight
  READ_FINANCIAL_REPORTS: 'reports:read',
  EXPORT_REPORTS: 'reports:export',
  CREATE_REPORTS: 'reports:create',

  // Risk management
  READ_AUDIT: 'audit:read',
  MANAGE_RISK: 'audit:update',

  // Team oversight
  READ_USERS: 'users:read',
  MANAGE_USERS: 'users:update',

  // Customer oversight
  READ_CUSTOMERS: 'customers:read',
  MANAGE_CUSTOMERS: 'customers:update',

  // Payment oversight
  READ_PAYMENTS: 'payments:read',
  MANAGE_PAYMENTS: 'payments:update',

  // Settings management
  READ_SETTINGS: 'settings:read',
  UPDATE_SETTINGS: 'settings:update'
} as const

// Helper function to check if user can access a specific dashboard feature
export function canAccessFeature(
  permissions: string[],
  requiredPermissions: string | string[]
): boolean {
  const required = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions]
  return required.every(permission => permissions.includes(permission))
}
