import { prisma } from '@/lib/prisma'
import { NextRequest } from 'next/server'

export interface AuditLogData {
  userId: string
  action: string
  resource: string
  resourceId?: string
  oldValues?: any
  newValues?: any
  ipAddress?: string
  userAgent?: string
  metadata?: any
}

export interface AuditContext {
  userId: string
  ipAddress?: string
  userAgent?: string
}

/**
 * Enhanced audit logging utility for comprehensive system tracking
 */
export class AuditLogger {
  /**
   * Create an audit log entry
   */
  static async log(data: AuditLogData): Promise<void> {
    try {
      await prisma.auditLog.create({
        data: {
          userId: data.userId,
          action: data.action,
          resource: data.resource,
          resourceId: data.resourceId,
          oldValues: data.oldValues,
          newValues: data.newValues,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          timestamp: new Date()
        }
      })
    } catch (error) {
      console.error('Failed to create audit log:', error)
      // Don't throw error to avoid breaking the main operation
    }
  }

  /**
   * Extract audit context from request
   */
  static getContextFromRequest(request: NextRequest, userId: string): AuditContext {
    return {
      userId,
      ipAddress: this.getClientIP(request),
      userAgent: request.headers.get('user-agent') || undefined
    }
  }

  /**
   * Get client IP address from request
   */
  private static getClientIP(request: NextRequest): string | undefined {
    // Check various headers for the real IP
    const forwarded = request.headers.get('x-forwarded-for')
    const realIP = request.headers.get('x-real-ip')
    const remoteAddr = request.headers.get('x-remote-addr')
    
    if (forwarded) {
      return forwarded.split(',')[0].trim()
    }
    
    return realIP || remoteAddr || undefined
  }

  /**
   * Log user authentication events
   */
  static async logAuth(context: AuditContext, action: 'LOGIN' | 'LOGOUT' | 'LOGIN_FAILED', metadata?: any): Promise<void> {
    await this.log({
      userId: context.userId,
      action,
      resource: 'User',
      resourceId: context.userId,
      newValues: {
        timestamp: new Date(),
        ...metadata
      },
      ipAddress: context.ipAddress,
      userAgent: context.userAgent
    })
  }

  /**
   * Log CRUD operations
   */
  static async logCRUD(
    context: AuditContext,
    action: 'CREATE' | 'READ' | 'UPDATE' | 'DELETE',
    resource: string,
    resourceId: string,
    oldValues?: any,
    newValues?: any
  ): Promise<void> {
    await this.log({
      userId: context.userId,
      action,
      resource,
      resourceId,
      oldValues,
      newValues,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent
    })
  }

  /**
   * Log loan-specific operations
   */
  static async logLoanOperation(
    context: AuditContext,
    action: 'APPROVE' | 'REJECT' | 'DISBURSE' | 'REQUEST_MORE_INFO',
    loanId: string,
    oldValues?: any,
    newValues?: any
  ): Promise<void> {
    await this.log({
      userId: context.userId,
      action,
      resource: 'Loan',
      resourceId: loanId,
      oldValues,
      newValues,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent
    })
  }

  /**
   * Log payment operations
   */
  static async logPaymentOperation(
    context: AuditContext,
    action: 'RECORD_PAYMENT' | 'REVERSE_PAYMENT' | 'UPDATE_PAYMENT',
    paymentId: string,
    oldValues?: any,
    newValues?: any
  ): Promise<void> {
    await this.log({
      userId: context.userId,
      action,
      resource: 'Payment',
      resourceId: paymentId,
      oldValues,
      newValues,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent
    })
  }

  /**
   * Log permission changes
   */
  static async logPermissionChange(
    context: AuditContext,
    role: string,
    oldPermissions: string[],
    newPermissions: string[]
  ): Promise<void> {
    await this.log({
      userId: context.userId,
      action: 'PERMISSIONS_UPDATED',
      resource: 'RolePermission',
      resourceId: role,
      oldValues: { permissions: oldPermissions },
      newValues: { permissions: newPermissions },
      ipAddress: context.ipAddress,
      userAgent: context.userAgent
    })
  }

  /**
   * Log system configuration changes
   */
  static async logSystemConfigChange(
    context: AuditContext,
    configKey: string,
    oldValue: any,
    newValue: any
  ): Promise<void> {
    await this.log({
      userId: context.userId,
      action: 'CONFIG_UPDATE',
      resource: 'SystemConfig',
      resourceId: configKey,
      oldValues: { value: oldValue },
      newValues: { value: newValue },
      ipAddress: context.ipAddress,
      userAgent: context.userAgent
    })
  }

  /**
   * Log bulk operations
   */
  static async logBulkOperation(
    context: AuditContext,
    action: string,
    resource: string,
    affectedCount: number,
    metadata?: any
  ): Promise<void> {
    await this.log({
      userId: context.userId,
      action: `BULK_${action}`,
      resource,
      newValues: {
        affectedCount,
        ...metadata
      },
      ipAddress: context.ipAddress,
      userAgent: context.userAgent
    })
  }

  /**
   * Log security events
   */
  static async logSecurityEvent(
    context: AuditContext,
    event: 'UNAUTHORIZED_ACCESS' | 'PERMISSION_DENIED' | 'SUSPICIOUS_ACTIVITY',
    resource: string,
    details?: any
  ): Promise<void> {
    await this.log({
      userId: context.userId,
      action: `SECURITY_${event}`,
      resource,
      newValues: {
        event,
        details,
        timestamp: new Date()
      },
      ipAddress: context.ipAddress,
      userAgent: context.userAgent
    })
  }
}

/**
 * Convenience function for quick audit logging
 */
export async function auditLog(data: AuditLogData): Promise<void> {
  await AuditLogger.log(data)
}

/**
 * Middleware helper for automatic audit logging
 */
export function withAuditLogging<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  getAuditData: (...args: T) => AuditLogData
) {
  return async (...args: T): Promise<R> => {
    const result = await fn(...args)
    
    try {
      const auditData = getAuditData(...args)
      await AuditLogger.log(auditData)
    } catch (error) {
      console.error('Audit logging failed:', error)
    }
    
    return result
  }
}
