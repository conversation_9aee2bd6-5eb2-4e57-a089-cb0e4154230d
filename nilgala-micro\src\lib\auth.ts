import { NextAuthOptions } from 'next-auth'
import Cred<PERSON><PERSON><PERSON>rovider from 'next-auth/providers/credentials'
import { PrismaAdapter } from '@auth/prisma-adapter'
import { prisma } from './prisma'
import bcrypt from 'bcryptjs'
import { UserRole } from '@prisma/client'

// Cache for role permissions to avoid database calls on every request
let rolePermissionsCache: Record<string, string[]> = {}
let cacheLastUpdated = 0
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma) as any,
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email
          }
        })

        if (!user || !user.isActive) {
          return null
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password
        )

        if (!isPasswordValid) {
          return null
        }

        // Update last login
        await prisma.user.update({
          where: { id: user.id },
          data: { lastLogin: new Date() }
        })

        return {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          avatar: user.avatar || undefined,
        }
      }
    })
  ],
  session: {
    strategy: 'jwt'
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role
        token.firstName = user.firstName
        token.lastName = user.lastName
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!
        session.user.role = token.role as UserRole
        session.user.firstName = token.firstName as string
        session.user.lastName = token.lastName as string
      }
      return session
    }
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  }
}

// Load role permissions from database with caching
async function loadRolePermissions(): Promise<Record<string, string[]>> {
  const now = Date.now()

  // Return cached permissions if still valid
  if (cacheLastUpdated && (now - cacheLastUpdated) < CACHE_DURATION) {
    return rolePermissionsCache
  }

  try {
    // Fetch all active role permissions from database
    const permissions = await prisma.rolePermission.findMany({
      where: { isActive: true },
      select: { role: true, permission: true }
    })

    // Group permissions by role
    const rolePermissions: Record<string, string[]> = {}
    for (const perm of permissions) {
      if (!rolePermissions[perm.role]) {
        rolePermissions[perm.role] = []
      }
      rolePermissions[perm.role].push(perm.permission)
    }

    // Update cache
    rolePermissionsCache = rolePermissions
    cacheLastUpdated = now

    return rolePermissions
  } catch (error) {
    console.error('Error loading role permissions from database:', error)
    // Return empty permissions on error to be safe
    return {}
  }
}

// Get role permissions (cached)
export async function getRolePermissions(): Promise<Record<string, string[]>> {
  return await loadRolePermissions()
}

// Clear permissions cache (call this when permissions are updated)
export function clearPermissionsCache() {
  rolePermissionsCache = {}
  cacheLastUpdated = 0
}

// Initialize permissions cache on server startup
export async function initializePermissionsCache() {
  try {
    await loadRolePermissions()
    console.log('✅ Permissions cache initialized')
  } catch (error) {
    console.error('❌ Failed to initialize permissions cache:', error)
  }
}

export async function hasPermission(userRole: UserRole, permission: string): Promise<boolean> {
  const rolePermissions = await getRolePermissions()
  return rolePermissions[userRole]?.includes(permission) || false
}

export async function checkPermissions(userRole: UserRole, requiredPermissions: string[]): Promise<boolean> {
  const rolePermissions = await getRolePermissions()
  return requiredPermissions.every(permission => rolePermissions[userRole]?.includes(permission) || false)
}

// Synchronous version for backward compatibility (uses cache)
export function hasPermissionSync(userRole: UserRole, permission: string): boolean {
  return rolePermissionsCache[userRole]?.includes(permission) || false
}

export function checkPermissionsSync(userRole: UserRole, requiredPermissions: string[]): boolean {
  return requiredPermissions.every(permission => hasPermissionSync(userRole, permission))
}
