import { prisma } from '@/lib/prisma'
import { CustomerStatus } from '@prisma/client'

/**
 * Updates customer status based on their loan activity
 * Business Rules:
 * - New customers start as INACTIVE
 * - Customers with active loans (ACTIVE, DISBURSED) become ACTIVE
 * - Customers without active loans become INACTIVE (unless manually set to SUSPENDED/BLACKLISTED)
 * - SUSPENDED and BLACKLISTED statuses can only be changed manually by authorized roles
 */
export async function updateCustomerStatus(customerId: string, updatedBy?: string): Promise<CustomerStatus> {
  try {
    // Get customer with their loans
    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
      include: {
        loans: {
          where: {
            status: {
              in: ['ACTIVE', 'DISBURSED']
            }
          }
        }
      }
    })

    if (!customer) {
      throw new Error('Customer not found')
    }

    // Don't auto-update if customer is manually suspended or blacklisted
    if (customer.status === 'SUSPENDED' || customer.status === 'BLACKLISTED') {
      return customer.status
    }

    // Determine new status based on active loans
    const newStatus: CustomerStatus = customer.loans.length > 0 ? 'ACTIVE' : 'INACTIVE'

    // Only update if status has changed
    if (customer.status !== newStatus) {
      await prisma.customer.update({
        where: { id: customerId },
        data: {
          status: newStatus,
          statusUpdatedBy: updatedBy || null,
          statusUpdatedAt: new Date(),
          statusNotes: `Auto-updated based on loan activity`
        }
      })

      // Create audit log for automatic status updates
      if (updatedBy) {
        await prisma.auditLog.create({
          data: {
            action: 'AUTO_UPDATE_STATUS',
            resource: 'Customer',
            resourceId: customerId,
            userId: updatedBy,
            oldValues: {
              status: customer.status
            },
            newValues: {
              status: newStatus,
              reason: 'Automatic update based on loan activity'
            }
          }
        })
      }
    }

    return newStatus
  } catch (error) {
    console.error('Error updating customer status:', error)
    throw error
  }
}

/**
 * Get customer status with additional context
 */
export async function getCustomerStatusInfo(customerId: string) {
  const customer = await prisma.customer.findUnique({
    where: { id: customerId },
    include: {
      loans: {
        where: {
          status: {
            in: ['ACTIVE', 'DISBURSED']
          }
        },
        select: {
          id: true,
          loanNumber: true,
          status: true,
          principalAmount: true,
          disbursedAmount: true
        }
      }
    }
  })

  if (!customer) {
    return null
  }

  const statusInfo = {
    currentStatus: customer.status,
    activeLoansCount: customer.loans.length,
    totalActiveAmount: customer.loans.reduce((sum, loan) => 
      sum + Number(loan.disbursedAmount || loan.principalAmount), 0
    ),
    canBeSetToActive: customer.loans.length > 0,
    lastStatusUpdate: customer.statusUpdatedAt,
    statusNotes: customer.statusNotes
  }

  return {
    customer,
    statusInfo
  }
}

/**
 * Validate if a status change is allowed
 */
export function validateStatusChange(
  currentStatus: CustomerStatus,
  newStatus: CustomerStatus,
  userRole: string,
  hasActiveLoans: boolean
): { isValid: boolean; reason?: string } {
  // Only certain roles can set SUSPENDED or BLACKLISTED
  if ((newStatus === 'SUSPENDED' || newStatus === 'BLACKLISTED') && 
      !['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER'].includes(userRole)) {
    return {
      isValid: false,
      reason: 'Insufficient permissions to suspend or blacklist customers'
    }
  }

  // Cannot set to ACTIVE without active loans
  if (newStatus === 'ACTIVE' && !hasActiveLoans) {
    return {
      isValid: false,
      reason: 'Cannot set customer to ACTIVE status without active loans'
    }
  }

  // All other changes are valid
  return { isValid: true }
}

/**
 * Get status badge color for UI
 */
export function getStatusBadgeColor(status: CustomerStatus): string {
  switch (status) {
    case 'ACTIVE':
      return 'bg-green-100 text-green-800'
    case 'INACTIVE':
      return 'bg-gray-100 text-gray-800'
    case 'SUSPENDED':
      return 'bg-yellow-100 text-yellow-800'
    case 'BLACKLISTED':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

/**
 * Get status display label
 */
export function getStatusLabel(status: CustomerStatus): string {
  switch (status) {
    case 'ACTIVE':
      return 'Active'
    case 'INACTIVE':
      return 'Inactive'
    case 'SUSPENDED':
      return 'Suspended'
    case 'BLACKLISTED':
      return 'Blacklisted'
    default:
      return 'Unknown'
  }
}
