/**
 * Interest calculation utilities for different loan calculation methods
 */

export interface LoanCalculationInput {
  principalAmount: number
  interestRate: number // Monthly percentage rate for MONTHLY_INTEREST method, Annual for COMPOUND_INTEREST
  tenureInDays: number
  collectionType: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'YEARLY'
  interestCalculationMethod: 'MONTHLY_INTEREST' | 'COMPOUND_INTEREST'
}

export interface LoanCalculationResult {
  totalInterest: number
  totalAmount: number
  emiAmount: number
  numberOfPayments: number
}

/**
 * Method 1: Monthly Interest Calculation
 * Formula: Principal × (Monthly Rate/100) × (Tenure in Months)
 * Example: 100,000 × 5% × 2 months = 10,000 interest
 * Note: interestRate is treated as MONTHLY rate for this method
 */
function calculateMonthlyInterest(input: LoanCalculationInput): LoanCalculationResult {
  const { principalAmount, interestRate, tenureInDays, collectionType } = input

  // Convert tenure to months (30 days = 1 month)
  const tenureInMonths = tenureInDays / 30

  // Calculate total interest: Principal × Monthly Rate × Months
  // interestRate is already monthly rate for this method
  const totalInterest = principalAmount * (interestRate / 100) * tenureInMonths
  
  // Total amount to be repaid
  const totalAmount = principalAmount + totalInterest
  
  // Calculate number of payments based on collection type
  let numberOfPayments: number
  switch (collectionType) {
    case 'DAILY':
      numberOfPayments = tenureInDays
      break
    case 'WEEKLY':
      numberOfPayments = Math.ceil(tenureInDays / 7)
      break
    case 'MONTHLY':
      numberOfPayments = Math.ceil(tenureInMonths)
      break
    case 'QUARTERLY':
      numberOfPayments = Math.ceil(tenureInMonths / 3)
      break
    case 'YEARLY':
      numberOfPayments = Math.ceil(tenureInMonths / 12)
      break
    default:
      numberOfPayments = tenureInDays
  }
  
  // EMI = Total Amount / Number of Payments
  const emiAmount = totalAmount / numberOfPayments
  
  return {
    totalInterest,
    totalAmount,
    emiAmount,
    numberOfPayments
  }
}

/**
 * Method 2: Compound Interest EMI Calculation
 * Formula: EMI = P × r × (1+r)^n / ((1+r)^n - 1)
 * This is the standard loan EMI calculation with compound interest
 * Note: interestRate is treated as ANNUAL rate for this method
 */
function calculateCompoundInterest(input: LoanCalculationInput): LoanCalculationResult {
  const { principalAmount, interestRate, tenureInDays, collectionType } = input

  // Determine periods based on collection type
  let periodsPerYear: number
  let totalPeriods: number

  switch (collectionType) {
    case 'DAILY':
      periodsPerYear = 365
      totalPeriods = tenureInDays
      break
    case 'WEEKLY':
      periodsPerYear = 52
      totalPeriods = Math.ceil(tenureInDays / 7)
      break
    case 'MONTHLY':
      periodsPerYear = 12
      totalPeriods = Math.ceil(tenureInDays / 30)
      break
    case 'QUARTERLY':
      periodsPerYear = 4
      totalPeriods = Math.ceil(tenureInDays / 90)
      break
    case 'YEARLY':
      periodsPerYear = 1
      totalPeriods = Math.ceil(tenureInDays / 365)
      break
    default:
      periodsPerYear = 12
      totalPeriods = Math.ceil(tenureInDays / 30)
  }

  // Calculate period interest rate
  const periodRate = interestRate / 100 / periodsPerYear

  // Calculate EMI using compound interest formula
  let emiAmount: number
  if (periodRate === 0) {
    // If no interest, just divide principal by periods
    emiAmount = principalAmount / totalPeriods
  } else {
    // Standard EMI formula: EMI = P × r × (1+r)^n / ((1+r)^n - 1)
    emiAmount = (principalAmount * periodRate * Math.pow(1 + periodRate, totalPeriods)) /
                (Math.pow(1 + periodRate, totalPeriods) - 1)
  }

  // Calculate total amount and interest
  const totalAmount = emiAmount * totalPeriods
  const totalInterest = totalAmount - principalAmount

  return {
    totalInterest,
    totalAmount,
    emiAmount,
    numberOfPayments: totalPeriods
  }
}

/**
 * Main calculation function that routes to appropriate method
 */
export function calculateLoanInterest(input: LoanCalculationInput): LoanCalculationResult {
  switch (input.interestCalculationMethod) {
    case 'MONTHLY_INTEREST':
      return calculateMonthlyInterest(input)
    case 'COMPOUND_INTEREST':
      return calculateCompoundInterest(input)
    default:
      throw new Error(`Unsupported interest calculation method: ${input.interestCalculationMethod}`)
  }
}

/**
 * Helper function to convert tenure to days based on unit
 */
export function convertTenureToDays(tenure: number, unit: string): number {
  switch (unit) {
    case 'DAYS': return tenure
    case 'WEEKS': return tenure * 7
    case 'MONTHS': return tenure * 30
    case 'YEARS': return tenure * 365
    default: return tenure
  }
}

/**
 * Helper function to get EMI frequency label
 */
export function getEMIFrequencyLabel(collectionType: string): string {
  switch (collectionType) {
    case 'DAILY': return 'Daily Payment'
    case 'WEEKLY': return 'Weekly EMI'
    case 'MONTHLY': return 'Monthly EMI'
    case 'QUARTERLY': return 'Quarterly EMI'
    case 'YEARLY': return 'Yearly EMI'
    default: return 'Payment'
  }
}
