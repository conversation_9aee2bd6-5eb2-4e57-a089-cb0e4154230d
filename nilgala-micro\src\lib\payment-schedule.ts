/**
 * Unified payment schedule generation that uses the correct interest calculation method
 */

import { calculateLoanInterest, convertTenureToDays } from './interest-calculations'

export interface LoanData {
  id: string
  principalAmount: number
  interestRate: number
  tenure: number
  tenureUnit: string
  repaymentFrequency: string
  interestCalculationMethod: 'MONTHLY_INTEREST' | 'COMPOUND_INTEREST'
  applicationDate?: Date
  disbursementDate?: Date
}

export interface PaymentScheduleItem {
  installmentNumber: number
  dueDate: Date
  principalAmount: number
  interestAmount: number
  totalAmount: number
  paidAmount: number
  status: 'PENDING' | 'PAID' | 'PARTIAL' | 'OVERDUE'
}

/**
 * Generate payment schedule using the loan's specified interest calculation method
 */
export function generatePaymentSchedule(loan: LoanData): PaymentScheduleItem[] {
  const principal = Number(loan.principalAmount)
  const interestRate = Number(loan.interestRate)
  const tenure = Number(loan.tenure)

  // Smart tenure conversion: For daily loans, if tenure unit is MONTHS but the value is small (≤ 90),
  // treat it as days to avoid creating excessive payment schedules
  let tenureInDays: number
  let actualTenureUnit = loan.tenureUnit

  if (loan.repaymentFrequency === 'DAILY' && loan.tenureUnit === 'MONTHS' && tenure <= 90) {
    // For daily loans with small "month" values, treat as days
    console.warn(`⚠️  Daily loan with ${tenure} MONTHS tenure detected. Converting to ${tenure} DAYS for realistic payment schedule.`)
    tenureInDays = tenure
    actualTenureUnit = 'DAYS'
  } else {
    // Normal conversion
    tenureInDays = convertTenureToDays(tenure, loan.tenureUnit)
  }

  console.log('Payment schedule generation:', {
    originalTenure: tenure,
    originalUnit: loan.tenureUnit,
    actualUnit: actualTenureUnit,
    tenureInDays,
    repaymentFrequency: loan.repaymentFrequency
  })

  // Map repayment frequency to collection type
  const collectionType = mapRepaymentFrequencyToCollectionType(loan.repaymentFrequency)

  // Calculate loan details using the specified method
  const calculation = calculateLoanInterest({
    principalAmount: principal,
    interestRate: interestRate,
    tenureInDays: tenureInDays,
    collectionType: collectionType,
    interestCalculationMethod: loan.interestCalculationMethod
  })

  console.log('Loan calculation result:', {
    numberOfPayments: calculation.numberOfPayments,
    totalAmount: calculation.totalAmount,
    emiAmount: calculation.emiAmount
  })

  // Generate schedule based on the calculation method
  if (loan.interestCalculationMethod === 'MONTHLY_INTEREST') {
    return generateMonthlyInterestSchedule(loan, calculation)
  } else {
    return generateCompoundInterestSchedule(loan, calculation)
  }
}

/**
 * Generate schedule for Monthly Interest method (Simple Interest)
 * Each payment has equal installment amount with varying principal/interest split
 */
function generateMonthlyInterestSchedule(loan: LoanData, calculation: any): PaymentScheduleItem[] {
  const schedule: PaymentScheduleItem[] = []
  const startDate = new Date(loan.disbursementDate || loan.applicationDate || new Date())
  
  // For monthly interest, each payment is the same amount
  const installmentAmount = calculation.emiAmount
  const totalInterest = calculation.totalInterest
  const numberOfPayments = calculation.numberOfPayments
  
  // Distribute interest evenly across payments (simple interest approach)
  const interestPerPayment = totalInterest / numberOfPayments
  const principalPerPayment = (Number(loan.principalAmount) / numberOfPayments)
  
  for (let i = 1; i <= numberOfPayments; i++) {
    const dueDate = getNextDueDate(startDate, loan.repaymentFrequency, i)
    
    schedule.push({
      installmentNumber: i,
      dueDate,
      principalAmount: Math.round(principalPerPayment * 100) / 100,
      interestAmount: Math.round(interestPerPayment * 100) / 100,
      totalAmount: Math.round(installmentAmount * 100) / 100,
      paidAmount: 0,
      status: 'PENDING'
    })
  }
  
  return schedule
}

/**
 * Generate schedule for Compound Interest method (EMI)
 * Principal and interest amounts vary each payment, but total EMI remains constant
 */
function generateCompoundInterestSchedule(loan: LoanData, calculation: any): PaymentScheduleItem[] {
  const schedule: PaymentScheduleItem[] = []
  const principal = Number(loan.principalAmount)
  const annualRate = Number(loan.interestRate) / 100
  const startDate = new Date(loan.disbursementDate || loan.applicationDate || new Date())
  
  // Determine period rate based on frequency
  let periodsPerYear: number
  switch (loan.repaymentFrequency) {
    case 'DAILY': periodsPerYear = 365; break
    case 'WEEKLY': periodsPerYear = 52; break
    case 'MONTHLY': periodsPerYear = 12; break
    case 'QUARTERLY': periodsPerYear = 4; break
    case 'YEARLY': periodsPerYear = 1; break
    default: periodsPerYear = 12
  }
  
  const periodRate = annualRate / periodsPerYear
  const emiAmount = calculation.emiAmount
  const numberOfPayments = calculation.numberOfPayments
  
  let remainingPrincipal = principal
  
  for (let i = 1; i <= numberOfPayments; i++) {
    const dueDate = getNextDueDate(startDate, loan.repaymentFrequency, i)
    
    // Calculate interest for this period
    const interestAmount = remainingPrincipal * periodRate
    
    // Calculate principal payment (EMI - Interest)
    const principalAmount = Math.min(emiAmount - interestAmount, remainingPrincipal)
    
    // Update remaining principal
    remainingPrincipal -= principalAmount
    
    schedule.push({
      installmentNumber: i,
      dueDate,
      principalAmount: Math.round(principalAmount * 100) / 100,
      interestAmount: Math.round(interestAmount * 100) / 100,
      totalAmount: Math.round(emiAmount * 100) / 100,
      paidAmount: 0,
      status: 'PENDING'
    })
    
    // Break if principal is fully paid
    if (remainingPrincipal <= 0.01) break
  }
  
  return schedule
}

/**
 * Calculate next due date based on frequency
 */
function getNextDueDate(startDate: Date, frequency: string, installmentNumber: number): Date {
  const date = new Date(startDate)
  
  switch (frequency) {
    case 'DAILY':
      // For daily payments, add the installment number as calendar days
      // This ensures that a 90-day loan has exactly 90 daily payments
      date.setDate(date.getDate() + installmentNumber)
      return date
      
    case 'WEEKLY':
      const weeklyDate = new Date(date)
      weeklyDate.setDate(weeklyDate.getDate() + (installmentNumber * 7))
      return weeklyDate
      
    case 'MONTHLY':
      const monthlyDate = new Date(date)
      monthlyDate.setMonth(monthlyDate.getMonth() + installmentNumber)
      return monthlyDate
      
    case 'QUARTERLY':
      const quarterlyDate = new Date(date)
      quarterlyDate.setMonth(quarterlyDate.getMonth() + (installmentNumber * 3))
      return quarterlyDate
      
    case 'YEARLY':
      const yearlyDate = new Date(date)
      yearlyDate.setFullYear(yearlyDate.getFullYear() + installmentNumber)
      return yearlyDate
      
    default:
      const defaultDate = new Date(date)
      defaultDate.setMonth(defaultDate.getMonth() + installmentNumber)
      return defaultDate
  }
}

/**
 * Map repayment frequency to collection type for interest calculations
 */
function mapRepaymentFrequencyToCollectionType(frequency: string): 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'YEARLY' {
  switch (frequency) {
    case 'DAILY': return 'DAILY'
    case 'WEEKLY': return 'WEEKLY'
    case 'MONTHLY': return 'MONTHLY'
    case 'QUARTERLY': return 'QUARTERLY'
    case 'YEARLY': return 'YEARLY'
    default: return 'MONTHLY'
  }
}
