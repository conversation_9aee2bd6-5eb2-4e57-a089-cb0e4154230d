import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'

// Initialize R2 client
export const r2Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY!,
  },
})

export const BUCKET_NAME = process.env.CLOUDFLARE_R2_BUCKET_NAME!

// Upload a file to R2
export async function uploadFileToR2(
  file: Buffer,
  key: string,
  contentType: string
): Promise<string> {
  const command = new PutObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
    Body: file,
    ContentType: contentType,
  })

  await r2Client.send(command)
  return key
}

// Delete a file from R2
export async function deleteFileFromR2(key: string): Promise<void> {
  const command = new DeleteObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
  })

  await r2Client.send(command)
}

// Get a file from R2
export async function getFileFromR2(key: string): Promise<Buffer> {
  const command = new GetObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
  })

  const response = await r2Client.send(command)

  if (!response.Body) {
    throw new Error('File not found')
  }

  // Convert stream to buffer
  const chunks: Uint8Array[] = []
  const reader = response.Body.transformToWebStream().getReader()

  while (true) {
    const { done, value } = await reader.read()
    if (done) break
    chunks.push(value)
  }

  return Buffer.concat(chunks)
}

// Generate a presigned URL for file access
export async function getPresignedUrl(key: string, expiresIn: number = 3600): Promise<string> {
  const command = new GetObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
  })

  return await getSignedUrl(r2Client, command, { expiresIn })
}

// Generate a unique file key
export function generateFileKey(
  type: 'loan' | 'customer',
  entityId: string,
  documentName: string,
  originalFileName: string
): string {
  const timestamp = Date.now()
  const extension = originalFileName.split('.').pop()
  const sanitizedName = documentName.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase()

  return `${type}s/${entityId}/${sanitizedName}/${timestamp}.${extension}`
}

// Generate a unique file key for guarantor documents
export function generateGuarantorFileKey(originalFileName: string, guarantorId?: string): string {
  if (guarantorId) {
    // New format: guarantor-documents/{guarantorId}/{originalFileName}
    return `guarantor-documents/${guarantorId}/${originalFileName}`
  } else {
    // Legacy format for backward compatibility
    const timestamp = Date.now()
    const extension = originalFileName.split('.').pop()
    const randomId = Math.random().toString(36).substring(2, 15)
    return `guarantor-documents/${timestamp}_${randomId}.${extension}`
  }
}
