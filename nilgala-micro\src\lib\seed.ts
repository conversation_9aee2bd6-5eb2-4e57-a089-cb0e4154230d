import { prisma } from './prisma'
import bcrypt from 'bcryptjs'
import { UserRole } from '@prisma/client'

export async function createDefaultAdmin() {
  try {
    // Check if admin user already exists
    const existingAdmin = await prisma.user.findFirst({
      where: { role: UserRole.SUPER_ADMIN }
    })

    if (existingAdmin) {
      console.log('Admin user already exists')
      return existingAdmin
    }

    // Create default admin user
    const hashedPassword = await bcrypt.hash('admin123', 12)
    
    const adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'System',
        lastName: 'Administrator',
        role: UserRole.SUPER_ADMIN,
        isActive: true,
      }
    })

    console.log('Default admin user created:', adminUser.email)
    return adminUser
  } catch (error) {
    console.error('Error creating default admin:', error)
    throw error
  }
}

export async function seedSystemConfig() {
  try {
    const defaultConfigs = [
      {
        key: 'LOAN_MIN_AMOUNT',
        value: '1000',
        description: 'Minimum loan amount allowed',
        category: 'LOAN_SETTINGS'
      },
      {
        key: 'LOAN_MAX_AMOUNT',
        value: '1000000',
        description: 'Maximum loan amount allowed',
        category: 'LOAN_SETTINGS'
      },
      {
        key: 'DEFAULT_INTEREST_RATE',
        value: '15',
        description: 'Default annual interest rate percentage',
        category: 'LOAN_SETTINGS'
      },
      {
        key: 'MAX_LOAN_TENURE',
        value: '120',
        description: 'Maximum loan tenure in months',
        category: 'LOAN_SETTINGS'
      },
      {
        key: 'PENALTY_RATE',
        value: '2',
        description: 'Penalty rate percentage for overdue payments',
        category: 'PAYMENT_SETTINGS'
      },
      {
        key: 'GRACE_PERIOD_DAYS',
        value: '7',
        description: 'Grace period in days before penalty applies',
        category: 'PAYMENT_SETTINGS'
      },
      {
        key: 'COMPANY_NAME',
        value: 'Nilgala Micro Finance',
        description: 'Company name for documents and communications',
        category: 'SYSTEM_SETTINGS'
      },
      {
        key: 'COMPANY_ADDRESS',
        value: 'Nilgala, Sri Lanka',
        description: 'Company address',
        category: 'SYSTEM_SETTINGS'
      }
    ]

    for (const config of defaultConfigs) {
      await prisma.systemConfig.upsert({
        where: { key: config.key },
        update: {},
        create: config
      })
    }

    console.log('System configuration seeded successfully')
  } catch (error) {
    console.error('Error seeding system config:', error)
    throw error
  }
}

async function seedLoanTypes() {
  try {
    const defaultLoanTypes = [
      {
        name: 'Personal Loan',
        description: 'General purpose personal loans for individual needs',
        minAmount: 10000,
        maxAmount: 500000,
        interestRate: 18.0,
        termMonths: 12,
        processingFee: 1000,
        collateralRequired: false,
        guarantorRequired: true,
        eligibilityCriteria: 'Minimum monthly income of LKR 25,000',
        requiredDocuments: ['NIC Copy', 'Income Certificate', 'Bank Statements'],
        isActive: true,
      },
      {
        name: 'Business Loan',
        description: 'Loans for small business development and expansion',
        minAmount: 50000,
        maxAmount: 2000000,
        interestRate: 16.0,
        termMonths: 24,
        processingFee: 2500,
        collateralRequired: true,
        guarantorRequired: true,
        eligibilityCriteria: 'Valid business registration and minimum 1 year operation',
        requiredDocuments: ['Business Registration', 'Financial Statements', 'Tax Returns'],
        isActive: true,
      },
      {
        name: 'Emergency Loan',
        description: 'Quick loans for emergency situations',
        minAmount: 5000,
        maxAmount: 100000,
        interestRate: 22.0,
        termMonths: 6,
        processingFee: 500,
        collateralRequired: false,
        guarantorRequired: false,
        eligibilityCriteria: 'Existing customer with good payment history',
        requiredDocuments: ['NIC Copy', 'Emergency Declaration'],
        isActive: true,
      },
      {
        name: 'Agricultural Loan',
        description: 'Specialized loans for farming and agricultural activities',
        minAmount: 25000,
        maxAmount: 1000000,
        interestRate: 14.0,
        termMonths: 18,
        processingFee: 1500,
        collateralRequired: true,
        guarantorRequired: true,
        eligibilityCriteria: 'Land ownership or lease agreement',
        requiredDocuments: ['Land Documents', 'Cultivation Plan', 'Income Proof'],
        isActive: true,
      }
    ]

    // Get the admin user ID for createdBy field
    const adminUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    })

    if (!adminUser) {
      throw new Error('Admin user not found. Please run admin seeding first.')
    }

    for (const loanType of defaultLoanTypes) {
      await prisma.loanType.upsert({
        where: { name: loanType.name },
        update: {},
        create: {
          ...loanType,
          createdBy: adminUser.id,
        }
      })
    }

    console.log('Loan types seeded successfully')
  } catch (error) {
    console.error('Error seeding loan types:', error)
    throw error
  }
}

export async function runSeed() {
  try {
    await createDefaultAdmin()
    await seedSystemConfig()
    await seedLoanTypes()
    console.log('Database seeding completed successfully')
  } catch (error) {
    console.error('Database seeding failed:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the seed if this file is executed directly
if (require.main === module) {
  runSeed()
}
