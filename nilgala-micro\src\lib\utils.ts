import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format currency amount with proper formatting
 * Removes leading zeros and ensures 2 decimal places
 * Handles Prisma Decimal objects properly
 */
export function formatCurrency(amount: number | string | null | undefined | any, currency: string = 'LKR'): string {
  if (amount === null || amount === undefined || amount === '') {
    return `${currency} 0.00`
  }

  let numAmount: number

  // Handle Prisma Decimal objects
  if (amount && typeof amount === 'object' && typeof amount.toNumber === 'function') {
    numAmount = amount.toNumber()
  } else if (typeof amount === 'string') {
    numAmount = parseFloat(amount)
  } else if (typeof amount === 'number') {
    numAmount = amount
  } else {
    // Try to convert to number as fallback
    numAmount = Number(amount)
  }

  if (isNaN(numAmount)) {
    return `${currency} 0.00`
  }

  // Format with 2 decimal places and proper thousand separators
  const formatted = numAmount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })

  return `${currency} ${formatted}`
}

/**
 * Format number without currency symbol
 * Handles Prisma Decimal objects properly
 */
export function formatNumber(amount: number | string | null | undefined | any): string {
  if (amount === null || amount === undefined || amount === '') {
    return '0.00'
  }

  let numAmount: number

  // Handle Prisma Decimal objects
  if (amount && typeof amount === 'object' && typeof amount.toNumber === 'function') {
    numAmount = amount.toNumber()
  } else if (typeof amount === 'string') {
    numAmount = parseFloat(amount)
  } else if (typeof amount === 'number') {
    numAmount = amount
  } else {
    // Try to convert to number as fallback
    numAmount = Number(amount)
  }

  if (isNaN(numAmount)) {
    return '0.00'
  }

  return numAmount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}
