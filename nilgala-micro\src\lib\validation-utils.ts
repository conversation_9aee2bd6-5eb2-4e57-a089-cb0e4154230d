import { z } from 'zod'
import { NextResponse } from 'next/server'

/**
 * Creates a user-friendly error response from Zod validation errors
 */
export function createValidationErrorResponse(error: z.ZodError): NextResponse {
  // Create user-friendly error messages from Zod errors
  const errorMessages = error.errors.map(err => {
    const field = err.path.join('.')
    const fieldName = field.charAt(0).toUpperCase() + field.slice(1).replace(/([A-Z])/g, ' $1')
    return `${fieldName}: ${err.message}`
  }).join(', ')
  
  return NextResponse.json(
    { error: `Please fix the following errors: ${errorMessages}` },
    { status: 400 }
  )
}

/**
 * Handles API errors consistently
 */
export function handleApiError(error: unknown, context: string): NextResponse {
  if (error instanceof z.ZodError) {
    return createValidationErrorResponse(error)
  }

  console.error(`Error in ${context}:`, error)
  return NextResponse.json(
    { error: 'Internal server error' },
    { status: 500 }
  )
}
