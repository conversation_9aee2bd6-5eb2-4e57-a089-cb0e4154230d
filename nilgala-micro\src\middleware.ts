import { withAuth } from 'next-auth/middleware'
import { NextResponse } from 'next/server'
import { UserRole } from '@prisma/client'

export default withAuth(
  function middleware(req) {
    const token = req.nextauth.token
    const { pathname } = req.nextUrl

    // If user is authenticated and trying to access auth pages, redirect to dashboard
    if (token && ['/auth/signin', '/auth/signup'].includes(pathname)) {
      return NextResponse.redirect(new URL('/dashboard', req.url))
    }

    // Public routes that don't require authentication
    const publicRoutes = ['/auth/signin', '/auth/signup', '/auth/error']
    if (publicRoutes.includes(pathname)) {
      return NextResponse.next()
    }

    // Allow root path to handle its own redirect logic
    if (pathname === '/') {
      return NextResponse.next()
    }

    // Check if user is authenticated for protected routes
    if (!token) {
      return NextResponse.redirect(new URL('/auth/signin', req.url))
    }

    // Role-based route protection
    const userRole = token.role as UserRole

    // Super Admin can access everything
    if (userRole === UserRole.SUPER_ADMIN) {
      return NextResponse.next()
    }

    // Admin routes - only for Super Admin and Higher Management
    if (pathname.startsWith('/admin')) {
      if (![UserRole.SUPER_ADMIN, UserRole.HIGHER_MANAGEMENT].includes(userRole)) {
        return NextResponse.redirect(new URL('/unauthorized', req.url))
      }
    }

    // Manager routes
    if (pathname.startsWith('/manager')) {
      if (![UserRole.SUPER_ADMIN, UserRole.HIGHER_MANAGEMENT, UserRole.MANAGER].includes(userRole)) {
        return NextResponse.redirect(new URL('/unauthorized', req.url))
      }
    }

    // API route protection
    if (pathname.startsWith('/api/')) {
      // Add specific API route protections here
      return NextResponse.next()
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // Allow access to public routes and root path
        const { pathname } = req.nextUrl
        const publicRoutes = ['/auth/signin', '/auth/signup', '/auth/error', '/']
        if (publicRoutes.includes(pathname)) {
          return true
        }
        // Require token for all other routes
        return !!token
      }
    }
  }
)

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (NextAuth routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)'
  ]
}
