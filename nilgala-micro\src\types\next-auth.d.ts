import { User<PERSON><PERSON> } from '@prisma/client'
import <PERSON><PERSON><PERSON> from 'next-auth'

declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      email: string
      firstName: string
      lastName: string
      role: UserRole
      avatar?: string
    }
  }

  interface User {
    id: string
    email: string
    firstName: string
    lastName: string
    role: UserRole
    avatar?: string
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role: UserRole
    firstName: string
    lastName: string
  }
}
