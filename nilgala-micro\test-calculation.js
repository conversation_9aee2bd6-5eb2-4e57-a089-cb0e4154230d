// Test the interest calculation logic
function convertTenureToDays(tenure, unit) {
  switch (unit) {
    case 'DAYS': return tenure
    case 'WEEKS': return tenure * 7
    case 'MONTHS': return tenure * 30
    case 'YEARS': return tenure * 365
    default: return tenure
  }
}

function calculateMonthlyInterest(input) {
  const { principalAmount, interestRate, tenureInDays, collectionType } = input

  // Convert tenure to months (30 days = 1 month)
  const tenureInMonths = tenureInDays / 30

  // Calculate total interest: Principal × Monthly Rate × Months
  const totalInterest = principalAmount * (interestRate / 100) * tenureInMonths
  
  // Total amount to be repaid
  const totalAmount = principalAmount + totalInterest
  
  // Calculate number of payments based on collection type
  let numberOfPayments
  switch (collectionType) {
    case 'DAILY':
      numberOfPayments = tenureInDays
      break
    case 'WEEKLY':
      numberOfPayments = Math.ceil(tenureInDays / 7)
      break
    case 'MONTHLY':
      numberOfPayments = Math.ceil(tenureInMonths)
      break
    default:
      numberOfPayments = tenureInDays
  }
  
  // EMI = Total Amount / Number of Payments
  const emiAmount = totalAmount / numberOfPayments
  
  return {
    totalInterest,
    totalAmount,
    emiAmount,
    numberOfPayments,
    tenureInDays,
    tenureInMonths
  }
}

// Test Case 1: 30 days loan (if tenure unit is DAYS)
console.log('=== Test Case 1: 30 DAYS loan ===')
const test1 = calculateMonthlyInterest({
  principalAmount: 50000,
  interestRate: 5,
  tenureInDays: convertTenureToDays(30, 'DAYS'), // 30 days
  collectionType: 'DAILY'
})
console.log('Tenure in days:', test1.tenureInDays)
console.log('Number of payments:', test1.numberOfPayments)
console.log('EMI amount:', test1.emiAmount)
console.log('Total amount:', test1.totalAmount)

// Test Case 2: 30 months loan (if tenure unit is MONTHS)
console.log('\n=== Test Case 2: 30 MONTHS loan ===')
const test2 = calculateMonthlyInterest({
  principalAmount: 50000,
  interestRate: 5,
  tenureInDays: convertTenureToDays(30, 'MONTHS'), // 30 months = 900 days
  collectionType: 'DAILY'
})
console.log('Tenure in days:', test2.tenureInDays)
console.log('Number of payments:', test2.numberOfPayments)
console.log('EMI amount:', test2.emiAmount)
console.log('Total amount:', test2.totalAmount)

// Test Case 3: What should be correct for 30-day loan
console.log('\n=== Test Case 3: Correct 30-day loan ===')
const test3 = calculateMonthlyInterest({
  principalAmount: 50000,
  interestRate: 5,
  tenureInDays: 30, // Directly 30 days
  collectionType: 'DAILY'
})
console.log('Tenure in days:', test3.tenureInDays)
console.log('Number of payments:', test3.numberOfPayments)
console.log('EMI amount:', test3.emiAmount)
console.log('Total amount:', test3.totalAmount)
