// Script to check existing guarantor documents
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  try {
    console.log('=== Checking Guarantor Documents ===')
    
    // Get all guarantor documents
    const documents = await prisma.guarantorDocument.findMany({
      include: {
        guarantor: {
          select: {
            firstName: true,
            lastName: true,
            nationalId: true
          }
        }
      },
      orderBy: {
        uploadedAt: 'desc'
      },
      take: 10
    })
    
    console.log(`Found ${documents.length} guarantor documents:`)
    
    documents.forEach(doc => {
      console.log(`- ${doc.documentName} (${doc.fileName})`)
      console.log(`  Guarantor: ${doc.guarantor.firstName} ${doc.guarantor.lastName}`)
      console.log(`  File Key: ${doc.fileKey}`)
      console.log(`  Size: ${Math.round(doc.fileSize / 1024)} KB`)
      console.log(`  Uploaded: ${doc.uploadedAt.toLocaleDateString()}`)
      console.log(`  View URL: http://localhost:3000/api/guarantor-documents/${doc.id}/view`)
      console.log('')
    })
    
    if (documents.length > 0) {
      console.log('Test the first document by opening:')
      console.log(`http://localhost:3000/api/guarantor-documents/${documents[0].id}/view`)
    }
    
  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()
