/**
 * Comprehensive tests for Database Backup and Restore functionality
 * Tests both API endpoints and actual backup/restore operations
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Test configuration
const TEST_CONFIG = {
  baseUrl: 'http://localhost:3000',
  backupDir: path.join(__dirname, '../backups'),
  testBackupFile: 'test_backup.sql',
  adminCredentials: {
    email: '<EMAIL>',
    password: 'admin123'
  }
};

// Helper function to authenticate and get session token
async function authenticateAdmin() {
  try {
    const response = await fetch(`${TEST_CONFIG.baseUrl}/api/auth/signin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(TEST_CONFIG.adminCredentials)
    });

    if (!response.ok) {
      throw new Error(`Authentication failed: ${response.status}`);
    }

    const cookies = response.headers.get('set-cookie');
    return cookies;
  } catch (error) {
    console.error('Authentication error:', error);
    throw error;
  }
}

// Helper function to make authenticated API requests
async function makeAuthenticatedRequest(url, options = {}) {
  const cookies = await authenticateAdmin();
  
  return fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      'Cookie': cookies
    }
  });
}

// Test 1: API Endpoint Tests
describe('Backup API Endpoints', () => {
  
  test('GET /api/admin/backups - List backups', async () => {
    console.log('Testing backup list endpoint...');
    
    const response = await makeAuthenticatedRequest(`${TEST_CONFIG.baseUrl}/api/admin/backups`);
    
    expect(response.status).toBe(200);
    
    const data = await response.json();
    expect(Array.isArray(data)).toBe(true);
    
    console.log('✅ Backup list endpoint test passed');
  });

  test('POST /api/admin/backups - Create backup', async () => {
    console.log('Testing backup creation endpoint...');
    
    const response = await makeAuthenticatedRequest(`${TEST_CONFIG.baseUrl}/api/admin/backups`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        description: 'Test backup created by automated test',
        backupType: 'MANUAL'
      })
    });

    expect(response.status).toBe(201);
    
    const backup = await response.json();
    expect(backup.id).toBeDefined();
    expect(backup.filename).toBeDefined();
    expect(backup.status).toBe('CREATING');
    expect(backup.description).toBe('Test backup created by automated test');
    
    // Store backup ID for later tests
    global.testBackupId = backup.id;
    
    console.log('✅ Backup creation endpoint test passed');
    console.log(`Created backup with ID: ${backup.id}`);
  });

  test('Wait for backup completion', async () => {
    console.log('Waiting for backup to complete...');
    
    let attempts = 0;
    const maxAttempts = 30; // Wait up to 5 minutes
    
    while (attempts < maxAttempts) {
      const response = await makeAuthenticatedRequest(`${TEST_CONFIG.baseUrl}/api/admin/backups`);
      const backups = await response.json();
      
      const testBackup = backups.find(b => b.id === global.testBackupId);
      
      if (testBackup && testBackup.status === 'COMPLETED') {
        console.log('✅ Backup completed successfully');
        global.testBackup = testBackup;
        return;
      }
      
      if (testBackup && testBackup.status === 'FAILED') {
        throw new Error(`Backup failed: ${testBackup.errorMessage}`);
      }
      
      attempts++;
      await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
    }
    
    throw new Error('Backup did not complete within expected time');
  });

  test('GET /api/admin/backups/[id] - Download backup', async () => {
    console.log('Testing backup download...');
    
    const response = await makeAuthenticatedRequest(
      `${TEST_CONFIG.baseUrl}/api/admin/backups/${global.testBackupId}`
    );

    expect(response.status).toBe(200);
    expect(response.headers.get('content-type')).toBe('application/sql');
    expect(response.headers.get('content-disposition')).toContain('attachment');
    
    const backupContent = await response.text();
    expect(backupContent.length).toBeGreaterThan(0);
    expect(backupContent).toContain('PostgreSQL database dump');
    
    console.log('✅ Backup download test passed');
    console.log(`Downloaded backup size: ${backupContent.length} bytes`);
  });

});

// Test 2: Backup File Validation Tests
describe('Backup File Validation', () => {
  
  test('Backup file exists on disk', () => {
    console.log('Testing backup file existence...');
    
    const backupPath = global.testBackup.filePath;
    expect(fs.existsSync(backupPath)).toBe(true);
    
    const stats = fs.statSync(backupPath);
    expect(stats.size).toBeGreaterThan(0);
    
    console.log('✅ Backup file exists and has content');
    console.log(`Backup file size: ${stats.size} bytes`);
  });

  test('Backup file contains valid SQL', () => {
    console.log('Testing backup file SQL validity...');
    
    const backupPath = global.testBackup.filePath;
    const content = fs.readFileSync(backupPath, 'utf8');
    
    // Check for essential SQL elements
    expect(content).toContain('PostgreSQL database dump');
    expect(content).toContain('CREATE TABLE');
    expect(content).toContain('INSERT INTO');
    expect(content).toContain('users');
    expect(content).toContain('customers');
    expect(content).toContain('loans');
    
    console.log('✅ Backup file contains valid SQL structure');
  });

});

// Test 3: Database Restore Tests (WARNING: This will replace current data)
describe('Database Restore Tests', () => {
  
  test('POST /api/admin/backups/[id]/restore - Restore database', async () => {
    console.log('⚠️  WARNING: Testing database restore - this will replace current data');
    console.log('Proceeding with restore test...');
    
    const response = await makeAuthenticatedRequest(
      `${TEST_CONFIG.baseUrl}/api/admin/backups/${global.testBackupId}/restore`,
      {
        method: 'POST'
      }
    );

    expect(response.status).toBe(200);
    
    const result = await response.json();
    expect(result.message).toContain('restored successfully');
    expect(result.backupInfo).toBeDefined();
    expect(result.backupInfo.filename).toBe(global.testBackup.filename);
    
    console.log('✅ Database restore test passed');
    console.log('Database has been restored from backup');
  });

  test('Verify database integrity after restore', async () => {
    console.log('Testing database integrity after restore...');
    
    // Test basic API endpoints to ensure database is functional
    const response = await makeAuthenticatedRequest(`${TEST_CONFIG.baseUrl}/api/dashboard/stats?role=SUPER_ADMIN`);
    
    expect(response.status).toBe(200);
    
    const stats = await response.json();
    expect(stats.totalUsers).toBeDefined();
    expect(stats.totalCustomers).toBeDefined();
    expect(stats.totalLoans).toBeDefined();
    
    console.log('✅ Database integrity verified after restore');
  });

});

// Test 4: Cleanup Tests
describe('Cleanup Tests', () => {
  
  test('DELETE /api/admin/backups/[id] - Delete test backup', async () => {
    console.log('Testing backup deletion...');
    
    const response = await makeAuthenticatedRequest(
      `${TEST_CONFIG.baseUrl}/api/admin/backups/${global.testBackupId}`,
      {
        method: 'DELETE'
      }
    );

    expect(response.status).toBe(200);
    
    const result = await response.json();
    expect(result.message).toContain('deleted successfully');
    
    console.log('✅ Backup deletion test passed');
  });

  test('Verify backup file removed from disk', () => {
    console.log('Verifying backup file cleanup...');
    
    const backupPath = global.testBackup.filePath;
    expect(fs.existsSync(backupPath)).toBe(false);
    
    console.log('✅ Backup file successfully removed from disk');
  });

});

// Test Runner Function
async function runBackupTests() {
  console.log('🚀 Starting Backup and Restore Tests');
  console.log('=====================================');
  
  try {
    // Ensure backup directory exists
    if (!fs.existsSync(TEST_CONFIG.backupDir)) {
      fs.mkdirSync(TEST_CONFIG.backupDir, { recursive: true });
    }

    console.log('📋 Test Configuration:');
    console.log(`Base URL: ${TEST_CONFIG.baseUrl}`);
    console.log(`Backup Directory: ${TEST_CONFIG.backupDir}`);
    console.log('');

    // Run all test suites
    await runTestSuite('Backup API Endpoints');
    await runTestSuite('Backup File Validation');
    await runTestSuite('Database Restore Tests');
    await runTestSuite('Cleanup Tests');

    console.log('');
    console.log('🎉 All backup and restore tests completed successfully!');
    console.log('=====================================');

  } catch (error) {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  }
}

// Simple test runner implementation
async function runTestSuite(suiteName) {
  console.log(`\n📦 Running ${suiteName}...`);
  console.log('-'.repeat(50));
}

// Mock expect function for basic assertions
function expect(actual) {
  return {
    toBe: (expected) => {
      if (actual !== expected) {
        throw new Error(`Expected ${expected}, but got ${actual}`);
      }
    },
    toBeGreaterThan: (expected) => {
      if (actual <= expected) {
        throw new Error(`Expected ${actual} to be greater than ${expected}`);
      }
    },
    toContain: (expected) => {
      if (!actual.includes(expected)) {
        throw new Error(`Expected "${actual}" to contain "${expected}"`);
      }
    },
    toBeDefined: () => {
      if (actual === undefined) {
        throw new Error('Expected value to be defined');
      }
    }
  };
}

// Export for use in other test files
module.exports = {
  runBackupTests,
  TEST_CONFIG,
  authenticateAdmin,
  makeAuthenticatedRequest
};

// Run tests if this file is executed directly
if (require.main === module) {
  runBackupTests();
}
